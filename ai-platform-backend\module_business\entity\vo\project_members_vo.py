from datetime import datetime
from pydantic import BaseModel, ConfigDict, Field
from pydantic.alias_generators import to_camel
from pydantic_validation_decorator import NotBlank
from typing import Optional, Literal
from module_admin.annotation.pydantic_annotation import as_query


class ProjectMembersModel(BaseModel):
    """
    项目成员关联表对应pydantic模型
    """
    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True)

    project_id: Optional[int] = Field(default=None, description='项目ID')
    user_id: Optional[int] = Field(default=None, description='用户ID')
    role_type: Optional[Literal['project_manager', 'project_member']] = Field(default=None, description='成员角色类型：project_manager(项目管理员), project_member(项目成员)')
    assigned_at: Optional[datetime] = Field(default=None, description='分配时间')
    assigned_by: Optional[int] = Field(default=None, description='分配人ID')
    is_deleted: Optional[int] = Field(default=0, description='删除标志（0代表存在 1代表删除）')
    
    # 关联字段
    user_name: Optional[str] = Field(default=None, description='用户名')
    nick_name: Optional[str] = Field(default=None, description='用户昵称')
    project_name: Optional[str] = Field(default=None, description='项目名称')

    @NotBlank(field_name='project_id', message='项目ID不能为空')
    def get_project_id(self):
        return self.project_id

    @NotBlank(field_name='user_id', message='用户ID不能为空')
    def get_user_id(self):
        return self.user_id

    @NotBlank(field_name='role_type', message='成员角色类型不能为空')
    def get_role_type(self):
        return self.role_type

    def validate_fields(self):
        self.get_project_id()
        self.get_user_id()
        self.get_role_type()


class ProjectMembersQueryModel(ProjectMembersModel):
    """
    项目成员关联不分页查询模型
    """
    pass


@as_query
class ProjectMembersPageQueryModel(ProjectMembersQueryModel):
    """
    项目成员关联分页查询模型
    """

    page_num: int = Field(default=1, description='当前页码')
    page_size: int = Field(default=10, description='每页记录数')
    order_by_column: Optional[str] = Field(default=None, description='排序字段')
    is_asc: Optional[bool] = Field(default=True, description='是否升序')


class DeleteProjectMembersModel(BaseModel):
    """
    删除项目成员模型
    """

    model_config = ConfigDict(alias_generator=to_camel)

    project_ids: str = Field(description='需要删除的项目ID')
    user_ids: str = Field(description='需要删除的用户ID')


class AddProjectMemberModel(BaseModel):
    """
    添加项目成员模型
    """
    model_config = ConfigDict(alias_generator=to_camel)

    project_id: int = Field(description='项目ID')
    user_id: int = Field(description='用户ID')
    role_type: Literal['project_manager', 'project_member'] = Field(description='成员角色类型')


class BatchAddProjectMembersModel(BaseModel):
    """
    批量添加项目成员模型
    """
    model_config = ConfigDict(alias_generator=to_camel)

    project_id: int = Field(description='项目ID')
    user_ids: list[int] = Field(description='用户ID列表')
    role_type: Literal['project_manager', 'project_member'] = Field(description='成员角色类型')


class UpdateProjectMemberRoleModel(BaseModel):
    """
    更新项目成员角色模型
    """
    model_config = ConfigDict(alias_generator=to_camel)

    project_id: int = Field(description='项目ID')
    user_id: int = Field(description='用户ID')
    role_type: Literal['project_manager', 'project_member'] = Field(description='新的成员角色类型')
