"""
项目成员管理测试
"""
import pytest
from datetime import datetime
from sqlalchemy.ext.asyncio import AsyncSession
from fastapi.testclient import TestClient
from unittest.mock import AsyncMock, patch

from module_business.service.project_members_service import ProjectMembersService
from module_business.entity.vo.project_members_vo import (
    ProjectMembersModel,
    AddProjectMemberModel,
    BatchAddProjectMembersModel,
    UpdateProjectMemberRoleModel,
)
from module_admin.entity.vo.common_vo import CrudResponseModel
from exceptions.exception import ServiceException


class TestProjectMembersService:
    """项目成员服务测试类"""

    @pytest.fixture
    def mock_db(self):
        """模拟数据库会话"""
        return AsyncMock(spec=AsyncSession)

    @pytest.fixture
    def sample_project_member(self):
        """示例项目成员数据"""
        return ProjectMembersModel(
            project_id=1,
            user_id=2,
            role_type='project_member',
            assigned_at=datetime.now(),
            assigned_by=1,
            is_deleted=0
        )

    @pytest.fixture
    def add_member_model(self):
        """添加成员模型"""
        return AddProjectMemberModel(
            project_id=1,
            user_id=2,
            role_type='project_member'
        )

    @pytest.fixture
    def batch_add_model(self):
        """批量添加成员模型"""
        return BatchAddProjectMembersModel(
            project_id=1,
            user_ids=[2, 3, 4],
            role_type='project_member'
        )

    @pytest.fixture
    def update_role_model(self):
        """更新角色模型"""
        return UpdateProjectMemberRoleModel(
            project_id=1,
            user_id=2,
            role_type='project_manager'
        )

    @pytest.mark.asyncio
    async def test_add_project_member_success(self, mock_db, add_member_model):
        """测试成功添加项目成员"""
        with patch('module_business.dao.projects_dao.ProjectsDao.get_projects_detail_by_info') as mock_project, \
             patch('module_admin.dao.user_dao.UserDao.get_user_detail_by_info') as mock_user, \
             patch('module_business.dao.project_members_dao.ProjectMembersDao.get_project_members_detail_by_info') as mock_existing, \
             patch('module_business.dao.project_members_dao.ProjectMembersDao.add_project_members_dao') as mock_add:
            
            # 设置模拟返回值
            mock_project.return_value = AsyncMock()  # 项目存在
            mock_user.return_value = AsyncMock()  # 用户存在
            mock_existing.return_value = None  # 不是现有成员
            mock_add.return_value = AsyncMock()
            
            # 执行测试
            result = await ProjectMembersService.add_project_member_services(
                mock_db, add_member_model, 1
            )
            
            # 验证结果
            assert isinstance(result, CrudResponseModel)
            assert result.is_success is True
            assert result.message == '添加成功'

    @pytest.mark.asyncio
    async def test_add_project_member_project_not_exists(self, mock_db, add_member_model):
        """测试添加成员时项目不存在"""
        with patch('module_business.dao.projects_dao.ProjectsDao.get_projects_detail_by_info') as mock_project:
            mock_project.return_value = None  # 项目不存在
            
            # 执行测试并验证异常
            with pytest.raises(ServiceException) as exc_info:
                await ProjectMembersService.add_project_member_services(
                    mock_db, add_member_model, 1
                )
            
            assert str(exc_info.value) == '项目不存在'

    @pytest.mark.asyncio
    async def test_add_project_member_user_not_exists(self, mock_db, add_member_model):
        """测试添加成员时用户不存在"""
        with patch('module_business.dao.projects_dao.ProjectsDao.get_projects_detail_by_info') as mock_project, \
             patch('module_admin.dao.user_dao.UserDao.get_user_detail_by_info') as mock_user:
            
            mock_project.return_value = AsyncMock()  # 项目存在
            mock_user.return_value = None  # 用户不存在
            
            # 执行测试并验证异常
            with pytest.raises(ServiceException) as exc_info:
                await ProjectMembersService.add_project_member_services(
                    mock_db, add_member_model, 1
                )
            
            assert str(exc_info.value) == '用户不存在'

    @pytest.mark.asyncio
    async def test_add_project_member_already_exists(self, mock_db, add_member_model):
        """测试添加已存在的项目成员"""
        with patch('module_business.dao.projects_dao.ProjectsDao.get_projects_detail_by_info') as mock_project, \
             patch('module_admin.dao.user_dao.UserDao.get_user_detail_by_info') as mock_user, \
             patch('module_business.dao.project_members_dao.ProjectMembersDao.get_project_members_detail_by_info') as mock_existing:
            
            mock_project.return_value = AsyncMock()  # 项目存在
            mock_user.return_value = AsyncMock()  # 用户存在
            mock_existing.return_value = AsyncMock()  # 已是成员
            
            # 执行测试并验证异常
            with pytest.raises(ServiceException) as exc_info:
                await ProjectMembersService.add_project_member_services(
                    mock_db, add_member_model, 1
                )
            
            assert str(exc_info.value) == '用户已经是该项目的成员'

    @pytest.mark.asyncio
    async def test_batch_add_project_members_success(self, mock_db, batch_add_model):
        """测试批量添加项目成员成功"""
        with patch('module_business.dao.projects_dao.ProjectsDao.get_projects_detail_by_info') as mock_project, \
             patch('module_admin.dao.user_dao.UserDao.get_user_detail_by_info') as mock_user, \
             patch('module_business.dao.project_members_dao.ProjectMembersDao.get_project_members_detail_by_info') as mock_existing, \
             patch('module_business.dao.project_members_dao.ProjectMembersDao.add_project_members_dao') as mock_add:
            
            # 设置模拟返回值
            mock_project.return_value = AsyncMock()  # 项目存在
            mock_user.return_value = AsyncMock()  # 用户存在
            mock_existing.return_value = None  # 不是现有成员
            mock_add.return_value = AsyncMock()
            
            # 执行测试
            result = await ProjectMembersService.batch_add_project_members_services(
                mock_db, batch_add_model, 1
            )
            
            # 验证结果
            assert isinstance(result, CrudResponseModel)
            assert result.is_success is True
            assert '成功添加 3 个成员' in result.message

    @pytest.mark.asyncio
    async def test_update_project_member_role_success(self, mock_db, update_role_model):
        """测试成功更新项目成员角色"""
        with patch('module_business.dao.project_members_dao.ProjectMembersDao.get_project_members_detail_by_info') as mock_existing, \
             patch('module_business.dao.project_members_dao.ProjectMembersDao.edit_project_members_dao') as mock_edit:
            
            mock_existing.return_value = AsyncMock()  # 成员存在
            mock_edit.return_value = None
            
            # 执行测试
            result = await ProjectMembersService.update_project_member_role_services(
                mock_db, update_role_model
            )
            
            # 验证结果
            assert isinstance(result, CrudResponseModel)
            assert result.is_success is True
            assert result.message == '更新成功'

    @pytest.mark.asyncio
    async def test_update_project_member_role_not_exists(self, mock_db, update_role_model):
        """测试更新不存在的项目成员角色"""
        with patch('module_business.dao.project_members_dao.ProjectMembersDao.get_project_members_detail_by_info') as mock_existing:
            mock_existing.return_value = None  # 成员不存在
            
            # 执行测试并验证异常
            with pytest.raises(ServiceException) as exc_info:
                await ProjectMembersService.update_project_member_role_services(
                    mock_db, update_role_model
                )
            
            assert str(exc_info.value) == '项目成员不存在'

    @pytest.mark.asyncio
    async def test_remove_project_member_success(self, mock_db):
        """测试成功移除项目成员"""
        with patch('module_business.dao.project_members_dao.ProjectMembersDao.get_project_members_detail_by_info') as mock_existing, \
             patch('module_business.dao.project_members_dao.ProjectMembersDao.delete_project_members_dao') as mock_delete:
            
            mock_existing.return_value = AsyncMock()  # 成员存在
            mock_delete.return_value = None
            
            # 执行测试
            result = await ProjectMembersService.remove_project_member_services(
                mock_db, 1, 2
            )
            
            # 验证结果
            assert isinstance(result, CrudResponseModel)
            assert result.is_success is True
            assert result.message == '移除成功'

    @pytest.mark.asyncio
    async def test_remove_project_member_not_exists(self, mock_db):
        """测试移除不存在的项目成员"""
        with patch('module_business.dao.project_members_dao.ProjectMembersDao.get_project_members_detail_by_info') as mock_existing:
            mock_existing.return_value = None  # 成员不存在
            
            # 执行测试并验证异常
            with pytest.raises(ServiceException) as exc_info:
                await ProjectMembersService.remove_project_member_services(
                    mock_db, 1, 2
                )
            
            assert str(exc_info.value) == '项目成员不存在'

    @pytest.mark.asyncio
    async def test_get_user_projects(self, mock_db):
        """测试获取用户项目列表"""
        with patch('module_business.dao.project_members_dao.ProjectMembersDao.get_user_projects') as mock_get:
            mock_projects = [
                AsyncMock(project_id=1, project_name='项目1', role_type='project_manager'),
                AsyncMock(project_id=2, project_name='项目2', role_type='project_member'),
            ]
            mock_get.return_value = mock_projects
            
            # 执行测试
            result = await ProjectMembersService.get_user_projects_services(mock_db, 1)
            
            # 验证结果
            assert result == mock_projects
            mock_get.assert_called_once_with(mock_db, 1)

    @pytest.mark.asyncio
    async def test_check_user_project_permission(self, mock_db):
        """测试检查用户项目权限"""
        with patch('module_business.dao.project_members_dao.ProjectMembersDao.check_user_project_permission') as mock_check:
            mock_check.return_value = True
            
            # 执行测试
            result = await ProjectMembersService.check_user_project_permission_services(
                mock_db, 1, 1, 'project_manager'
            )
            
            # 验证结果
            assert result is True
            mock_check.assert_called_once_with(mock_db, 1, 1, 'project_manager')
