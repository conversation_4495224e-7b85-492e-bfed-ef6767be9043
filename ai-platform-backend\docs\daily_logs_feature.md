# 按天分割日志功能说明

## 功能概述

系统已实现按天分割日志文件的功能，提供简洁高效的日志管理方案。

## 功能特性

### 1. 按天分割日志
- **全量日志**：`YYYY-MM-DD_all.log` - 包含所有级别的日志
- **错误日志**：`YYYY-MM-DD_error.log` - 仅包含ERROR级别的日志
- **自动轮转**：每天午夜自动创建新的日志文件

### 2. 日志管理
- **自动压缩**：7天前的日志文件自动压缩为 `.gz` 格式
- **自动清理**：30天前的日志文件自动删除
- **文件管理**：提供日志文件查询、统计、手动清理等功能

### 3. 定时任务
- **每天凌晨2点**：自动清理30天前的日志文件
- **每天凌晨3点**：自动压缩7天前的日志文件

## 文件结构

```
logs/
├── 2025-07-25_all.log      # 今天的全量日志
├── 2025-07-25_error.log    # 今天的错误日志
├── 2025-07-24_all.log      # 昨天的全量日志
├── 2025-07-24_error.log    # 昨天的错误日志
├── 2025-07-23_all.log.gz   # 压缩的历史日志
└── ...
```

## 日志格式

```
2025-07-25 15:25:56.932 | trace_id | INFO | module:function:line - 日志消息
```

格式说明：
- **时间戳**：精确到毫秒
- **trace_id**：请求追踪ID
- **日志级别**：DEBUG/INFO/WARNING/ERROR
- **位置信息**：模块名:函数名:行号
- **日志消息**：具体的日志内容

## API接口

系统提供了完整的日志管理API接口：

### 查询日志文件列表
```
GET /system/log-management/files
```

### 读取日志文件内容
```
GET /system/log-management/content?file_path=xxx&lines=100
```

### 获取日志统计信息
```
GET /system/log-management/stats
```

### 清理旧日志
```
POST /system/log-management/clean?days_to_keep=30
```

### 压缩日志文件
```
POST /system/log-management/compress?days_old=7
```

## 配置说明

### 日志级别
- **DEV环境**：DEBUG及以上级别
- **SIT/PROD环境**：INFO及以上级别

### 存储配置
- **日志目录**：`logs/`
- **轮转时间**：每天午夜00:00
- **保留期限**：30天
- **压缩时间**：7天后自动压缩
- **编码格式**：UTF-8

## 使用示例

### 1. 查看今天的日志
```bash
# 查看全量日志
tail -f logs/2025-07-25_all.log

# 查看错误日志
tail -f logs/2025-07-25_error.log
```

### 2. 程序中使用日志
```python
from utils.log_util import logger

# 记录不同级别的日志
logger.info("这是一条信息日志")
logger.warning("这是一条警告日志")
logger.error("这是一条错误日志")
logger.debug("这是一条调试日志")
```

### 3. 日志管理
```python
from utils.log_manager import get_log_manager

# 获取日志管理器
log_manager = get_log_manager()

# 获取日志文件列表
files = log_manager.get_log_files()

# 获取统计信息
stats = log_manager.get_log_stats()

# 手动清理旧日志
deleted_count = log_manager.clean_old_logs(days_to_keep=30)

# 手动压缩日志
compressed_count = log_manager.compress_logs(days_old=7)
```

## 注意事项

1. **磁盘空间**：请定期监控日志目录的磁盘使用情况
2. **权限设置**：确保应用有读写日志目录的权限
3. **时区设置**：日志时间基于系统时区
4. **并发安全**：日志写入是线程安全的
5. **性能影响**：日志写入采用异步队列，对性能影响最小

## 故障排除

### 日志文件未生成
1. 检查日志目录权限
2. 检查磁盘空间
3. 查看应用启动日志

### 日志轮转失败
1. 检查系统时间设置
2. 检查文件权限
3. 重启应用服务

### 自动清理失败
1. 检查定时任务状态
2. 查看错误日志
3. 手动执行清理命令

## 技术实现

- **日志库**：loguru
- **调度器**：APScheduler
- **压缩格式**：gzip
- **文件管理**：pathlib
- **异步处理**：asyncio

该功能实现简洁高效，维护方便，满足系统日志统一收集和管理的需求。
