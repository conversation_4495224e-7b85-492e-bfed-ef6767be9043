from sqlalchemy.ext.asyncio import AsyncSession
from typing import List
from config.constant import CommonConstant
from exceptions.exception import ServiceException
from module_admin.entity.vo.common_vo import CrudResponseModel
from module_business.dao.files_dao import FilesDao
from module_business.entity.vo.files_vo import FilesModel, FilesPageQueryModel
from utils.common_util import CamelCaseUtil
from utils.excel_util import ExcelUtil
from utils.log_util import logger
from utils.minio_util import MinioUtil


class FilesService:
    """
    数据库文件存储模块服务层
    """

    @classmethod
    async def get_files_list_services(
        cls, query_db: AsyncSession, query_object: FilesPageQueryModel, is_page: bool = False
    ):
        """
        获取数据库文件存储列表信息service

        :param query_db: orm对象
        :param query_object: 查询参数对象
        :param is_page: 是否开启分页
        :return: 数据库文件存储列表信息对象
        """
        files_list_result = await FilesDao.get_files_list(query_db, query_object, is_page)

        return files_list_result

    @classmethod
    async def get_files_by_task_id(cls, query_db: AsyncSession, task_id: int):
        """
        根据任务ID获取文件列表

        :param query_db: orm对象
        :param task_id: 任务ID
        :return: 文件列表
        """
        try:
            files_result = await FilesDao.get_files_by_task_id(query_db, task_id)
            return files_result
        except Exception as e:
            logger.error(f"获取任务文件列表失败: {str(e)}")
            raise ServiceException(message=f"获取任务文件列表失败: {str(e)}")

    @classmethod
    async def add_files_services(cls, query_db: AsyncSession, page_object: FilesModel):
        """
        新增数据库文件存储信息service

        :param query_db: orm对象
        :param page_object: 新增数据库文件存储对象
        :return: 新增数据库文件存储校验结果
        """
        try:
            await FilesDao.add_files_dao(query_db, page_object)
            await query_db.commit()
            return CrudResponseModel(is_success=True, message='新增成功')
        except Exception as e:
            await query_db.rollback()
            raise e

    @classmethod
    async def edit_files_services(cls, query_db: AsyncSession, page_object: FilesModel):
        """
        编辑数据库文件存储信息service（支持逻辑删除）

        :param query_db: orm对象
        :param page_object: 编辑数据库文件存储对象
        :return: 编辑数据库文件存储校验结果
        """
        files_info = await cls.files_detail_services(query_db, page_object.file_id)
        if not files_info.file_id:
            raise ServiceException(message='数据库文件存储不存在')
        
        try:
            # 如果是逻辑删除操作
            # if page_object.is_deleted == 1:
            #     # 从MinIO删除文件
            #     minio_util = MinioUtil.get_instance()
            #     await minio_util.delete_file_by_id(page_object.file_id, query_db)
            #     logger.info(f'逻辑删除文件{page_object.file_id}，已从MinIO删除物理文件')
            
            # 更新数据库记录
            edit_files = page_object.model_dump(exclude_unset=True, exclude={})
            await FilesDao.edit_files_dao(query_db, edit_files)
            await query_db.commit()
            
            if page_object.is_deleted == 1:
                return CrudResponseModel(is_success=True, message='文件删除成功')
            else:
                return CrudResponseModel(is_success=True, message='更新成功')
                
        except Exception as e:
            await query_db.rollback()
            raise e

    @classmethod
    async def files_detail_services(cls, query_db: AsyncSession, file_id: int):
        """
        获取数据库文件存储详细信息service

        :param query_db: orm对象
        :param file_id: 文件id
        :return: 文件id对应的信息
        """
        files = await FilesDao.get_files_detail_by_id(query_db, file_id=file_id)
        if files:
            result = FilesModel(**CamelCaseUtil.transform_result(files))
        else:
            result = FilesModel(**dict())

        return result

    @staticmethod
    async def export_files_list_services(files_list: List):
        """
        导出数据库文件存储信息service

        :param files_list: 数据库文件存储信息列表
        :return: 数据库文件存储信息对应excel的二进制数据
        """
        # 创建一个映射字典，将英文键映射到中文键
        mapping_dict = {
            'fileId': '文件id',
            'kbId': '关联数据库id',
            'projectId': '冗余存储便于查询',
            'fileType': '文件类型',
            'originalName': '源文件名称',
            'storagePath': 'OSS存储路径',
            'fileSize': '文件带下',
            'fileFormat': '文件扩展名',
            'sourceTaskId': '关联产生此文件的任务',
            'fileMetadata': '包含模型参数/仿真条件等',
            'createdAt': '创建时间',
            'isDeleted': '是否删除',
        }
        binary_data = ExcelUtil.export_list2excel(files_list, mapping_dict)

        return binary_data

    @classmethod
    async def get_file_stats_by_kb_services(cls, query_db: AsyncSession, kb_id: int):
        """
        根据数据库ID获取文件类型统计service

        :param query_db: orm对象
        :param kb_id: 数据库ID
        :return: 各类型文件数量统计
        """
        file_stats = await FilesDao.get_file_stats_by_kb(query_db, kb_id)
        return file_stats

    @classmethod
    async def get_files_by_kb_and_type_services(cls, query_db: AsyncSession, kb_id: int, file_type: str = None):
        """
        根据数据库ID和文件类型获取文件列表service

        :param query_db: orm对象
        :param kb_id: 数据库ID
        :param file_type: 文件类型（可选，不传则获取所有类型）
        :return: 文件列表
        """
        files = await FilesDao.get_files_by_kb_and_type(query_db, kb_id, file_type)
        result = []
        for file in files:
            result.append(FilesModel(**CamelCaseUtil.transform_result(file)))
        return result

    @classmethod
    async def get_files_grouped_by_task_services(cls, query_db: AsyncSession, kb_id: int, file_type: str = None):
        """
        根据数据库ID和文件类型获取按task_id分组的文件列表service

        :param query_db: orm对象
        :param kb_id: 数据库ID
        :param file_type: 文件类型（可选，不传则获取所有类型）
        :return: 按task_id分组的文件列表
        """
        grouped_files = await FilesDao.get_files_grouped_by_task(query_db, kb_id, file_type)

        # 转换为前端需要的格式
        result = {}
        for task_id, files in grouped_files.items():
            result[task_id] = []
            for file in files:
                result[task_id].append(FilesModel(**CamelCaseUtil.transform_result(file)))

        return result
