#!/usr/bin/env python3
"""
权限系统测试脚本
用于验证权限管理系统是否正常工作
"""
import asyncio
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from config.role_constants import RoleConstants
from utils.permission_util import PermissionUtil


def test_role_constants():
    """测试角色常量定义"""
    print("🔍 测试角色常量定义...")
    
    # 测试角色检查
    test_cases = [
        (['super_admin'], 'super_admin', True),
        (['project_manager'], 'project_manager', True),
        (['project_member'], 'project_member', True),
        (['common_user'], 'common_user', True),
        (['common_user'], 'super_admin', False),
        (['project_member'], 'project_manager', False),
    ]
    
    for roles, check_role, expected in test_cases:
        if check_role == 'super_admin':
            result = RoleConstants.is_super_admin(roles)
        elif check_role == 'project_manager':
            result = RoleConstants.is_project_manager(roles)
        elif check_role == 'project_member':
            result = RoleConstants.is_project_member(roles)
        elif check_role == 'common_user':
            result = RoleConstants.is_common_user(roles)
        
        status = "✅" if result == expected else "❌"
        print(f"  {status} 角色 {roles} 检查 {check_role}: {result} (期望: {expected})")
    
    # 测试权限级别
    print("\n🔍 测试权限级别...")
    level_tests = [
        (['super_admin'], 1),
        (['project_manager'], 2),
        (['project_member'], 3),
        (['common_user'], 4),
        (['super_admin', 'project_manager'], 1),  # 多角色取最高权限
    ]
    
    for roles, expected_level in level_tests:
        level = RoleConstants.get_highest_role_level(roles)
        status = "✅" if level == expected_level else "❌"
        print(f"  {status} 角色 {roles} 权限级别: {level} (期望: {expected_level})")


def test_permission_util():
    """测试权限验证工具"""
    print("\n🔍 测试权限验证工具...")
    
    # 测试菜单权限
    menu_tests = [
        (['super_admin'], '/dashboard', True),
        (['super_admin'], '/system', True),
        (['project_manager'], '/projects', True),
        (['project_manager'], '/database', True),
        (['project_manager'], '/system', False),
        (['project_member'], '/tasks', True),
        (['project_member'], '/projects', False),
        (['common_user'], '/dashboard', True),
        (['common_user'], '/tools', True),
        (['common_user'], '/tasks', False),
    ]
    
    for roles, menu_path, expected in menu_tests:
        result = PermissionUtil.check_menu_permission(roles, menu_path)
        status = "✅" if result == expected else "❌"
        print(f"  {status} 角色 {roles} 访问菜单 {menu_path}: {result} (期望: {expected})")
    
    # 测试接口权限
    interface_tests = [
        (['super_admin'], 'any:permission', True),
        (['project_manager'], 'business:projects:list', True),
        (['project_manager'], 'business:tasks:edit', True),
        (['project_manager'], 'system:user:add', False),
        (['project_member'], 'business:tasks:list', True),
        (['project_member'], 'business:projects:edit', False),
        (['common_user'], 'dashboard:view', True),
        (['common_user'], 'business:tasks:list', False),
    ]
    
    for roles, interface_code, expected in interface_tests:
        result = PermissionUtil.check_interface_permission(roles, interface_code)
        status = "✅" if result == expected else "❌"
        print(f"  {status} 角色 {roles} 访问接口 {interface_code}: {result} (期望: {expected})")


def test_role_hierarchy():
    """测试角色层次结构"""
    print("\n🔍 测试角色层次结构...")
    
    # 测试权限继承
    hierarchy_tests = [
        ('super_admin', 'project_manager', True),  # 超级管理员权限 > 项目管理员
        ('project_manager', 'project_member', True),  # 项目管理员权限 > 项目成员
        ('project_member', 'common_user', True),  # 项目成员权限 > 普通用户
        ('project_member', 'project_manager', False),  # 项目成员权限 < 项目管理员
    ]
    
    for higher_role, lower_role, expected in hierarchy_tests:
        higher_level = RoleConstants.get_highest_role_level([higher_role])
        lower_level = RoleConstants.get_highest_role_level([lower_role])
        result = higher_level < lower_level  # 级别数字越小权限越高
        
        status = "✅" if result == expected else "❌"
        print(f"  {status} {higher_role}({higher_level}) > {lower_role}({lower_level}): {result} (期望: {expected})")


def test_project_management_permissions():
    """测试项目管理权限"""
    print("\n🔍 测试项目管理权限...")
    
    project_tests = [
        (['super_admin'], True),
        (['project_manager'], True),
        (['project_member'], False),
        (['common_user'], False),
    ]
    
    for roles, expected in project_tests:
        result = RoleConstants.can_manage_projects(roles)
        status = "✅" if result == expected else "❌"
        print(f"  {status} 角色 {roles} 可以管理项目: {result} (期望: {expected})")


def test_database_access_permissions():
    """测试数据库访问权限"""
    print("\n🔍 测试数据库访问权限...")
    
    database_tests = [
        (['super_admin'], True),
        (['project_manager'], True),
        (['project_member'], False),
        (['common_user'], False),
    ]
    
    for roles, expected in database_tests:
        result = RoleConstants.can_access_database(roles)
        status = "✅" if result == expected else "❌"
        print(f"  {status} 角色 {roles} 可以访问数据库: {result} (期望: {expected})")


def main():
    """主测试函数"""
    print("🚀 开始权限系统测试...")
    print("=" * 60)
    
    try:
        # 运行各项测试
        test_role_constants()
        test_permission_util()
        test_role_hierarchy()
        test_project_management_permissions()
        test_database_access_permissions()
        
        print("\n" + "=" * 60)
        print("🎉 权限系统测试完成！")
        
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True


if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
