from datetime import datetime
from pydantic import BaseModel, ConfigDict, Field
from pydantic.alias_generators import to_camel
from pydantic_validation_decorator import NotBlank
from typing import Optional
from module_admin.annotation.pydantic_annotation import as_query




class KbTypesRelModel(BaseModel):
    """
    数据库-类型关联关系表对应pydantic模型
    """
    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True)

    kb_id: Optional[int] = Field(default=None, description='数据库id')
    type_id: Optional[int] = Field(default=None, description='类型id')
    assigned_at: Optional[datetime] = Field(default=None, description='创建时间')
    assigned_by: Optional[int] = Field(default=None, description='创建者')

    @NotBlank(field_name='assigned_by', message='创建者不能为空')
    def get_assigned_by(self):
        return self.assigned_by

    def validate_fields(self):
        self.get_assigned_by()




class KbTypesRelQueryModel(KbTypesRelModel):
    """
    数据库-类型关联关系不分页查询模型
    """
    pass


@as_query
class KbTypesRelPageQueryModel(KbTypesRelQueryModel):
    """
    数据库-类型关联关系分页查询模型
    """

    page_num: int = Field(default=1, description='当前页码')
    page_size: int = Field(default=10, description='每页记录数')


class DeleteKbTypesRelModel(BaseModel):
    """
    删除数据库-类型关联关系模型
    """

    model_config = ConfigDict(alias_generator=to_camel)

    kb_ids: str = Field(description='需要删除的数据库id')
