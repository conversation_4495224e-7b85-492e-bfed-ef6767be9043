import SparkMD5 from 'spark-md5'

/**
 * 计算文件的MD5值
 * @param {File} file - 文件对象
 * @returns {Promise<string>} MD5哈希值
 */
export function calculateFileMD5(file) {
  return new Promise((resolve, reject) => {
    const spark = new SparkMD5.ArrayBuffer()
    const fileReader = new FileReader()
    const chunkSize = 2097152 // 2MB chunks
    let currentChunk = 0
    const chunks = Math.ceil(file.size / chunkSize)

    fileReader.onload = function(e) {
      spark.append(e.target.result)
      currentChunk++

      if (currentChunk < chunks) {
        loadNext()
      } else {
        const md5 = spark.end()
        resolve(md5)
      }
    }

    fileReader.onerror = function() {
      reject(new Error('文件读取失败'))
    }

    function loadNext() {
      const start = currentChunk * chunkSize
      const end = ((start + chunkSize) >= file.size) ? file.size : start + chunkSize
      fileReader.readAsArrayBuffer(file.slice(start, end))
    }

    loadNext()
  })
}

/**
 * 批量计算多个文件的MD5值
 * @param {File[]} files - 文件数组
 * @returns {Promise<Object>} 文件名到MD5的映射对象
 */
export async function calculateMultipleFilesMD5(files) {
  const results = {}
  
  for (const file of files) {
    try {
      const md5 = await calculateFileMD5(file)
      results[file.name] = md5
    } catch (error) {
      console.error(`计算文件 ${file.name} 的MD5失败:`, error)
      results[file.name] = null
    }
  }
  
  return results
}
