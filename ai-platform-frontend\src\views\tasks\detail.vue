<template>
  <div class="task-detail-wrapper">
    <div class="app-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <h2>{{ getPageTitle() }}</h2>
      <el-button @click="goBack" icon="ArrowLeft">返回</el-button>
    </div>

    <!-- 加载状态 -->
    <el-card v-loading="loading" element-loading-text="正在加载任务详情..." class="detail-card">
      <template #header>
        <div class="card-header">
          <span class="card-title">{{ taskDetail.task_name || taskDetail.taskName || '任务详情' }}</span>
          <el-tag :type="getStatusType(taskDetail.status)">
            {{ getStatusText(taskDetail.status) }}
          </el-tag>
        </div>
      </template>

      <!-- 任务进度 -->
      <div class="progress-section">
        <div class="progress-header">
          <h3>任务进度</h3>
          <div class="progress-info">
            <span class="progress-text">{{ taskDetail.progress || 0 }}%</span>
            <el-tag 
              v-if="taskDetail.status === 'running' || taskDetail.task_status === 'running'"
              :type="progressUpdateSource === 'websocket' ? 'success' : 'info'"
              size="small"
              class="progress-source-tag"
            >
              {{ progressUpdateSource === 'websocket' ? '实时更新' : '定时刷新' }}
            </el-tag>
          </div>
        </div>
        <el-progress 
          :percentage="taskDetail.progress || 0" 
          :status="getProgressStatus(taskDetail.status)"
          :stroke-width="20"
          class="progress-bar"
        />
        <!-- 开发环境调试信息 -->
        <div v-if="isDevelopment" class="debug-info">
          <small style="color: #909399;">
            进度来源: {{ progressUpdateSource }} | 
            最后WebSocket更新: {{ lastWebSocketProgressUpdate ? new Date(lastWebSocketProgressUpdate).toLocaleTimeString() : '无' }}
          </small>
        </div>
      </div>

      <!-- 时间信息 -->
      <div class="time-section">
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="time-item">
              <label>开始时间：</label>
              <span>{{ formatTime(taskDetail.start_time || taskDetail.created_at) }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="time-item">
              <label>预计完成：</label>
              <span>{{ formatTime(taskDetail.estimated_completion) }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="time-item">
              <label>完成时间：</label>
              <span>{{ formatTime(taskDetail.completed_at) }}</span>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 任务参数 -->
      <div class="parameters-section">
        <h3>任务参数</h3>
        <el-card class="parameters-card">
          <pre class="parameters-json">{{ formatParameters(taskDetail.parameters || taskDetail.task_parameters) }}</pre>
        </el-card>
      </div>

      <!-- 日志和结果 -->
      <div class="content-section">
        <el-row :gutter="20">
          <!-- 左侧：过程日志 -->
          <el-col :span="12">
            <div class="log-section">
              <div class="log-header">
                <h3>执行日志</h3>
                <div class="log-controls">
                  <el-tag 
                    v-if="taskDetail.status === 'running' || taskDetail.task_status === 'running'"
                    type="success" 
                    size="small"
                  >
                    <el-icon><Connection /></el-icon>
                    实时日志
                  </el-tag>
                  <el-button
                    v-if="taskDetail.status === 'completed' || taskDetail.task_status === 'completed' || taskDetail.status === 'failed' || taskDetail.task_status === 'failed'"
                    link
                    size="small"
                    @click="() => loadLogFiles(true)"
                  >
                    <el-icon><Refresh /></el-icon>
                    {{ logFilesLoaded ? '重新加载日志文件' : '加载日志文件' }}
                  </el-button>
                  <el-button
                    link
                    size="small"
                    @click="clearLogs"
                  >
                    清空日志
                  </el-button>
                </div>
              </div>
              <div ref="logContainer" class="log-container">
                <div v-if="logs.length > 0">
                  <div
                    v-for="(log, index) in logs"
                    :key="index"
                    :class="['log-item', `log-${log.level?.toLowerCase() || 'info'}`]"
                  >
                    <span class="log-time">{{ formatLogTime(log.timestamp) }}</span>
                    <span class="log-level">[{{ log.level || 'INFO' }}]</span>
                    <span class="log-message">{{ log.message }}</span>
                  </div>
                </div>
                <div v-else class="no-logs">
                  <el-icon><Document /></el-icon>
                  <span>暂无日志信息</span>
                </div>
              </div>
            </div>
          </el-col>

          <!-- 右侧：运行结果 -->
          <el-col :span="12">
            <div class="result-section">
              <div class="result-header">
                <h3>运行结果</h3>
                <div class="result-controls">
                  <el-tag 
                    v-if="filesLoading" 
                    type="info" 
                    size="small"
                  >
                    <el-icon><Loading /></el-icon>
                    加载文件内容中...
                  </el-tag>
                  <el-button
                    type="primary"
                    size="small"
                    :icon="Refresh"
                    :loading="refreshing"
                    @click="refreshResults"
                  >
                    刷新结果
                  </el-button>
                </div>
              </div>
              <div class="result-container">
                <!-- 分隔线 -->
                <el-divider v-if="Object.keys(getAllResultsByDirectory()).length > 0">任务结果文件</el-divider>
                
                <!-- 按目录分组显示所有结果 -->
                <div v-if="Object.keys(getAllResultsByDirectory()).length > 0" class="result-files">
                  <div v-for="(files, groupName) in getAllResultsByDirectory()" :key="groupName" class="file-group">
                    <div class="group-header">
                      <h4 class="group-title">{{ groupName }}</h4>
                      <el-tag size="small" type="info">{{ files.length }} 个文件</el-tag>
                    </div>
                    <div class="group-content">
                      <div v-for="file in files" :key="file.file_id" class="file-item">
                        <!-- 3D模型文件展示 -->
                        <div v-if="is3DModelFile(file.file_name)" class="file-preview">
                          <div class="model-viewer-container" :id="`model-viewer-${file.file_id}`">
                            <!-- Model-viewer容器 -->
                            <div 
                              :id="`modelContainer-${file.file_id}`"
                              class="model-container"
                              :style="{ display: modelViewers[file.file_id] ? 'block' : 'none' }"
                            ></div>
                            
                            <!-- 加载覆盖层 -->
                            <div v-if="modelLoading[file.file_id]" class="model-loading">
                              <el-icon class="is-loading"><Loading /></el-icon>
                              <span>加载中...</span>
                            </div>
                            
                            <!-- 加载失败提示 -->
                            <div v-if="!modelViewers[file.file_id] && !modelLoading[file.file_id]" class="model-placeholder">
                              <el-icon><Box /></el-icon>
                              <span>3D模型加载失败</span>
                              <el-button 
                                type="primary" 
                                size="small" 
                                @click="loadModelFile(file)"
                              >
                                重试加载
                              </el-button>
                            </div>
                          </div>
                        </div>

                        <!-- 图片文件展示 -->
                        <div v-else-if="isImageFile(file.file_name)" class="file-preview">
                          <el-image
                            :src="getFileUrl(file.file_path)"
                            :alt="file.file_name"
                            fit="contain"
                            style="max-width: 200px; height: 150px;"
                            :preview-src-list="[getFileUrl(file.file_path)]"
                            preview-teleported
                            @error="handleImageError"
                          >
                            <template #error>
                              <div class="image-error">
                                <el-icon><Picture /></el-icon>
                                <span>图片加载失败</span>
                              </div>
                            </template>
                          </el-image>
                        </div>
                        <!-- 非图片文件展示 -->
                        <div v-else class="file-preview">
                          <!-- 如果是文本文件且已加载内容，直接显示内容 -->
                          <div v-if="isTextFile(file.file_name) && fileContents[file.file_path]" class="file-text-content">
                            <pre>{{ fileContents[file.file_path] }}</pre>
                          </div>
                          <!-- 如果是文本文件但内容未加载，显示加载状态 -->
                          <div v-else-if="isTextFile(file.file_name)" class="file-loading">
                            <el-icon class="is-loading"><Loading /></el-icon>
                            <span>加载文本内容中...</span>
                          </div>
                          <!-- 否则显示文件图标 -->
                          <div v-else class="file-icon">
                            <el-icon><Document /></el-icon>
                            <span>{{ file.file_name }}</span>
                          </div>
                        </div>
                        <div class="file-info">
                          <p class="file-name">{{ file.file_name }}</p>
                          <p class="file-size">{{ formatFileSize(file.file_size) }}</p>
                          <div class="file-actions">
                            <el-button 
                              v-if="is3DModelFile(file.file_name)"
                              type="primary" 
                              size="small" 
                              @click="previewModel(file)"
                            >
                              3D预览
                            </el-button>
                            <el-button 
                              v-else
                              type="primary" 
                              size="small" 
                              @click="previewReport(file.file_path, file.file_name)"
                            >
                              预览
                            </el-button>
                            <el-button
                              type="success"
                              size="small"
                              @click="downloadFile(file.file_path, file.file_name)"
                            >
                              下载
                            </el-button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <!-- 如果没有任何结果，显示提示 -->
                <div v-else class="no-task-results">
                  <el-icon><Document /></el-icon>
                  <span>当前任务暂无结果文件</span>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
    </el-card>
  </div>

  <!-- 报告预览对话框 -->
  <el-dialog
    v-model="previewDialogVisible"
    :title="previewFileName"
    width="80%"
    :before-close="handlePreviewClose"
  >
    <div v-if="previewContent" class="preview-content">
      <div v-if="isPreviewImage" class="preview-image">
        <el-image
          :src="previewContent"
          fit="contain"
          style="max-width: 100%; max-height: 500px;"
          :preview-src-list="[previewContent]"
          preview-teleported
        />
      </div>
      <div v-else-if="isPreviewText" class="preview-text">
        <pre>{{ previewContent }}</pre>
      </div>
      <div v-else class="preview-iframe">
        <iframe
          :src="previewContent"
          width="100%"
          height="500px"
          frameborder="0"
        ></iframe>
      </div>
    </div>
    <div v-else class="preview-loading">
      <el-icon class="is-loading"><Loading /></el-icon>
      <span>正在加载报告内容...</span>
    </div>
  </el-dialog>

  <!-- 3D模型预览对话框 -->
  <el-dialog
    v-model="modelPreviewDialogVisible"
    :title="`3D模型预览 - ${modelPreviewFileName}`"
    width="90%"
    :before-close="handleModelPreviewClose"
  >
    <div class="model-preview-content">
      <div class="model-preview-toolbar">
        <el-button-group>
          <el-button size="small" @click="resetModelView">重置视角</el-button>
          <el-button size="small" @click="fitModelView">适应窗口</el-button>
          <el-button size="small" @click="toggleAutoRotate">
            {{ modelAutoRotate ? '停止旋转' : '自动旋转' }}
          </el-button>
        </el-button-group>
      </div>
      <div class="model-preview-viewer">
        <div 
          ref="modelPreviewContainer"
          id="modelPreviewMainContainer"
          class="model-preview-container"
        ></div>
      </div>
      <div v-if="modelPreviewLoading" class="model-preview-loading">
        <el-icon class="is-loading"><Loading /></el-icon>
        <span>正在加载3D模型...</span>
      </div>
    </div>
  </el-dialog>


  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick, markRaw } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Document, Picture, Refresh, Connection, Loading, Box } from '@element-plus/icons-vue'
import { getTaskDetail, TaskLogWebSocket } from '@/api/tasks'
import ThreeModelViewer from '@/utils/threeModelViewer'

const route = useRoute()
const router = useRouter()

// 开发环境判断
const isDevelopment = import.meta.env.MODE === 'development'

// 响应式数据
const taskDetail = ref({})
const loading = ref(true)
const ws = ref(null)
const logs = ref([])
const logContainer = ref(null)
const refreshing = ref(false)
const autoRefreshInterval = ref(null)
const logFilesLoaded = ref(false) // 标记是否已加载过日志文件
const filesLoading = ref(false) // 标记文件内容是否正在加载

// 进度更新协调机制
const progressUpdateSource = ref('api') // 'api' | 'websocket'
const lastWebSocketProgressUpdate = ref(null) // 记录最后一次WebSocket进度更新时间

// 预览相关
const previewDialogVisible = ref(false)
const previewFileName = ref('')
const previewContent = ref('')
const isPreviewImage = ref(false)
const isPreviewText = ref(false)

// 文件内容缓存
const fileContents = ref({}) // 存储文本文件的内容

// 3D模型相关（Three.js）
const modelViewers = ref({}) // 存储每个文件的3D查看器实例
const modelLoading = ref({}) // 存储每个文件的加载状态
const modelFileCache = ref({}) // 存储每个文件的缓存数据
const modelPreviewDialogVisible = ref(false)
const modelPreviewFileName = ref('')
const modelPreviewLoading = ref(false)
const modelPreviewContainer = ref(null)
const modelAutoRotate = ref(false)
const modelPreviewViewer = ref(null)



// 获取任务最新进度
const fetchLatestProgress = async (taskId) => {
  try {
    // 使用API函数，自动携带认证信息
    const { getTaskProgress } = await import('@/api/tasks')
    const response = await getTaskProgress(taskId)

    if (response.code === 200) {
      const latestProgress = response.data.progress
      const currentProgress = taskDetail.value.progress || 0

      if (latestProgress > currentProgress) {
        console.log(`📊 更新最新进度: ${currentProgress}% -> ${latestProgress}% (来源: ${response.data.source})`)
        taskDetail.value.progress = latestProgress

        // 添加进度日志（使用API返回的消息或默认格式）
        logs.value.push({
          timestamp: new Date().toISOString(),
          level: 'INFO',
          message: response.data.message || `当前进度: ${latestProgress}%`,
          type: 'progress'
        })
      }
    }
  } catch (error) {
    console.warn('获取最新进度失败:', error)
  }
}

// 获取任务详情
const fetchTaskDetail = async (retryCount = 0) => {
  try {
    console.log('🔍 开始获取任务详情，任务ID:', route.params.id, '重试次数:', retryCount)
    loading.value = true
    const taskId = parseInt(route.params.id)
    const res = await getTaskDetail(taskId)
    console.log('🔍 任务详情API响应:', res)
    
    if (res.code === 200) {
      // 创建新对象避免readonly错误
      taskDetail.value = Object.assign({}, res.data)
      console.log('🔍 任务详情数据:', taskDetail.value)
      console.log('🔍 result_files数据:', taskDetail.value.result_files)
      console.log('🔍 result_files长度:', taskDetail.value.result_files?.length)

      // 如果任务正在运行，尝试获取最新进度
      if (taskDetail.value.status === 'running') {
        await fetchLatestProgress(taskId)
      }

      // 检查任务状态
      const taskStatus = res.data.status || res.data.task_status
      
      // 异步加载文件内容（不阻塞界面显示）
      filesLoading.value = true
      Promise.all([
        autoLoadTextFiles(),
        autoLoad3DModels()
      ]).then(() => {
        console.log('✅ 所有文件内容加载完成')
        filesLoading.value = false
      }).catch(error => {
        console.error('❌ 文件内容加载失败:', error)
        filesLoading.value = false
      })
      console.log('🔍 任务状态:', taskStatus)
      
      if (taskStatus === 'running') {
        // 如果任务正在运行，启动WebSocket连接
        console.log('🔍 任务正在运行，启动WebSocket连接')
        startWebSocket(taskId)
        // 重置日志加载标记
        logFilesLoaded.value = false
        // 启动自动刷新
        startAutoRefresh()
        // 显示任务运行中的提示
        ElMessage.info('任务正在执行中，请耐心等待...')
      } else if (taskStatus === 'pending') {
        // 如果任务等待中，启动自动刷新
        console.log('🔍 任务等待中，启动自动刷新')
        startAutoRefresh()
        ElMessage.info('任务等待中，即将开始执行...')
      } else if (taskStatus === 'completed' || taskStatus === 'failed') {
        // 如果任务已结束，只显示执行过程中的日志，不加载日志文件
        console.log('🔍 任务已结束，保留执行过程中的日志')
        // 不启动自动刷新
        console.log('📅 任务已结束，不启动自动刷新')
      }
    } else {
      console.error('🔍 任务详情API返回错误:', res)
      ElMessage.error(res.msg || '获取任务详情失败')
    }
  } catch (error) {
    console.error('🔍 获取任务详情失败:', error)
    
    // 如果是超时错误且重试次数少于3次，则重试
    if ((error.message && error.message.includes('timeout')) || 
        (error.code === 'ECONNABORTED') || 
        (retryCount < 3)) {
      ElMessage.warning(`请求超时，正在重试 (${retryCount + 1}/3)...`)
      setTimeout(() => {
        fetchTaskDetail(retryCount + 1)
      }, 2000) // 2秒后重试
      return
    }
    
    ElMessage.error('获取任务详情失败')
  } finally {
    loading.value = false
  }
}

// 启动WebSocket连接
const startWebSocket = (taskId) => {
  // 检查任务状态，只有运行中的任务才需要WebSocket连接
  const taskStatus = taskDetail.value.status || taskDetail.value.task_status
  console.log(`🔍 尝试启动WebSocket连接，任务ID: ${taskId}, 任务状态: ${taskStatus}`)

  if (taskStatus !== 'running') {
    console.log(`🔍 任务状态不是running，跳过WebSocket连接`)
    return
  }

  // 如果已有连接且连接正常，不重复连接
  if (ws.value && ws.value.ws && ws.value.ws.readyState === WebSocket.OPEN) {
    console.log(`🔍 WebSocket连接已存在且正常，跳过重复连接`)
    return
  }

  // 如果已有连接但状态不正常，先断开
  if (ws.value && ws.value.ws && ws.value.ws.readyState !== WebSocket.OPEN) {
    console.log(`🔍 断开异常的WebSocket连接，状态: ${ws.value.ws.readyState}`)
    try {
      ws.value.disconnect()
    } catch (error) {
      console.warn('断开WebSocket连接时出错:', error)
    }
    ws.value = null
  }

  console.log(`🔍 创建新的WebSocket连接`)
  ws.value = new TaskLogWebSocket(
    taskId,
    handleWebSocketMessage,
    handleWebSocketError,
    handleWebSocketClose
  )

  // 在WebSocket连接建立后获取最新进度
  const originalOnOpen = ws.value.onopen
  ws.value.onopen = async () => {
    if (originalOnOpen) originalOnOpen()
    console.log('🔗 WebSocket连接已建立，获取最新进度')
    await fetchLatestProgress(taskId)
  }

  ws.value.connect()
}

// 处理WebSocket消息
const handleWebSocketMessage = (data) => {
  // 只有任务状态为"运行中"时才接收WebSocket消息
  const taskStatus = taskDetail.value.status || taskDetail.value.task_status
  if (taskStatus !== 'running') {
    return
  }

  // 只显示进度更新消息，过滤重复的普通日志
  if (data.type === 'progress_update') {
    // 进度更新消息 - 主要的进度更新来源，保留后端的详细描述
    logs.value.push({
      timestamp: data.timestamp || new Date().toISOString(),
      level: data.level || 'INFO',
      message: data.message || `进度更新: ${data.progress || 0}%`,
      type: 'progress'
    })

    // 更新任务进度 - WebSocket进度更新的主要入口
    if (data.progress !== undefined && data.progress !== null) {
      console.log('📊 WebSocket进度更新:', data.progress)
      updateProgressFromWebSocket(data.progress)
    }
  } else if (data.type === 'log') {
    // 只显示以"[进度更新]"开头的日志消息，过滤重复消息
    if (data.message && data.message.startsWith('[进度更新]')) {
      logs.value.push(data)
    }

    // 检测任务完成消息，触发自动刷新
    if (data.message && (data.message.includes('任务状态更新: completed') || data.message.includes('任务执行完成'))) {
      console.log('🎉 WebSocket检测到任务完成，触发自动刷新')
      setTimeout(async () => {
        await fetchTaskDetail()
      }, 1000)
    }

    // 忽略其他普通日志消息，避免重复显示
  } else if (data.type === 'kafka_message') {
    // Kafka原始消息（已禁用）
    logs.value.push({
      timestamp: data.timestamp || new Date().toISOString(),
      level: data.level || 'INFO',
      message: `[Kafka已禁用] ${data.message || JSON.stringify(data)}`,
      type: 'kafka_disabled'
    })
  } else if (data.type === 'status_update') {
    // 状态更新消息
    logs.value.push({
      timestamp: data.timestamp || new Date().toISOString(),
      level: data.level || 'INFO',
      message: `[状态更新] ${data.message || JSON.stringify(data)}`,
      type: 'status'
    })
    
    // 状态更新可能包含进度信息
    if (data.progress !== undefined && data.progress !== null) {
      console.log('📊 WebSocket状态更新中的进度:', data.progress)
      updateProgressFromWebSocket(data.progress)
    }
  } else {
    // 其他类型的消息，统一显示
    logs.value.push({
      timestamp: data.timestamp || new Date().toISOString(),
      level: data.level || 'INFO',
      message: `[${data.type || 'UNKNOWN'}] ${data.message || JSON.stringify(data)}`,
      type: data.type || 'unknown'
    })
    
    // 其他消息类型也可能包含进度信息
    if (data.progress !== undefined && data.progress !== null) {
      console.log('📊 WebSocket其他消息中的进度:', data.progress)
      updateProgressFromWebSocket(data.progress)
    }
  }
  
  // 自动滚动到底部
  nextTick(() => {
    if (logContainer.value) {
      logContainer.value.scrollTop = logContainer.value.scrollHeight
    }
  })
}

// 进度保护更新函数
const safeUpdateProgress = (newProgress, source = 'unknown') => {
  const currentProgress = taskDetail.value.progress || 0

  // 进度只能向前推进，不能倒退（除非是WebSocket更新）
  if (source === 'websocket' || newProgress >= currentProgress) {
    if (taskDetail.value && typeof taskDetail.value === 'object') {
      taskDetail.value = {
        ...taskDetail.value,
        progress: newProgress
      }
    }
    console.log(`📊 ${source}进度更新成功: ${currentProgress}% -> ${newProgress}%`)
    return true
  } else {
    console.log(`🔍 ${source}进度更新被拒绝: ${currentProgress}% -> ${newProgress}% (不允许倒退)`)
    return false
  }
}

// WebSocket进度更新函数
const updateProgressFromWebSocket = (progress) => {
  // 设置进度更新来源为WebSocket
  progressUpdateSource.value = 'websocket'
  lastWebSocketProgressUpdate.value = Date.now()

  // 使用安全更新函数
  safeUpdateProgress(progress, 'WebSocket')
}

// 处理WebSocket错误
const handleWebSocketError = (error) => {
  console.error('WebSocket错误:', error)
  ElMessage.warning('日志连接异常，正在重连...')
}

// 处理WebSocket关闭
const handleWebSocketClose = (event) => {
  console.log('🔌 WebSocket连接已关闭，代码:', event?.code)

  // 清理WebSocket引用
  if (ws.value) {
    ws.value = null
  }

  // 如果任务仍在运行且不是正常关闭（1000），则可能需要重连
  const taskStatus = taskDetail.value?.status || taskDetail.value?.task_status
  if (taskStatus === 'running' && event?.code !== 1000) {
    console.log('🔄 任务仍在运行，WebSocket异常关闭，考虑重连')
    // 可以在这里添加重连逻辑，但要避免无限重连
  }
}

// 清空日志
const clearLogs = () => {
  logs.value = []
  logFilesLoaded.value = false // 重置加载标记，允许重新加载
  console.log('📝 日志已清空，可重新加载文件')
}

// 获取日志文件并加载内容
const loadLogFiles = async (forceReload = false) => {
  if (!taskDetail.value.result_files || taskDetail.value.result_files.length === 0) {
    return
  }
  
  // 如果不是强制重新加载且已经加载过，则跳过
  if (!forceReload && logFilesLoaded.value) {
    console.log('📝 日志文件已加载过，跳过重复加载')
    return
  }
  
  // 清空之前的日志内容（保留WebSocket实时日志）
  const websocketLogs = logs.value.filter(log => log.type !== 'file_log' && log.type !== 'separator' && log.type !== 'error' && log.type !== 'info')
  logs.value = [...websocketLogs]
  
  console.log('📝 开始加载日志文件...')
  
  const currentTaskId = parseInt(route.params.id)
  
  // 查找真正的日志文件（排除报告文件）
  const logFiles = taskDetail.value.result_files.filter(file => {
    const lowerFileName = file.file_name.toLowerCase()
    const lowerFilePath = file.file_path.toLowerCase()
    
    // 只识别真正的日志文件，排除报告文件
    const isLogFile = file.file_type === 'log' || 
                     (lowerFileName.includes('log') && !lowerFileName.includes('report')) || 
                     lowerFileName.endsWith('.log') ||
                     (lowerFilePath.includes('log') && !lowerFilePath.includes('report'))
    
    // 排除报告文件
    const isNotReportFile = !lowerFileName.includes('report') && 
                           !lowerFilePath.includes('report') &&
                           file.file_type !== 'report'
    
    const isRelatedToTask = file.source_task_id === currentTaskId
    
    console.log(`📝 检查文件: ${file.file_name} → ${isLogFile && isNotReportFile ? '✅真正的日志文件' : '❌不是日志文件'}`)
    
    return isLogFile && isNotReportFile && isRelatedToTask
  })
  
  // 如果没有找到日志文件，提示用户
  if (logFiles.length === 0) {
    logs.value.push({
      timestamp: new Date().toISOString(),
      level: 'INFO',
      message: '未找到可作为日志显示的文件',
      type: 'info'
    })
    
    // 提示用户可能需要从运行结果中查看报告文件
    logs.value.push({
      timestamp: new Date().toISOString(),
      level: 'INFO',
      message: '如需查看文件内容，请在右侧"运行结果"区域查看',
      type: 'info'
    })
    
    logFilesLoaded.value = true
    return
  }
  
  // 加载日志文件内容
  for (const logFile of logFiles) {
    try {
      // 添加文件开始标记
      logs.value.push({
        timestamp: new Date().toISOString(),
        level: 'INFO',
        message: `=== 开始加载日志文件: ${logFile.file_name} (${formatFileSize(logFile.file_size)}) ===`,
        type: 'separator'
      })
      
      const response = await fetch(`/dev-api/business/files/public/download?path=${encodeURIComponent(logFile.file_path)}`)
      if (response.ok) {
        const logContent = await response.text()
        
        if (!logContent.trim()) {
          logs.value.push({
            timestamp: new Date().toISOString(),
            level: 'WARNING',
            message: '日志文件为空',
            type: 'warning'
          })
          continue
        }
        
        // 将日志内容解析为日志条目
        const logLines = logContent.split('\n')
        
        logLines.forEach((line) => {
          // 跳过空行
          if (!line.trim()) return
          
          // 尝试解析日志格式
          let timestamp = new Date().toISOString()
          let level = 'INFO'
          let message = line
          
          // 简单的日志格式解析
          const timestampMatch = line.match(/(\d{4}-\d{2}-\d{2}[\sT]\d{2}:\d{2}:\d{2}[.\d]*)/);
          if (timestampMatch) {
            timestamp = timestampMatch[1]
          }
          
          const levelMatch = line.match(/\[(DEBUG|INFO|WARNING|ERROR|CRITICAL|WARN|ERR)\]/i);
          if (levelMatch) {
            level = levelMatch[1].toUpperCase()
            if (level === 'WARN') level = 'WARNING'
            if (level === 'ERR') level = 'ERROR'
          }
          
          logs.value.push({
            timestamp: timestamp,
            level: level,
            message: message,
            type: 'file_log',
            source_file: logFile.file_name
          })
        })
        
        // 添加文件结束标记
        logs.value.push({
          timestamp: new Date().toISOString(),
          level: 'INFO',
          message: `=== 结束加载日志文件: ${logFile.file_name} (共 ${logLines.filter(l => l.trim()).length} 行) ===`,
          type: 'separator'
        })
      } else {
        logs.value.push({
          timestamp: new Date().toISOString(),
          level: 'ERROR',
          message: `无法加载日志文件: ${logFile.file_name} (HTTP ${response.status})`,
          type: 'error'
        })
      }
    } catch (error) {
      console.error('加载日志文件失败:', error)
      logs.value.push({
        timestamp: new Date().toISOString(),
        level: 'ERROR',
        message: `加载日志文件失败: ${logFile.file_name} - ${error.message}`,
        type: 'error'
      })
    }
  }
  
  // 设置已加载标记
  logFilesLoaded.value = true
  console.log('📝 日志文件加载完成')
  
  // 自动滚动到底部
  nextTick(() => {
    if (logContainer.value) {
      logContainer.value.scrollTop = logContainer.value.scrollHeight
    }
  })
}

// 格式化时间
const formatTime = (timeStr) => {
  if (!timeStr) return '--'
  return new Date(timeStr).toLocaleString('zh-CN')
}

// 格式化日志时间
const formatLogTime = (timeStr) => {
  if (!timeStr) return ''
  return new Date(timeStr).toLocaleTimeString('zh-CN')
}

// 英文键名到中文标签的映射表
const englishToChinese = {
  // 温度相关
  'lowTemp': '应用低温（℃）',
  'highTemp': '应用高温（℃）',
  'operatingTemp': '过冷/过热度（K）',
  
  // 压力相关
  'inletAbsolutePressure': '入口绝对压力（MPa）',
  'minOpeningPressureDiff': '最小开阀压差（目标压降值）Dp（Pa）',
  
  // 流量相关
  'inletMassFlow': '入口质量流量（kg/s）',
  
  // 材质相关
  'oRingMaterial': 'O型圈材质（HNBR/EPDM）',
  'coreMaterial': '阀芯材质（PPS+40%GF/PA66+30%GF）',
  
  // 尺寸相关
  'valveDiameter': '阀口直径d（mm）',
  'pipeDiameter': '单向阀安装位置前后管路内径D1（mm）',
  'sealAngle': '内壁密封斜度θ（°）',
  'guideRodLengthOptRangePositive': '阀芯导杆长度H4寻优范围（以设计值为中心）+（mm）',
  'guideRodLengthOptRangeNegative': '阀芯导杆长度H4寻优范围（以设计值为中心）-（mm）',
  
  // 工质相关
  'fluidMedium': '流体工质（R134a/R1234yf/R744/50EG）',
  
  // 计算参数
  'iterationStep': '迭代步长（默认0.1mm）',
  'maxSteps': '最大步数',
  
  // 文件上传相关
  'modelFilePath': '模型上传路径'
}

// 格式化参数
const formatParameters = (params) => {
  if (!params) return '暂无参数'
  
  let parsedParams = null
  
  // 如果是字符串，尝试解析为JSON
  if (typeof params === 'string') {
    try {
      parsedParams = JSON.parse(params)
    } catch {
      return params
    }
  } else if (typeof params === 'object' && Object.keys(params).length > 0) {
    parsedParams = params
  }
  
  if (!parsedParams) return '暂无参数'
  
  // 将英文键名转换为中文标签
  const chineseParams = {}
  Object.entries(parsedParams).forEach(([key, value]) => {
    const chineseKey = englishToChinese[key] || key
    chineseParams[chineseKey] = value
  })
  
  return JSON.stringify(chineseParams, null, 2)
}

// 获取状态类型
const getStatusType = (status) => {
  try {
    const statusMap = {
      'pending': 'info',
      'running': 'warning',
      'completed': 'success',
      'failed': 'danger'
    }
    return statusMap[status] || 'info'
  } catch (error) {
    console.error('获取状态类型失败:', error)
    return 'info'
  }
}

// 获取状态文本
const getStatusText = (status) => {
  try {
    const statusMap = {
      'pending': '等待中',
      'running': '运行中',
      'completed': '已完成',
      'failed': '失败'
    }
    return statusMap[status] || '未知'
  } catch (error) {
    console.error('获取状态文本失败:', error)
    return '未知'
  }
}

// 获取进度状态
const getProgressStatus = (status) => {
  try {
    if (status === 'failed') return 'exception'
    if (status === 'completed') return 'success'
    return ''
  } catch (error) {
    console.error('获取进度状态失败:', error)
    return ''
  }
}

// 判断是否为图片文件
const isImageFile = (fileName) => {
  const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp']
  const extension = fileName.toLowerCase().substring(fileName.lastIndexOf('.'))
  return imageExtensions.includes(extension)
}

// 判断是否为3D模型文件
const is3DModelFile = (fileName) => {
  const modelExtensions = ['.gltf', '.glb']
  const extension = fileName.toLowerCase().substring(fileName.lastIndexOf('.'))
  return modelExtensions.includes(extension)
}

// 判断是否为文本文件
const isTextFile = (fileName) => {
  const textExtensions = ['.txt', '.log', '.md', '.json', '.xml', '.csv']
  const extension = fileName.toLowerCase().substring(fileName.lastIndexOf('.'))
  return textExtensions.includes(extension)
}

// 自动加载3D模型文件
const autoLoad3DModels = async () => {
  if (!taskDetail.value.result_files || taskDetail.value.result_files.length === 0) {
    return
  }
  
  const currentTaskId = parseInt(route.params.id)
  const modelFiles = taskDetail.value.result_files.filter(file => {
    return file.source_task_id === currentTaskId && is3DModelFile(file.file_name)
  })
  
  console.log('🔍 自动加载3D模型文件:', modelFiles.length, '个文件')
  
  for (const file of modelFiles) {
    try {
      console.log('🔍 开始自动加载3D模型:', file.file_name)
      await loadModelFile(file)
    } catch (error) {
      console.error('❌ 自动加载3D模型失败:', file.file_name, error)
    }
  }
}

// 自动加载文本文件内容
const autoLoadTextFiles = async () => {
  if (!taskDetail.value.result_files || taskDetail.value.result_files.length === 0) {
    return
  }
  
  const currentTaskId = parseInt(route.params.id)
  const textFiles = taskDetail.value.result_files.filter(file => {
    return file.source_task_id === currentTaskId && isTextFile(file.file_name)
  })
  
  console.log('🔍 自动加载文本文件:', textFiles.length, '个文件')
  
  for (const file of textFiles) {
    try {
      console.log('🔍 开始自动加载文本文件:', file.file_name)
      await loadTextFileContent(file)
    } catch (error) {
      console.error('❌ 自动加载文本文件失败:', file.file_name, error)
    }
  }
}

// 加载文本文件内容
const loadTextFileContent = async (file) => {
  try {
    console.log('📄 开始加载文本文件内容:', file.file_name)
    
    const response = await fetch(`/dev-api/business/files/public/download?path=${encodeURIComponent(file.file_path)}`)
    if (response.ok) {
      const text = await response.text()
      fileContents.value[file.file_path] = text
      console.log('✅ 文本文件内容加载成功:', file.file_name, '内容长度:', text.length)
    } else {
      console.error('❌ 文本文件下载失败:', file.file_name, response.status)
    }
  } catch (error) {
    console.error('❌ 加载文本文件内容失败:', file.file_name, error)
  }
}

// 获取文件URL
const getFileUrl = (filePath) => {
  if (!filePath) {
    return ''
  }
  
  // 构建文件下载URL（使用公共端点，不需要认证）
  const url = `/dev-api/business/files/public/download?path=${encodeURIComponent(filePath)}`
  return url
}

// 下载3D模型文件数据
const downloadModelFile = async (filePath) => {
  try {
    console.log('🌐 开始下载3D模型文件:', filePath)
    
    // 构建下载URL
    const url = `/dev-api/business/files/public/download?path=${encodeURIComponent(filePath)}`
    
    // 使用fetch下载文件
    const response = await fetch(url)
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }
    
    // 获取文件二进制数据
    const arrayBuffer = await response.arrayBuffer()
    const uint8Array = new Uint8Array(arrayBuffer)
    
    console.log('✅ 3D模型文件下载完成:', {
      url: url,
      size: uint8Array.length,
      contentType: response.headers.get('content-type')
    })
    
    return uint8Array
    
  } catch (error) {
    console.error('❌ 下载3D模型文件失败:', error)
    throw new Error(`下载模型文件失败: ${error.message}`)
  }
}

// 格式化文件大小
const formatFileSize = (size) => {
  if (!size) return '0 KB'
  if (size < 1024) return `${size} B`
  if (size < 1024 * 1024) return `${(size / 1024).toFixed(1)} KB`
  return `${(size / (1024 * 1024)).toFixed(1)} MB`
}

// 处理图片加载错误
const handleImageError = () => {
  ElMessage.warning('图片加载失败，可能文件不存在或网络问题')
}

// 返回上一页
const goBack = () => {
  router.go(-1)
}

// 获取页面标题
const getPageTitle = () => {
  try {
    const taskName = taskDetail.value?.task_name || taskDetail.value?.taskName
    if (taskName) {
      return `${taskName} - 详情看板`
    }
    return '任务详情看板'
  } catch (error) {
    console.error('获取页面标题失败:', error)
    return '任务详情看板'
  }
}

// 注意：报告文件处理逻辑已整合到结果文件显示中

// 获取所有结果文件并按目录分组
const getAllResultsByDirectory = () => {
  console.log('🔍 getAllResultsByDirectory被调用')
  console.log('🔍 taskDetail.value:', taskDetail.value)
  console.log('🔍 taskDetail.value.result_files:', taskDetail.value.result_files)
  
  // 如果没有结果文件，返回空对象
  if (!taskDetail.value.result_files || taskDetail.value.result_files.length === 0) {
    console.log('📝 当前任务没有结果文件')
    // 返回空对象，避免后续处理出错
    return {}
  }
  
  const currentTaskId = parseInt(route.params.id)
  // 严格匹配当前任务ID的文件
  const allFiles = taskDetail.value.result_files.filter(file => {
    return file.source_task_id === currentTaskId
  })
  
  // 调试信息
  console.log('🔍 文件分组 - 任务ID:', currentTaskId, '文件数量:', allFiles.length)
  
  // 按目录分组
  const groupedFiles = {}
  allFiles.forEach(file => {
    // 从文件路径中提取目录
    const filePath = file.file_path || ''
    const pathParts = filePath.split('/')
    let directory = '根目录'
    
    // 尝试从路径中提取有意义的目录名
    if (pathParts.length > 1) {
      // 查找包含特定关键词的目录
      const meaningfulDirs = pathParts.filter(part => 
        part.includes('report') || 
        part.includes('image') || 
        part.includes('log') || 
        part.includes('output') ||
        part.includes('result') ||
        part.includes('data') ||
        part.includes('图片') ||
        part.includes('报告') ||
        part.includes('结果')
      )
      
      if (meaningfulDirs.length > 0) {
        directory = meaningfulDirs[0]
      } else {
        // 如果没有有意义的目录，使用倒数第二个路径部分
        directory = pathParts[pathParts.length - 2] || '根目录'
      }
    }
    
    // 按文件类型进一步分类（优先根据文件名和路径判断，然后根据file_type）
    let fileCategory = '其他文件'
    const lowerFileName = file.file_name.toLowerCase()
    const lowerFilePath = file.file_path.toLowerCase()
    
    // 优先根据文件名和路径判断类型
    if (lowerFileName.includes('log') || lowerFilePath.includes('log') || lowerFileName.endsWith('.log')) {
      fileCategory = '日志文件'
    } else if (lowerFileName.includes('report') || lowerFilePath.includes('report') || file.file_type === 'report') {
      fileCategory = isImageFile(file.file_name) ? '报告图片' : '报告文件'
    } else if (lowerFileName.includes('data') || lowerFilePath.includes('data') || file.file_type === 'data') {
      fileCategory = '数据文件'
    } else if (is3DModelFile(file.file_name)) {
      fileCategory = '3D模型文件'
    } else if (isImageFile(file.file_name)) {
      fileCategory = '图片文件'
    } else if (lowerFileName.endsWith('.txt') || lowerFileName.endsWith('.md') || lowerFileName.endsWith('.json')) {
      fileCategory = '文本文件'
    } else if (lowerFileName.endsWith('.pdf')) {
      fileCategory = 'PDF文件'
    } else if (lowerFileName.endsWith('.csv') || lowerFileName.endsWith('.xlsx') || lowerFileName.endsWith('.xls')) {
      fileCategory = '表格文件'
    } else {
      // 最后根据file_type判断
      if (file.file_type === 'log') {
        fileCategory = '日志文件'
      } else if (file.file_type === 'report') {
        fileCategory = isImageFile(file.file_name) ? '报告图片' : '报告文件'
      } else if (file.file_type === 'data') {
        fileCategory = '数据文件'
      }
    }
    
    const groupKey = `${directory} - ${fileCategory}`
    
    // console.log(`📂 文件分组: ${file.file_name} → ${groupKey}`)
    
    if (!groupedFiles[groupKey]) {
      groupedFiles[groupKey] = []
    }
    
    groupedFiles[groupKey].push(file)
  })
  
  console.log('🎯 最终分组:', Object.keys(groupedFiles).length, '组')
  
  return groupedFiles
}

// 下载文件
const downloadFile = (filePath, fileName) => {
  if (!filePath) {
    ElMessage.error('文件路径为空')
    return
  }
  // 构建下载URL
  const url = `/dev-api/business/files/download-by-path?path=${encodeURIComponent(filePath)}`
  // 创建下载链接
  const link = document.createElement('a')
  link.href = url
  link.download = fileName || 'download'
  link.target = '_blank'
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}

// 预览报告
const previewReport = async (filePath, fileName) => {
  if (!filePath) {
    ElMessage.error('文件路径为空')
    return
  }
  
  try {
    previewDialogVisible.value = true
    previewFileName.value = fileName
    previewContent.value = ''
    isPreviewImage.value = false
    isPreviewText.value = false
    
    // 判断文件类型
    const fileExtension = fileName.toLowerCase().split('.').pop()
    
    if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].includes(fileExtension)) {
      // 图片文件
      isPreviewImage.value = true
      previewContent.value = getFileUrl(filePath)
    } else if (['txt', 'log', 'md'].includes(fileExtension)) {
      // 文本文件
      isPreviewText.value = true
      const response = await fetch(`/dev-api/business/files/public/download?path=${encodeURIComponent(filePath)}`)
      if (response.ok) {
        const text = await response.text()
        previewContent.value = text
      } else {
        throw new Error('文件加载失败')
      }
    } else if (['pdf'].includes(fileExtension)) {
      // PDF文件
      previewContent.value = getFileUrl(filePath)
    } else if (['html', 'htm'].includes(fileExtension)) {
      // HTML文件
      previewContent.value = getFileUrl(filePath)
    } else {
      // 其他文件类型，尝试作为文本显示
      isPreviewText.value = true
      try {
        const response = await fetch(`/dev-api/business/files/public/download?path=${encodeURIComponent(filePath)}`)
        if (response.ok) {
          const text = await response.text()
          previewContent.value = text
        } else {
          throw new Error('文件加载失败')
        }
      } catch (error) {
        ElMessage.warning('该文件类型不支持预览，请下载后查看')
        previewDialogVisible.value = false
        return
      }
    }
  } catch (error) {
    console.error('预览文件失败:', error)
    ElMessage.error('预览文件失败')
    previewDialogVisible.value = false
  }
}

// 关闭预览对话框
const handlePreviewClose = () => {
  previewDialogVisible.value = false
  previewContent.value = ''
  previewFileName.value = ''
  isPreviewImage.value = false
  isPreviewText.value = false
}

// 刷新结果
const refreshResults = async () => {
  if (refreshing.value) return
  refreshing.value = true
  try {
    const taskId = parseInt(route.params.id)
    const res = await getTaskDetail(taskId)
    if (res.code === 200) {
      // 使用与自动刷新相同的智能更新逻辑
      const oldProgress = taskDetail.value.progress // 保存当前进度
      const updatedTaskData = res.data

      // 检查WebSocket是否在最近30秒内更新过进度
      const websocketRecentlyUpdated = lastWebSocketProgressUpdate.value &&
        (Date.now() - lastWebSocketProgressUpdate.value) < 30000 // 30秒内

      // 确定最终使用的进度值
      let finalProgress = oldProgress

      if (websocketRecentlyUpdated) {
        // WebSocket最近更新过，保留当前进度
        finalProgress = oldProgress
        console.log(`🔍 手动刷新保留WebSocket进度: ${oldProgress}%，忽略API进度: ${updatedTaskData.progress}%`)
      } else {
        // WebSocket没有最近更新，使用API进度，但确保不倒退
        if (updatedTaskData.progress >= oldProgress) {
          finalProgress = updatedTaskData.progress
          if (updatedTaskData.progress !== oldProgress) {
            console.log(`📊 手动刷新更新进度: ${oldProgress} -> ${updatedTaskData.progress}`)
            progressUpdateSource.value = 'api'
          }
        } else {
          // API返回的进度比当前进度低，保留当前进度
          finalProgress = oldProgress
          console.log(`🔍 手动刷新保留当前进度: ${oldProgress}%，忽略API较低进度: ${updatedTaskData.progress}%`)
        }
      }

      // 合并任务数据，使用确定的进度值（创建新对象避免readonly错误）
      taskDetail.value = Object.assign({}, updatedTaskData, {
        progress: finalProgress
      })

      // 自动加载文本文件内容
      await autoLoadTextFiles()
      
      // 自动加载3D模型文件
      await autoLoad3DModels()

      // 检查任务状态
      const taskStatus = updatedTaskData.status || updatedTaskData.task_status

      if (taskStatus === 'running') {
        // 如果任务正在运行，重新启动WebSocket连接
        startWebSocket(taskId)
        // 重置日志加载标记，因为任务重新开始运行
        logFilesLoaded.value = false
        // 如果自动刷新没有运行，则启动它
        if (!autoRefreshInterval.value) {
          startAutoRefresh()
        }
      } else if (taskStatus === 'pending') {
        // 如果任务等待中，确保自动刷新运行
        if (!autoRefreshInterval.value) {
          startAutoRefresh()
        }
      } else if (taskStatus === 'completed' || taskStatus === 'failed') {
        // 如果任务已结束，停止自动刷新，不自动加载日志文件
        stopAutoRefresh()
        console.log('🔍 任务已结束，保留执行过程中的日志')
      }

      ElMessage.success('结果刷新成功')
    } else {
      ElMessage.error(res.msg || '刷新结果失败')
    }
  } catch (error) {
    console.error('刷新结果失败:', error)
    ElMessage.error('刷新结果失败')
  } finally {
    refreshing.value = false
  }
}

// 启动自动刷新（只为运行中的任务）
const startAutoRefresh = () => {
  // 清除之前的定时器
  if (autoRefreshInterval.value) {
    clearInterval(autoRefreshInterval.value)
  }
  
  // 检查当前任务状态，只为运行中或等待中的任务启动自动刷新
  const currentStatus = taskDetail.value.status || taskDetail.value.task_status
  if (currentStatus !== 'running' && currentStatus !== 'pending') {
    console.log('📅 任务已完成，不启动自动刷新')
    return
  }
  
  console.log('📅 启动自动刷新，当前状态:', currentStatus)
  
  // 每60秒自动刷新一次（增加频率，避免频繁请求）
  autoRefreshInterval.value = setInterval(async () => {
    if (!loading.value) {
      try {
        const taskId = parseInt(route.params.id)
        const res = await getTaskDetail(taskId)
        if (res.code === 200) {
          const oldStatus = taskDetail.value.status || taskDetail.value.task_status
          const oldProgress = taskDetail.value.progress // 保存当前进度
          
          // 更新任务信息，但保留WebSocket更新的进度
          const updatedTaskData = res.data
          const newStatus = updatedTaskData.status || updatedTaskData.task_status
          
          // 检查WebSocket是否在最近30秒内更新过进度
          const websocketRecentlyUpdated = lastWebSocketProgressUpdate.value && 
            (Date.now() - lastWebSocketProgressUpdate.value) < 30000 // 30秒内
          
          // 确定最终使用的进度值（与手动刷新逻辑保持一致）
          let finalProgress = oldProgress

          if (websocketRecentlyUpdated) {
            // WebSocket最近更新过，保留当前进度
            finalProgress = oldProgress
          } else {
            // WebSocket没有最近更新，使用API进度，但确保不倒退
            if (updatedTaskData.progress >= oldProgress) {
              finalProgress = updatedTaskData.progress
              if (updatedTaskData.progress !== oldProgress) {
                console.log(`📊 API自动刷新更新进度: ${oldProgress} -> ${updatedTaskData.progress}`)
                progressUpdateSource.value = 'api'
              }
            } else {
              // API返回的进度比当前进度低，保留当前进度
              finalProgress = oldProgress
              console.log(`🔍 自动刷新保留当前进度: ${oldProgress}%，忽略API较低进度: ${updatedTaskData.progress}%`)
            }
          }

          // 合并任务数据，使用确定的进度值
          taskDetail.value = {
            ...updatedTaskData,
            progress: finalProgress
          }
          
          // 只有当状态从running变为completed/failed时才显示完成消息
          // 避免pending到completed的直接跳转也显示消息
          if (oldStatus === 'running' &&
              (newStatus === 'completed' || newStatus === 'failed')) {
            stopAutoRefresh()

            // 任务完成后立即刷新运行结果，不加载日志文件
            console.log('🎉 任务完成，立即刷新运行结果')

            // 立即刷新任务详情和结果文件，确保最新数据
            await fetchTaskDetail() // 刷新任务详情和结果文件

            // 等待2秒后再次刷新，确保文件处理完成
            setTimeout(async () => {
              console.log('🔄 任务完成2秒后再次刷新，确保文件处理完成')
              await fetchTaskDetail()
            }, 2000)

            // 等待5秒后第三次刷新，确保所有文件都已处理
            setTimeout(async () => {
              console.log('🔄 任务完成5秒后最终刷新')
              await fetchTaskDetail()
            }, 5000)
          }

          // 如果任务状态变为completed或failed，也触发刷新（兜底机制，但不显示消息避免重复）
          if ((newStatus === 'completed' || newStatus === 'failed') && oldStatus !== 'running') {
            console.log('🔄 检测到任务完成状态，触发兜底刷新（无消息避免重复）')
            setTimeout(async () => {
              await fetchTaskDetail()
            }, 1000)
          }
          // 如果状态从pending直接变为completed，不显示消息，让用户自己发现
          // 如果状态从pending变为running，显示开始执行的消息并启动WebSocket连接
          else if (oldStatus === 'pending' && newStatus === 'running') {
            ElMessage.info('🚀 任务已开始执行')
            // 启动WebSocket连接以接收实时进度更新
            console.log('🔍 任务状态从pending变为running，启动WebSocket连接')
            startWebSocket(taskId)
            // 重置日志加载标记
            logFilesLoaded.value = false
          }
          // 如果任务正在运行但没有WebSocket连接，重新建立连接
          else if (newStatus === 'running' && (!ws.value || ws.value.ws?.readyState !== WebSocket.OPEN)) {
            console.log('🔍 任务正在运行但WebSocket未连接，重新建立连接')
            startWebSocket(taskId)
          }
        }
      } catch (error) {
        console.warn('自动刷新失败:', error)
        // 如果连续失败3次，停止自动刷新
        if (error.message && error.message.includes('timeout')) {
          console.warn('自动刷新超时，停止自动刷新')
          stopAutoRefresh()
        }
      }
    }
  }, 60000) // 改为60秒，减少刷新频率
}

// 停止自动刷新
const stopAutoRefresh = () => {
  if (autoRefreshInterval.value) {
    clearInterval(autoRefreshInterval.value)
    autoRefreshInterval.value = null
  }
}





// 加载3D模型（小预览）
const loadModelFile = async (file) => {
  try {
    console.log('🚀 开始从后端加载GLB/GLTF模型:', {
      fileName: file.file_name,
      fileId: file.file_id,
      filePath: file.file_path
    })
    
    modelLoading.value[file.file_id] = true
    console.log('🔄 设置加载状态为true:', file.file_id)
    
    // 从后端下载3D模型文件
    console.log('🌐 开始从后端下载3D模型文件...')
    const modelFileData = await downloadModelFile(file.file_path)
    if (!modelFileData) {
      console.log('❌ 模型文件下载失败')
      ElMessage.error('模型文件下载失败')
      return
    }
    
    console.log('✅ 3D模型文件下载成功:', {
      fileName: file.file_name,
      dataSize: modelFileData.length
    })
    
    // 缓存文件数据供预览使用
    file.cachedModelData = { data: modelFileData, name: file.file_name }
    modelFileCache.value[file.file_id] = { data: modelFileData, name: file.file_name }
    console.log('💾 已缓存GLB/GLTF模型文件数据:', {
      fileName: file.file_name,
      dataSize: modelFileData.length,
      fileId: file.file_id
    })
    
    // 等待DOM更新
    await nextTick()
    console.log('🔍 DOM更新完成，开始查找容器...')
    
    // 等待一点时间确保DOM完全渲染
    await new Promise(resolve => setTimeout(resolve, 100))
    
    // 创建Three.js查看器
    const containerId = `modelContainer-${file.file_id}`
    console.log('🔍 查找容器 ID:', containerId)
    const container = document.getElementById(containerId)
    if (container) {
      console.log('🎨 找到小预览容器:', {
        id: container.id,
        clientWidth: container.clientWidth,
        clientHeight: container.clientHeight,
        visible: container.offsetWidth > 0 && container.offsetHeight > 0
      })
      
      console.log('🔧 开始创建Three.js小预览查看器...')
      const viewer = markRaw(new ThreeModelViewer(container))
      await viewer.loadModel(modelFileData, file.file_name)
      
      modelViewers.value[file.file_id] = viewer
      console.log('✅ Three.js小预览查看器创建成功')
      
      // 确保容器可见
      container.style.display = 'block'
      console.log('🎨 容器显示状态已设置为block')
      
    } else {
      console.log('❌ 未找到小预览容器:', containerId)
    }
    
    ElMessage.success('GLB/GLTF模型加载成功')
    console.log('🎉 整个加载流程完成')
    
  } catch (error) {
    console.error('❌ 加载GLB/GLTF模型失败:', {
      error: error.message,
      stack: error.stack,
      fileName: file.file_name,
      fileId: file.file_id
    })
    ElMessage.error('加载GLB/GLTF模型失败: ' + error.message)
  } finally {
    modelLoading.value[file.file_id] = false
    console.log('🔄 清除加载状态:', file.file_id)
  }
}

// 预览3D模型（大预览）
const previewModel = async (file) => {
  try {
    console.log('🔍 开始大预览3D模型:', {
      fileName: file.file_name,
      fileId: file.file_id,
      filePath: file.file_path
    })
    
    console.log('🔍 检查缓存数据状态:', {
      fileObjectCache: !!file.cachedModelData,
      globalCache: !!modelFileCache.value[file.file_id],
      globalCacheKeys: Object.keys(modelFileCache.value),
      modelViewersKeys: Object.keys(modelViewers.value)
    })
    
    // 检查是否有缓存的文件数据（双重检查）
    const cachedData = file.cachedModelData || modelFileCache.value[file.file_id]
    if (!cachedData) {
      console.log('❌ 没有找到缓存的3D模型文件数据')
      console.log('🔍 详细缓存状态:', {
        fileCacheType: typeof file.cachedModelData,
        globalCacheType: typeof modelFileCache.value[file.file_id]
      })
      ElMessage.warning('请先点击"加载3D模型"按钮加载3D模型')
      return
    }
    
    console.log('✅ 找到缓存数据:', {
      dataType: typeof cachedData,
      hasData: !!cachedData.data,
      hasName: !!cachedData.name,
      dataSize: cachedData.data?.length
    })
    
    console.log('🎬 打开大预览对话框')
    modelPreviewDialogVisible.value = true
    modelPreviewFileName.value = cachedData.name || file.file_name
    modelPreviewLoading.value = true
    
    // 等待对话框渲染完成
    await nextTick()
    console.log('✅ 对话框DOM更新完成')
    
    // 额外等待确保对话框完全显示
    await new Promise(resolve => setTimeout(resolve, 200))
    console.log('✅ 对话框显示延迟完成')
    
    console.log('🎬 开始创建大预览查看器')
    
    // 使用缓存的文件数据创建大预览查看器
    console.log('🔍 查找大预览容器元素...')
    
    let container = modelPreviewContainer.value
    console.log('🔍 通过ref获取容器:', !!container)
    
    if (!container) {
      // 备选方案：通过ID获取容器
      container = document.getElementById('modelPreviewMainContainer')
      console.log('🔄 通过ID获取容器:', !!container)
    }
    
    if (container) {
      console.log('🎯 大预览容器详细信息:', {
        element: container,
        id: container.id,
        clientWidth: container.clientWidth,
        clientHeight: container.clientHeight,
        offsetWidth: container.offsetWidth,
        offsetHeight: container.offsetHeight,
        style: container.style.cssText,
        visible: container.offsetWidth > 0 && container.offsetHeight > 0,
        parentElement: container.parentElement?.tagName
      })
      
      console.log('🔧 开始创建Three.js大预览查看器...')
      modelPreviewViewer.value = markRaw(new ThreeModelViewer(container))
      await modelPreviewViewer.value.loadModel(cachedData.data, cachedData.name)
      console.log('✅ Three.js大预览查看器创建完成')
      
    } else {
      console.log('❌ 无法获取预览容器元素')
      console.log('🔍 对话框状态:', {
        dialogVisible: modelPreviewDialogVisible.value,
        fileName: modelPreviewFileName.value,
        loading: modelPreviewLoading.value
      })
      
      // 查找所有可能的容器元素
      const allContainers = document.querySelectorAll('[id*="model"]')
      console.log('🔍 页面所有model相关元素:', Array.from(allContainers).map(c => ({
        id: c.id,
        className: c.className,
        tagName: c.tagName,
        visible: c.offsetWidth > 0 && c.offsetHeight > 0
      })))
    }
    
    ElMessage.success('3D模型预览加载成功')
    console.log('🎉 大预览加载流程完成')
    
  } catch (error) {
    console.error('❌ 预览3D模型失败:', {
      error: error.message,
      stack: error.stack,
      fileName: file.file_name,
      fileId: file.file_id
    })
    ElMessage.error('预览3D模型失败: ' + error.message)
  } finally {
    modelPreviewLoading.value = false
    console.log('🔄 清除预览加载状态')
  }
}

// 重置模型视角
const resetModelView = async () => {
  if (modelPreviewViewer.value) {
    await modelPreviewViewer.value.resetView()
    console.log('🔄 模型视角已重置')
  }
}

// 适应窗口
const fitModelView = async () => {
  if (modelPreviewViewer.value) {
    await modelPreviewViewer.value.fitView()
    console.log('📐 模型视图已适应窗口')
  }
}

// 切换自动旋转
const toggleAutoRotate = async () => {
  if (modelPreviewViewer.value) {
    modelAutoRotate.value = await modelPreviewViewer.value.toggleAutoRotate()
    console.log('🔄 自动旋转状态:', modelAutoRotate.value ? '开启' : '关闭')
  }
}



// 关闭3D模型预览对话框
const handleModelPreviewClose = () => {
  if (modelPreviewViewer.value) {
    modelPreviewViewer.value.destroy()
  }
  modelPreviewDialogVisible.value = false
  modelPreviewFileName.value = ''
  modelPreviewLoading.value = false
  modelAutoRotate.value = true
  modelPreviewViewer.value = null
  console.log('🔄 3D模型预览对话框已关闭')
}















// 组件挂载
onMounted(() => {
  console.log('🔍 任务详情页组件挂载，路由参数:', route.params)
  fetchTaskDetail()
  // 注意：自动刷新会在fetchTaskDetail中根据任务状态决定是否启动
})

// 组件卸载
onUnmounted(() => {
  console.log('🔍 任务详情页组件卸载')
  if (ws.value) {
    ws.value.disconnect()
  }
  stopAutoRefresh()
})
</script>

<style scoped>
.task-detail-wrapper {
  min-height: 100vh;
}

.app-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.detail-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.progress-section {
  margin-bottom: 30px;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.progress-header h3 {
  margin: 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.progress-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.progress-source-tag {
  font-size: 12px;
  font-weight: 500;
  margin-left: 8px;
}

.progress-text {
  font-size: 18px;
  font-weight: 600;
  color: #409eff;
  display: flex;
  align-items: center;
}

.progress-bar {
  margin-bottom: 10px;
}

.debug-info {
  margin-top: 8px;
  padding: 4px 8px;
  background-color: #f5f7fa;
  border-radius: 4px;
  font-size: 12px;
}

.time-section {
  margin-bottom: 30px;
}

.time-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.time-item label {
  font-weight: 600;
  color: #606266;
  margin-right: 10px;
  min-width: 80px;
}

.time-item span {
  color: #303133;
}

.parameters-section {
  margin-bottom: 30px;
}

.parameters-section h3 {
  margin: 0 0 15px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.parameters-card {
  background-color: #fafafa;
}

.parameters-json {
  margin: 0;
  padding: 15px;
  background-color: #f5f5f5;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 14px;
  line-height: 1.5;
  color: #333;
  white-space: pre-wrap;
  word-break: break-all;
}

.content-section {
  margin-bottom: 20px;
}

.log-section, .result-section {
  height: 700px;
  display: flex;
  flex-direction: column;
}

.log-header, .result-section h3 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.log-header h3, .result-section h3 {
  margin: 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.result-controls {
  display: flex;
  align-items: center;
  gap: 10px;
}

.result-header h3 {
  margin: 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.log-container {
  flex: 1;
  background-color: #1e1e1e;
  border-radius: 4px;
  padding: 15px;
  overflow-y: auto;
  max-height: 650px;
  font-family: 'Courier New', monospace;
  font-size: 13px;
  line-height: 1.4;
  border: 1px solid #e4e7ed;
}

/* 自定义滚动条样式 */
.log-container::-webkit-scrollbar {
  width: 8px;
}

.log-container::-webkit-scrollbar-track {
  background: #2d2d2d;
  border-radius: 4px;
}

.log-container::-webkit-scrollbar-thumb {
  background: #555;
  border-radius: 4px;
}

.log-container::-webkit-scrollbar-thumb:hover {
  background: #777;
}

.log-item {
  margin-bottom: 5px;
  padding: 2px 0;
}

.log-time {
  color: #888;
  margin-right: 10px;
}

.log-level {
  margin-right: 10px;
  font-weight: 600;
}

.log-info .log-level {
  color: #4fc08d;
}

.log-warning .log-level {
  color: #e6a23c;
}

.log-error .log-level {
  color: #f56c6c;
}

.log-debug .log-level {
  color: #909399;
}

.log-progress .log-level {
  color: #409eff;
}

.log-file_log .log-level {
  color: #67c23a;
}

.log-separator {
  border-top: 1px solid #444;
  margin: 10px 0;
  padding-top: 10px;
}

.log-separator .log-level {
  color: #909399;
}

.log-separator .log-message {
  color: #909399;
  font-style: italic;
}

.log-message {
  color: #e1e1e1;
}

.no-logs {
  color: #888;
  text-align: center;
  padding: 20px;
}

.result-container {
  flex: 1;
  background-color: #fafafa;
  border-radius: 4px;
  padding: 15px;
  overflow-y: auto;
  max-height: 650px;
  border: 1px solid #e4e7ed;
}

/* 结果容器滚动条样式 */
.result-container::-webkit-scrollbar {
  width: 8px;
}

.result-container::-webkit-scrollbar-track {
  background: #f0f0f0;
  border-radius: 4px;
}

.result-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.result-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.result-files {
  flex: 1;
  background-color: transparent;
  border-radius: 4px;
  padding: 0;
}

.file-group {
  margin-bottom: 25px;
  background-color: white;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.group-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  background-color: #f8f9fa;
  border-bottom: 1px solid #e4e7ed;
  border-radius: 6px 6px 0 0;
}

.group-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.group-content {
  padding: 15px;
}

.file-item {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
  transition: all 0.3s ease;
}

.file-item:hover {
  background-color: #f0f2f5;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.file-item:last-child {
  margin-bottom: 0;
}

.file-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.file-name {
  font-weight: 600;
  color: #303133;
}

.file-size {
  color: #909399;
  font-size: 12px;
}

.file-preview {
  text-align: center;
}

.image-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 150px;
  background-color: #f5f7fa;
  color: #909399;
  font-size: 14px;
  border-radius: 4px;
}

.image-error .el-icon {
  font-size: 28px;
  margin-bottom: 8px;
}

.file-icon {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 150px;
  background-color: #f5f7fa;
  border-radius: 4px;
  color: #909399;
  font-size: 14px;
}

.file-icon .el-icon {
  font-size: 48px;
  margin-bottom: 10px;
  color: #409eff;
}

.file-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 150px;
  background-color: #f5f7fa;
  border-radius: 4px;
  color: #909399;
  font-size: 14px;
}

.file-loading .el-icon {
  font-size: 32px;
  margin-bottom: 10px;
  color: #409eff;
}

.no-task-results {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #909399;
  margin-top: 20px;
}

.no-task-results .el-icon {
  font-size: 48px;
  margin-bottom: 10px;
}

.log-controls {
  display: flex;
  align-items: center;
  gap: 10px;
}

.file-actions {
  display: flex;
  gap: 10px;
  margin-top: 10px;
}

/* 文件预览区域样式优化 */
.file-preview {
  cursor: pointer;
  transition: all 0.2s ease;
}

.file-preview:hover {
  background-color: #f8f9fa;
}

/* 文件文本内容直接在红框内展示 */
.file-text-content {
  width: 100%;
  height: 100%;
  padding: 12px;
  background-color: #fafbfc;
}

.file-text-content pre {
  margin: 0;
  font-family: 'Courier New', Consolas, 'Liberation Mono', Menlo, monospace;
  font-size: 11px;
  line-height: 1.4;
  color: #24292f;
  white-space: pre-wrap;
  word-wrap: break-word;
  background: transparent;
  max-height: 300px;
  overflow-y: auto;
}

/* 点击提示样式 */
.click-hint {
  font-size: 12px;
  color: #6a737d;
  margin-top: 8px;
  text-align: center;
}

/* 文本内容滚动条样式 */
.file-text-content pre::-webkit-scrollbar {
  width: 6px;
}

.file-text-content pre::-webkit-scrollbar-track {
  background: #f1f3f4;
  border-radius: 3px;
}

.file-text-content pre::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.file-text-content pre::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.preview-content {
  max-height: 80vh;
  overflow-y: auto;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
}

/* 预览内容滚动条样式 */
.preview-content::-webkit-scrollbar {
  width: 8px;
}

.preview-content::-webkit-scrollbar-track {
  background: #f0f0f0;
  border-radius: 4px;
}

.preview-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.preview-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.preview-image {
  text-align: center;
}

.preview-text {
  background-color: #f5f5f5;
  padding: 15px;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 14px;
  line-height: 1.5;
  white-space: pre-wrap;
  word-break: break-all;
  max-height: 70vh;
  overflow-y: auto;
  border: 1px solid #e4e7ed;
}

/* 文本预览滚动条样式 */
.preview-text::-webkit-scrollbar {
  width: 8px;
}

.preview-text::-webkit-scrollbar-track {
  background: #f0f0f0;
  border-radius: 4px;
}

.preview-text::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.preview-text::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.preview-iframe {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
}

.preview-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #909399;
}

.preview-loading .el-icon {
  font-size: 32px;
  margin-bottom: 10px;
}

/* 3D模型相关样式 */
.model-viewer-container {
  position: relative;
  width: 200px;
  height: 150px;
  margin: 0 auto;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background-color: #f8f9fa;
  overflow: hidden;
}

.model-container {
  width: 100%;
  height: 100%;
  border-radius: 4px;
  background-color: #f0f0f0 !important;
  position: relative;
  overflow: hidden;
}

/* Three.js Canvas 元素样式 */
.model-container canvas {
  width: 100% !important;
  height: 100% !important;
  display: block !important;
  border-radius: 4px;
  background-color: #f0f0f0 !important;
}

.model-placeholder {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #f8f9fa;
  color: #909399;
  font-size: 14px;
  border-radius: 4px;
  z-index: 10;
}

.model-loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: rgba(248, 249, 250, 0.9);
  color: #909399;
  font-size: 14px;
  border-radius: 4px;
  z-index: 20;
}

.model-placeholder .el-icon {
  font-size: 32px;
  margin-bottom: 8px;
  color: #409eff;
}

/* 3D模型预览对话框样式 */
.model-preview-content {
  position: relative;
  height: 70vh;
  display: flex;
  flex-direction: column;
}

.model-preview-toolbar {
  padding: 10px 0;
  border-bottom: 1px solid #e4e7ed;
  margin-bottom: 15px;
}

.model-preview-viewer {
  flex: 1;
  position: relative;
  background-color: #f0f0f0;
  border-radius: 4px;
  overflow: hidden;
}

.model-preview-container {
  width: 100%;
  height: 100%;
  display: block;
  background-color: #f0f0f0 !important;
  border-radius: 4px;
  position: relative;
  overflow: hidden;
}

/* 大预览 Three.js Canvas 元素样式 */
.model-preview-container canvas {
  width: 100% !important;
  height: 100% !important;
  display: block !important;
  border-radius: 4px;
  background-color: #f0f0f0 !important;
}

.model-preview-loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #909399;
  background-color: rgba(255, 255, 255, 0.9);
  padding: 20px;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.model-preview-loading .el-icon {
  font-size: 32px;
  margin-bottom: 10px;
}




</style> 