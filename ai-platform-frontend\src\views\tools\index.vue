<template>
  <div class="app-container">
    <!-- 类型筛选 -->
    <div class="filter-container">
      <div class="filter-types-search">
        <el-radio-group v-model="selectedType" class="mr-2" @change="handleTypeChange">
          <el-radio-button value="">全部</el-radio-button>
          <el-radio-button v-for="type in types" :key="type.typeId" :value="type.typeId">{{ type.displayName }}</el-radio-button>
        </el-radio-group>
        <el-input
          v-model="searchQuery"
          placeholder="搜索工具名称"
          class="search-input ml-2"
          clearable
          @input="handleSearch"
          style="width: 220px;"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
      </div>
    </div>

    <!-- 工具列表 -->
    <el-row :gutter="20" class="tools-list">
      <el-col v-for="tool in filteredTools" :key="tool.toolId" :xs="24" :sm="12" :md="8" :lg="6">
        <el-card class="tool-card" shadow="hover" style="cursor:pointer;">
          <!-- @dblclick="goToDetails(tool)" 双击功能已注释 -->
          <template #header>
            <div class="tool-header">
              <span class="tool-name">{{ tool.toolName }}</span>
              <el-tag size="small" :type="getTypeTagType(tool.typeId)">
                {{ getTypeName(tool.typeId) }}
              </el-tag>
            </div>
          </template>
          <div class="tool-content">
            <p class="tool-description">{{ tool.description }}</p>
            <div class="tool-footer">
              <span class="tool-vendor">供应商: {{ tool.vendor }}</span>
              <span class="tool-version">版本: {{ tool.version }}</span>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[12, 24, 36, 48]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { Search } from '@element-plus/icons-vue'
import { getToolsList } from '@/api/tools'
import { getTypesList } from '@/api/types'
import { ElMessage } from 'element-plus'
import { useRouter } from 'vue-router'

// 数据
const tools = ref([])
const types = ref([])
const selectedType = ref('')
const searchQuery = ref('')
const currentPage = ref(1)
const pageSize = ref(12)
const total = ref(0)
const router = useRouter()

// 获取工具列表
const fetchTools = async () => {
  try {
    const response = await getToolsList({
      pageNum: currentPage.value,
      pageSize: pageSize.value,
      typeId: selectedType.value || undefined,
      toolName: searchQuery.value || undefined
    })
    if (response.code === 200) {
      tools.value = response.rows.map(tool => {
        // 根据工具名称动态映射toolType
        const toolName = tool.toolName || ''
        
        if (toolName.includes('单向阀建模')) {
          tool.toolType = 'jm'
        } else if (toolName.includes('O型圈选型') || toolName.includes('O形圈选型')) {
          tool.toolType = 'o'
        } else if (toolName.includes('单向阀仿真')) {
          tool.toolType = 'fz'
        } else if (toolName.includes('单向阀建模仿真')) {
          tool.toolType = 'fzjm'
        } else {
          // 兜底：使用工具ID作为类型
          tool.toolType = `tool_${tool.toolId}`
        }
        
        return tool
      })
      total.value = response.total
      console.log('工具列表数据:', tools.value)
    } else {
      ElMessage.error(response.msg || '获取工具列表失败')
    }
  } catch (error) {
    console.error('获取工具列表失败:', error)
    ElMessage.error('获取工具列表失败')
  }
}

// 获取类型列表
const fetchTypes = async () => {
  try {
    const response = await getTypesList({
      pageNum: 1,
      pageSize: 100,
      isActive: 1
    })
    if (response.code === 200) {
      types.value = response.rows
    } else {
      ElMessage.error(response.msg || '获取类型列表失败')
    }
  } catch (error) {
    console.error('获取类型列表失败:', error)
    ElMessage.error('获取类型列表失败')
  }
}

// 根据类型ID获取类型名称
const getTypeName = (typeId) => {
  const type = types.value.find(t => t.typeId === typeId)
  return type ? type.displayName : '未知类型'
}

// 根据类型ID获取标签类型
const getTypeTagType = (typeId) => {
  const type = types.value.find(t => t.typeId === typeId)
  if (!type) return ''
  
  // 根据颜色代码返回对应的标签类型
  const colorMap = {
    'success': 'success',
    'warning': 'warning',
    'danger': 'danger',
    'info': 'info'
  }
  return colorMap[type.colorCode] || 'info'
}

// 筛选后的工具列表
const filteredTools = computed(() => {
  return tools.value
})

// 事件处理
const handleTypeChange = () => {
  currentPage.value = 1
  fetchTools()
}

const handleSearch = () => {
  currentPage.value = 1
  fetchTools()
}

const handleSizeChange = (val) => {
  pageSize.value = val
  fetchTools()
}

const handleCurrentChange = (val) => {
  currentPage.value = val
  fetchTools()
}

// 双击功能已注释
// function goToDetails(tool) {
//   if (tool && tool.toolType) {
//     router.push(`/tools/details/${tool.toolType}`)
//   } else {
//     console.error('工具类型(toolType)不存在', tool)
//     ElMessage.error('无法跳转：工具类型未定义')
//   }
// }

// 生命周期钩子
onMounted(() => {
  fetchTypes()
  fetchTools()
})
</script>

<style scoped>
.app-container {
  padding: 30px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.filter-container {
  display: flex;
  align-items: center;
  margin-bottom: 30px;
  padding: 24px 0 0 0;
  background: transparent;
  border-radius: 0;
  border: none;
  box-shadow: none;
  flex-shrink: 0;
}

.filter-types-search {
  display: flex;
  align-items: center;
  gap: 24px;
  width: 100%;
  margin-bottom: 8px;
}

.search-input {
  width: 300px;
  :deep(.el-input__wrapper) {
    border-radius: 6px;
    background: rgba(255, 255, 255, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow: none;
    transition: border-color 0.2s;
    &:hover, &.is-focus {
      border-color: #67aafc;
      box-shadow: none;
    }
  }
}

:deep(.el-radio-group) .el-radio-button__inner {
  font-size: 16px;
  padding: 8px 24px;
  border-radius: 24px !important;
  margin-right: 12px;
  margin-bottom: 4px;
  height: 40px;
  min-width: 60px;
  background: #f5f7fa;
  border: 1.5px solid #e3eaf1;
  transition: all 0.2s;
}

:deep(.el-radio-button.is-active) .el-radio-button__inner {
  background-color: #409eff !important;
  border-color: #409eff !important;
  color: #ffffff !important;
}

:deep(.el-tag) {
  border-radius: 6px;
  font-weight: 500;
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: none;
  background: rgba(255, 255, 255, 0.8);
}

.tools-list {
  margin-bottom: 30px;
}

.tool-card {
  margin-bottom: 24px;
  height: 20vh;
  border: none;
  border-radius: 16px;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  overflow: hidden;
  
  &:hover {
    transform: translateY(-8px);
    box-shadow: 0 16px 48px rgba(102, 126, 234, 0.2);
  }
  
  :deep(.el-card__header) {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    padding: 20px;
  }
  
  :deep(.el-card__body) {
    padding: 20px;
  }
}

.tool-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 12px;
}

.tool-name {
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  
  :deep(.el-tag) {
    border-radius: 6px;
    font-weight: 500;
    border: none;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
}

.tool-content {
  height: 7.5vw;
  max-height: 10%;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.tool-description {
  flex: 1;
  margin: 0;
  color: #606266;
  line-height: 1.6;
  font-size: 14px;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.tool-footer {
  display: flex;
  justify-content: space-between;
  color: #909399;
  font-size: 12px;
  font-weight: 500;
  padding-top: 12px;
  border-top: 1px solid rgba(102, 126, 234, 0.1);
  
  .tool-vendor, .tool-version {
    background: rgba(255, 255, 255, 0.5);
    padding: 4px 8px;
    border-radius: 4px;
  }
}

.pagination-container {
  position: fixed;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  justify-content: center;
  padding: 15px 25px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  z-index: 1000;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .app-container {
    padding: 20px;
  }
  
  .filter-container,
  .search-container {
    padding: 20px;
  }
  
  .search-input {
    width: 100%;
  }
  
  .tool-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .tool-footer {
    flex-direction: column;
    gap: 8px;
  }
}
</style> 