/**
 * 基于 Three.js 的 GLB/GLTF 模型查看器
 * 支持 GLB 和 GLTF 文件加载和预览
 * 使用 ESM import 方式引入 Three.js
 */

import * as THREE from 'three'
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader.js'
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js'

class ThreeModelViewer {
  constructor(container) {
    this.container = container
    this.scene = null
    this.camera = null
    this.renderer = null
    this.controls = null
    this.model = null
    this.mixer = null
    this.isLoaded = false
    this.isInitialized = false
    this.animationId = null
    
    // 初始化 Three.js 场景
    this.initPromise = this.initThreeJS()
  }

  // 初始化 Three.js 场景
  async initThreeJS() {
    try {
      console.log('🔧 初始化 Three.js 场景...')
      
      // 创建场景
      this.scene = new THREE.Scene()
      this.scene.background = new THREE.Color(0xf0f0f0) // 设置为中等灰色背景
      
      // 创建相机 - 调整近远平面以适应各种大小的模型
      const containerRect = this.container.getBoundingClientRect()
      let width = containerRect.width || this.container.clientWidth || this.container.offsetWidth
      let height = containerRect.height || this.container.clientHeight || this.container.offsetHeight
      
      // 如果容器尺寸仍然为0，使用默认尺寸
      if (width <= 0) width = 200
      if (height <= 0) height = 150
      
      console.log('📐 容器尺寸检查:', {
        getBoundingClientRect: { width: containerRect.width, height: containerRect.height },
        clientSize: { width: this.container.clientWidth, height: this.container.clientHeight },
        offsetSize: { width: this.container.offsetWidth, height: this.container.offsetHeight },
        finalSize: { width, height }
      })
      
      this.camera = new THREE.PerspectiveCamera(75, width / height, 0.001, 2000)
      this.camera.position.set(2, 2, 2)
      this.camera.lookAt(0, 0, 0)
      
      console.log('📷 相机初始设置:', {
        position: this.camera.position,
        fov: this.camera.fov,
        near: this.camera.near,
        far: this.camera.far
      })
      
      // 创建渲染器（不使用 alpha 通道以确保背景颜色显示）
      this.renderer = new THREE.WebGLRenderer({ antialias: true, alpha: false })
      this.renderer.setSize(width, height)
      this.renderer.setPixelRatio(window.devicePixelRatio)
      this.renderer.shadowMap.enabled = true
      this.renderer.shadowMap.type = THREE.PCFSoftShadowMap
      this.renderer.outputEncoding = THREE.sRGBEncoding
      
      // 明确设置渲染器的背景颜色（作为双重保险）
      this.renderer.setClearColor(0xf0f0f0, 1.0)
      
      // 添加到容器
      this.container.appendChild(this.renderer.domElement)
      
      // 添加轨道控制器
      this.controls = new OrbitControls(this.camera, this.renderer.domElement)
      this.controls.enableDamping = true
      this.controls.dampingFactor = 0.05
      this.controls.enableZoom = true
      this.controls.enablePan = true
      this.controls.enableRotate = true
      this.controls.autoRotate = false
      this.controls.autoRotateSpeed = 2.0
      this.controls.target.set(0, 0, 0)
      
      console.log('🎮 轨道控制器已初始化:', {
        hasTarget: !!this.controls.target,
        hasSetMethod: typeof this.controls.target.set === 'function',
        controlsType: this.controls.constructor.name
      })
      
      // 添加光照
      this.setupLighting()
      
      // 添加坐标轴辅助器（调试用）
      const axesHelper = new THREE.AxesHelper(1)
      this.scene.add(axesHelper)
      console.log('📐 坐标轴辅助器已添加')
      
      // 启动渲染循环
      this.startRenderLoop()
      
      // 处理窗口大小变化
      this.setupResizeHandler()
      
      this.isInitialized = true
      console.log('✅ Three.js 场景初始化完成')
      
      // 检查 Three.js 组件可用性
      this.checkThreeJSComponents()
      
    } catch (error) {
      console.error('❌ Three.js 场景初始化失败:', error)
      throw error
    }
  }

  // 检查 Three.js 组件可用性
  checkThreeJSComponents() {
    const components = {
      core: {
        Scene: !!THREE.Scene,
        Camera: !!THREE.PerspectiveCamera,
        WebGLRenderer: !!THREE.WebGLRenderer,
        Mesh: !!THREE.Mesh,
        Vector3: !!THREE.Vector3,
        Box3: !!THREE.Box3
      },
      geometry: {
        BoxGeometry: !!THREE.BoxGeometry,
        PlaneGeometry: !!THREE.PlaneGeometry,
        SphereGeometry: !!THREE.SphereGeometry
      },
      materials: {
        MeshBasicMaterial: !!THREE.MeshBasicMaterial,
        MeshLambertMaterial: !!THREE.MeshLambertMaterial,
        MeshPhongMaterial: !!THREE.MeshPhongMaterial
      },
      lights: {
        AmbientLight: !!THREE.AmbientLight,
        DirectionalLight: !!THREE.DirectionalLight
      },
      controls: {
        OrbitControls: !!OrbitControls
      },
      loaders: {
        GLTFLoader: !!GLTFLoader
      },
      helpers: {
        AxesHelper: !!THREE.AxesHelper
      }
    }
    
    console.log('🔍 Three.js 组件可用性检查:', components)
    
    // 检查缺失的重要组件
    const missingComponents = []
    if (!THREE.MeshBasicMaterial) missingComponents.push('MeshBasicMaterial')
    if (!THREE.BoxGeometry) missingComponents.push('BoxGeometry')
    if (!THREE.Mesh) missingComponents.push('Mesh')
    if (!GLTFLoader) missingComponents.push('GLTFLoader')
    if (!OrbitControls) missingComponents.push('OrbitControls')
    
    if (missingComponents.length > 0) {
      console.error('❌ 缺失重要的 Three.js 组件:', missingComponents)
    } else {
      console.log('✅ 所有 Three.js 组件都已正确加载!')
    }
  }

  // 设置光照
  setupLighting() {
    // 环境光
    const ambientLight = new THREE.AmbientLight(0xffffff, 0.8)
    this.scene.add(ambientLight)
    
    // 主光源
    const directionalLight1 = new THREE.DirectionalLight(0xffffff, 1)
    directionalLight1.position.set(5, 10, 7)
    directionalLight1.castShadow = true
    directionalLight1.shadow.mapSize.width = 2048
    directionalLight1.shadow.mapSize.height = 2048
    this.scene.add(directionalLight1)
    
    // 填充光
    const directionalLight2 = new THREE.DirectionalLight(0xffffff, 0.4)
    directionalLight2.position.set(-5, -5, -5)
    this.scene.add(directionalLight2)
  }

  // 启动渲染循环
  startRenderLoop() {
    const animate = () => {
      this.animationId = requestAnimationFrame(animate)
      
      // 更新控制器
      if (this.controls) {
        this.controls.update()
      }
      
      // 更新动画
      if (this.mixer) {
        this.mixer.update(0.016) // 假设60fps
      }
      
      // 渲染场景
      this.renderer.render(this.scene, this.camera)
    }
    
    animate()
  }

  // 设置窗口大小变化处理
  setupResizeHandler() {
    const handleResize = () => {
      this.updateRendererSize()
    }
    
    window.addEventListener('resize', handleResize)
    
    // 保存引用以便后续清理
    this.resizeHandler = handleResize
  }

  // 更新渲染器尺寸
  updateRendererSize() {
    if (!this.renderer || !this.camera) return
    
    const containerRect = this.container.getBoundingClientRect()
    let width = containerRect.width || this.container.clientWidth || this.container.offsetWidth
    let height = containerRect.height || this.container.clientHeight || this.container.offsetHeight
    
    // 如果容器尺寸仍然为0，使用当前渲染器尺寸或默认尺寸
    if (width <= 0) width = this.renderer.domElement.width || 200
    if (height <= 0) height = this.renderer.domElement.height || 150
    
    console.log('🔄 更新渲染器尺寸:', {
      container: { width: containerRect.width, height: containerRect.height },
      client: { width: this.container.clientWidth, height: this.container.clientHeight },
      offset: { width: this.container.offsetWidth, height: this.container.offsetHeight },
      final: { width, height }
    })
    
    this.camera.aspect = width / height
    this.camera.updateProjectionMatrix()
    this.renderer.setSize(width, height)
  }

  // 加载 GLB/GLTF 模型
  async loadModel(fileData, fileName) {
    try {
      console.log('📊 开始加载 3D 模型:', {
        fileName: fileName,
        dataSize: fileData.length,
        dataType: fileData.constructor.name
      })

      // 等待初始化完成
      if (!this.isInitialized) {
        console.log('⏳ 等待 Three.js 初始化完成...')
        await this.initPromise
      }

      // 检查文件类型
      const fileExtension = fileName.toLowerCase().split('.').pop()
      if (!['gltf', 'glb'].includes(fileExtension)) {
        throw new Error(`不支持的文件格式: ${fileExtension}。请使用 GLTF 或 GLB 文件。`)
      }

      // 清除之前的模型
      if (this.model) {
        this.scene.remove(this.model)
        this.model = null
      }

      // 创建 GLTFLoader
      const loader = new GLTFLoader()
      
      console.log('🔗 开始解析模型数据...')

      // 加载模型
      return new Promise((resolve, reject) => {
        const timeout = setTimeout(() => {
          reject(new Error('模型加载超时，请检查文件是否完整'))
        }, 30000) // 30秒超时

        try {
          // 使用 GLTFLoader.parse 解析二进制数据
          loader.parse(
            fileData.buffer || fileData, // 支持 ArrayBuffer 或 Uint8Array
            '', // 基础路径
            (gltf) => {
              clearTimeout(timeout)
              
              console.log('✅ 模型解析成功:', {
                scene: !!gltf.scene,
                animations: gltf.animations?.length || 0,
                cameras: gltf.cameras?.length || 0,
                scenes: gltf.scenes?.length || 0
              })
              
              // 添加模型到场景
              this.model = gltf.scene
              this.scene.add(this.model)
              
              console.log('🎬 模型已添加到场景:', {
                modelPosition: { 
                  x: this.model.position.x, 
                  y: this.model.position.y, 
                  z: this.model.position.z 
                },
                sceneChildren: this.scene.children.length,
                modelVisible: this.model.visible,
                modelMatrixAutoUpdate: this.model.matrixAutoUpdate
              })
              
              // 遍历模型的子对象来检查网格
              let meshCount = 0
              let materialCount = 0
              this.model.traverse((child) => {
                if (child.isMesh) {
                  meshCount++
                  if (child.material) {
                    materialCount++
                    // 确保材质可见
                    if (child.material.transparent) {
                      console.log('🔍 发现透明材质:', child.material.opacity)
                    }
                  }
                }
              })
              
              console.log('🧩 模型组件分析:', {
                meshCount: meshCount,
                materialCount: materialCount,
                hasGeometry: meshCount > 0
              })
              
              // 设置动画
              if (gltf.animations && gltf.animations.length > 0) {
                this.mixer = new THREE.AnimationMixer(this.model)
                gltf.animations.forEach(clip => {
                  this.mixer.clipAction(clip).play()
                })
                console.log('🎬 动画已设置:', gltf.animations.length, '个动画')
              }
              
              // 更新渲染器尺寸（以防容器尺寸在模型加载期间发生变化）
              this.updateRendererSize()
              
              // 自动调整相机位置
              this.fitModelToView()
              
              // 强制渲染一帧来确保模型显示
              if (this.renderer && this.scene && this.camera) {
                this.renderer.render(this.scene, this.camera)
                console.log('🎨 强制渲染一帧完成')
              }
              
              this.isLoaded = true
              console.log('✅ 模型加载完成，场景状态:', {
                sceneChildren: this.scene.children.length,
                cameraPosition: {
                  x: this.camera.position.x.toFixed(3),
                  y: this.camera.position.y.toFixed(3),
                  z: this.camera.position.z.toFixed(3)
                },
                renderSize: {
                  width: this.renderer.domElement.width,
                  height: this.renderer.domElement.height
                }
              })
              resolve(true)
            },
            (error) => {
              clearTimeout(timeout)
              console.error('❌ 模型解析失败:', error)
              
              // 提供友好的错误信息
              let errorMessage = '模型加载失败'
              if (error.message && error.message.includes('Invalid')) {
                errorMessage = '文件格式无效，请检查是否为有效的 GLB/GLTF 文件。'
              } else if (error.message && error.message.includes('parse')) {
                errorMessage = '文件解析失败，文件可能已损坏。'
              }
              
              reject(new Error(errorMessage))
            }
          )
        } catch (parseError) {
          clearTimeout(timeout)
          console.error('❌ 解析过程出错:', parseError)
          reject(new Error('模型文件解析失败，请检查文件格式。'))
        }
      })

    } catch (error) {
      console.error('❌ 加载 3D 模型失败:', error)
      throw error
    }
  }

  // 自动调整模型大小和位置
  fitModelToView() {
    if (!this.model) {
      console.warn('⚠️ 没有模型可以适应视图')
      return
    }
    
    console.log('📐 开始调整模型视图...')
    
    // 计算模型的包围盒
    const box = new THREE.Box3().setFromObject(this.model)
    const center = box.getCenter(new THREE.Vector3())
    const size = box.getSize(new THREE.Vector3())
    
    console.log('📊 模型信息:', {
      center: { x: center.x.toFixed(3), y: center.y.toFixed(3), z: center.z.toFixed(3) },
      size: { x: size.x.toFixed(3), y: size.y.toFixed(3), z: size.z.toFixed(3) },
      boundingBox: {
        min: { x: box.min.x.toFixed(3), y: box.min.y.toFixed(3), z: box.min.z.toFixed(3) },
        max: { x: box.max.x.toFixed(3), y: box.max.y.toFixed(3), z: box.max.z.toFixed(3) }
      }
    })
    
    // 检查模型是否太小（几乎没有尺寸）
    const maxDim = Math.max(size.x, size.y, size.z)
    if (maxDim < 0.001) {
      console.warn('⚠️ 模型太小，可能无法显示:', maxDim)
      // 为超小模型设置一个合理的视距
      this.camera.position.set(0.1, 0.1, 0.1)
      this.camera.lookAt(center)
      if (this.controls) {
        this.controls.target.copy(center)
        this.controls.update()
      }
      return
    }
    
    // 检查模型是否太大
    if (maxDim > 1000) {
      console.warn('⚠️ 模型很大:', maxDim)
    }
    
    // 计算合适的相机距离
    const fov = this.camera.fov * (Math.PI / 180) // 转换为弧度
    
    // 使用对角线长度来计算距离，确保整个模型都在视野内
    const diagonal = Math.sqrt(size.x * size.x + size.y * size.y + size.z * size.z)
    
    // 计算理论距离
    let distance = diagonal / (2 * Math.tan(fov / 2))
    
    // 添加安全边距（让模型不会填满整个视野）
    distance *= 1.8
    
    // 确保距离在合理范围内
    distance = Math.max(distance, maxDim * 0.5) // 最小距离
    distance = Math.min(distance, maxDim * 10)  // 最大距离
    
    console.log('📏 距离计算:', {
      maxDimension: maxDim.toFixed(3),
      diagonal: diagonal.toFixed(3),
      fov: this.camera.fov,
      calculatedDistance: distance.toFixed(3)
    })
    
    // 设置相机位置（从对角线方向观察）
    const direction = new THREE.Vector3(1, 1, 1).normalize()
    const cameraPosition = center.clone().add(direction.multiplyScalar(distance))
    
    this.camera.position.copy(cameraPosition)
    this.camera.lookAt(center)
    
    console.log('📷 相机设置:', {
      position: { 
        x: this.camera.position.x.toFixed(3), 
        y: this.camera.position.y.toFixed(3), 
        z: this.camera.position.z.toFixed(3) 
      },
      lookingAt: { 
        x: center.x.toFixed(3), 
        y: center.y.toFixed(3), 
        z: center.z.toFixed(3) 
      },
      distance: this.camera.position.distanceTo(center).toFixed(3)
    })
    
    // 更新相机的近远平面，确保模型在可见范围内
    const distanceToCenter = this.camera.position.distanceTo(center)
    this.camera.near = Math.max(0.001, distanceToCenter - diagonal)
    this.camera.far = Math.max(this.camera.near * 2, distanceToCenter + diagonal * 2)
    this.camera.updateProjectionMatrix()
    
    console.log('🔍 相机平面更新:', {
      near: this.camera.near.toFixed(3),
      far: this.camera.far.toFixed(3)
    })
    
    // 更新轨道控制器
    if (this.controls) {
      this.controls.target.copy(center)
      this.controls.enableZoom = true
      this.controls.enablePan = true
      this.controls.enableRotate = true
      
      // 设置缩放限制
      this.controls.minDistance = distance * 0.1
      this.controls.maxDistance = distance * 5
      
      this.controls.update()
      
      console.log('🎮 控制器更新:', {
        target: { 
          x: this.controls.target.x.toFixed(3), 
          y: this.controls.target.y.toFixed(3), 
          z: this.controls.target.z.toFixed(3) 
        },
        minDistance: this.controls.minDistance?.toFixed(3),
        maxDistance: this.controls.maxDistance?.toFixed(3)
      })
    }
    
    console.log('✅ 模型视图调整完成')
  }

  // 重置视角
  async resetView() {
    if (!this.isInitialized) {
      await this.initPromise
    }
    
    if (this.model) {
      this.fitModelToView()
      console.log('🔄 视角已重置')
    }
  }

  // 适应窗口
  async fitView() {
    if (!this.isInitialized) {
      await this.initPromise
    }
    
    if (this.model) {
      this.fitModelToView()
      console.log('📐 视图已适应窗口')
    }
  }

  // 切换自动旋转
  async toggleAutoRotate() {
    if (!this.isInitialized) {
      await this.initPromise
    }
    
    if (this.controls) {
      this.controls.autoRotate = !this.controls.autoRotate
      console.log('🔄 自动旋转状态:', this.controls.autoRotate ? '开启' : '关闭')
      return this.controls.autoRotate
    }
    
    return false
  }

  // 获取模型信息
  getModelInfo() {
    if (this.model && this.isLoaded) {
      const box = new THREE.Box3().setFromObject(this.model)
      const size = box.getSize(new THREE.Vector3())
      
      return {
        loaded: this.isLoaded,
        vertices: this.getVertexCount(),
        triangles: this.getTriangleCount(),
        size: {
          x: size.x.toFixed(2),
          y: size.y.toFixed(2),
          z: size.z.toFixed(2)
        },
        hasAnimations: !!this.mixer,
        animationCount: this.mixer ? this.mixer._actions.length : 0
      }
    }
    return { loaded: false }
  }

  // 获取顶点数量
  getVertexCount() {
    let count = 0
    this.model?.traverse((child) => {
      if (child.isMesh && child.geometry) {
        count += child.geometry.attributes.position?.count || 0
      }
    })
    return count
  }

  // 获取三角形数量
  getTriangleCount() {
    let count = 0
    this.model?.traverse((child) => {
      if (child.isMesh && child.geometry) {
        const positions = child.geometry.attributes.position?.count || 0
        count += Math.floor(positions / 3)
      }
    })
    return count
  }

  // 销毁查看器
  destroy() {
    // 停止动画循环
    if (this.animationId) {
      cancelAnimationFrame(this.animationId)
      this.animationId = null
    }
    
    // 停止动画混合器
    if (this.mixer) {
      this.mixer.stopAllAction()
      this.mixer = null
    }
    
    // 清理场景
    if (this.scene) {
      this.scene.clear()
    }
    
    // 清理渲染器
    if (this.renderer) {
      this.renderer.dispose()
      if (this.container.contains(this.renderer.domElement)) {
        this.container.removeChild(this.renderer.domElement)
      }
    }
    
    // 清理控制器
    if (this.controls) {
      this.controls.dispose()
    }
    
    // 清理窗口事件监听器
    if (this.resizeHandler) {
      window.removeEventListener('resize', this.resizeHandler)
    }
    
    // 重置状态
    this.scene = null
    this.camera = null
    this.renderer = null
    this.controls = null
    this.model = null
    this.mixer = null
    this.isLoaded = false
    this.isInitialized = false
    
    console.log('🗑️ Three.js 查看器已销毁')
  }


}

export default ThreeModelViewer 