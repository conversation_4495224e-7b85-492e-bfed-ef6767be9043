from datetime import datetime
from pydantic import BaseModel, ConfigDict, Field
from pydantic.alias_generators import to_camel
from pydantic_validation_decorator import NotBlank
from typing import Optional
from module_admin.annotation.pydantic_annotation import as_query




class TasksModel(BaseModel):
    """
    任务信息管理表对应pydantic模型
    """
    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True, populate_by_name=True)

    task_id: Optional[int] = Field(default=None, description='')
    project_id: Optional[int] = Field(default=None, description='')
    task_name: Optional[str] = Field(default=None, description='')
    type_id: Optional[int] = Field(default=None, description='引用system_types')
    description: Optional[str] = Field(default=None, description='')
    assigned_to: Optional[int] = Field(default=None, description='')
    status: Optional[str] = Field(default=None, description='')
    progress: Optional[int] = Field(default=None, description='')
    tool_id: Optional[int] = Field(default=None, description='')
    parameters: Optional[dict] = Field(default=None, description='')
    parameters_md5: Optional[str] = Field(default=None, description='参数MD5值，用于历史任务匹配')
    created_at: Optional[datetime] = Field(default=None, description='')
    updated_at: Optional[datetime] = Field(default=None, description='')
    started_at: Optional[datetime] = Field(default=None, description='')
    completed_at: Optional[datetime] = Field(default=None, description='')
    is_deleted: Optional[int] = Field(default=0, description='')
    average_execution_time: Optional[int] = Field(default=None, description='工具平均执行时间（动态更新）')
    tool_queue_required: Optional[bool] = Field(default=True, description='工具是否需要队列')
    creator_name: Optional[str] = Field(default=None, description='任务创建者姓名')
    tool_name: Optional[str] = Field(default=None, description='工具名称')

    @NotBlank(field_name='project_id', message='项目id不能为空')
    def get_project_id(self):
        return self.project_id

    @NotBlank(field_name='task_name', message='任务名称不能为空')
    def get_task_name(self):
        return self.task_name

    @NotBlank(field_name='type_id', message='引用sys_types不能为空')
    def get_type_id(self):
        return self.type_id


    def validate_fields(self):
        self.get_project_id()
        self.get_task_name()
        self.get_type_id()




class TasksQueryModel(TasksModel):
    """
    任务信息管理不分页查询模型
    """
    pass


@as_query
class TasksPageQueryModel(TasksQueryModel):
    """
    任务信息管理分页查询模型
    """

    page_num: int = Field(default=1, description='当前页码')
    page_size: int = Field(default=10, description='每页记录数')


class DeleteTasksModel(BaseModel):
    """
    删除任务信息管理模型
    """

    model_config = ConfigDict(alias_generator=to_camel)

    task_ids: str = Field(description='需要删除的')
