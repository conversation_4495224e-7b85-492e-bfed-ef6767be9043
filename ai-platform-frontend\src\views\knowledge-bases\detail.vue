<template>
  <div class="app-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>{{ knowledgeBase?.kbName || knowledgeBase?.kb_name || '未命名数据库' }}内容看板</h2>
    </div>

    <!-- 数据库概述 -->
    <el-card class="overview-card" shadow="never">
      <template #header>
        <div class="card-header">
          <span class="card-title">数据库概述</span>
        </div>
      </template>
      <div class="overview-content">
        <p class="description">{{ knowledgeBase?.description || '暂无描述' }}</p>
      </div>
    </el-card>

    <!-- 文件统计 -->
    <el-card class="stats-card" shadow="never">
      <div class="stats-grid">
        <div class="stat-item" v-for="(count, type) in fileStats" :key="type">
          <div class="stat-icon">
            <el-icon :size="24">
              <component :is="getFileTypeIcon(type)" />
            </el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-label">{{ getFileTypeLabel(type) }}</div>
            <div class="stat-value">{{ count }}</div>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 左侧文件列表 -->
      <div class="files-section">
        <el-card class="files-card" shadow="never">
          <template #header>
            <div class="card-header">
              <span class="card-title">文件管理</span>
              <div class="view-toggle">
                <el-button-group>
                  <el-button
                    size="small"
                    :type="!showGroupedView ? 'primary' : ''"
                    @click="showGroupedView = false"
                  >
                    按类型查看
                  </el-button>
                  <el-button
                    size="small"
                    :type="showGroupedView ? 'primary' : ''"
                    @click="toggleGroupedView"
                  >
                    按任务查看
                  </el-button>
                </el-button-group>
              </div>
            </div>
          </template>
          
          <!-- 文件类型标签 -->
          <div class="file-tabs">
            <el-tabs v-model="activeFileType" @tab-click="handleFileTypeChange">
              <el-tab-pane label="虚拟实验数据" name="virtual">
                <el-tabs v-model="activeVirtualType" class="sub-tabs">
                  <el-tab-pane label="模型文件" name="model">
                    <file-list
                      :files="filesByType.model"
                      :grouped-files="groupedFilesByType.model"
                      :show-grouped="showGroupedView"
                      @download="handleDownload"
                      @delete="handleDelete"
                    />
                  </el-tab-pane>
                  <el-tab-pane label="报告文件" name="report">
                    <file-list
                      :files="filesByType.report"
                      :grouped-files="groupedFilesByType.report"
                      :show-grouped="showGroupedView"
                      @download="handleDownload"
                      @delete="handleDelete"
                    />
                  </el-tab-pane>
                  <el-tab-pane label="数据表" name="dataset">
                    <file-list
                      :files="filesByType.dataset"
                      :grouped-files="groupedFilesByType.dataset"
                      :show-grouped="showGroupedView"
                      @download="handleDownload"
                      @delete="handleDelete"
                    />
                  </el-tab-pane>
                  <el-tab-pane label="日志文件" name="log">
                    <file-list
                      :files="filesByType.log"
                      :grouped-files="groupedFilesByType.log"
                      :show-grouped="showGroupedView"
                      @download="handleDownload"
                      @delete="handleDelete"
                    />
                  </el-tab-pane>
                  <el-tab-pane label="文档文件" name="document">
                    <file-list
                      :files="filesByType.document"
                      :grouped-files="groupedFilesByType.document"
                      :show-grouped="showGroupedView"
                      @download="handleDownload"
                      @delete="handleDelete"
                    />
                  </el-tab-pane>
                  <el-tab-pane label="归档文件" name="archived">
                    <file-list
                      :files="filesByType.archived"
                      :grouped-files="groupedFilesByType.archived"
                      :show-grouped="showGroupedView"
                      @download="handleDownload"
                      @delete="handleDelete"
                    />
                  </el-tab-pane>
                </el-tabs>
              </el-tab-pane>
              <el-tab-pane label="试验采集数据" name="experimental" disabled>
                <div class="disabled-content">
                  <el-empty description="待开发功能" />
                </div>
              </el-tab-pane>
            </el-tabs>
          </div>
        </el-card>
      </div>

      <!-- 右侧数据库信息 -->
      <div class="info-section">
        <el-card class="info-card" shadow="never">
          <template #header>
            <div class="card-header">
              <span class="card-title">数据库信息</span>
            </div>
          </template>
          
          <div class="info-content">
            <div class="info-item">
              <span class="info-label">数据库名称:</span>
              <span class="info-value">{{ knowledgeBase?.kbName || knowledgeBase?.kb_name || '未命名' }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">数据库类型:</span>
              <span class="info-value">
                <el-tag
                  v-for="typeName in knowledgeBase?.typeNames || knowledgeBase?.type_names || []"
                  :key="typeName"
                  size="small"
                  type="info"
                  class="type-tag"
                >
                  {{ typeName }}
                </el-tag>
                <el-tag
                  v-if="knowledgeBase?.project_id === null"
                  size="small"
                  type="success"
                  class="type-tag"
                >
                  通用
                </el-tag>
              </span>
            </div>
            <div class="info-item">
              <span class="info-label">创建时间:</span>
              <span class="info-value">{{ formatDate(knowledgeBase?.createdAt || knowledgeBase?.created_at) }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">上次更新时间:</span>
              <span class="info-value">{{ formatDate(knowledgeBase?.updatedAt || knowledgeBase?.updated_at) }}</span>
            </div>
          </div>

          <div class="action-buttons">
            <el-button type="primary" @click="handleDownloadDatabase" :loading="downloadLoading">
              <el-icon><Download /></el-icon>
              下载当前数据库
            </el-button>
            <el-button type="warning" @click="handleEditDatabase">
              <el-icon><Edit /></el-icon>
              修改数据库信息
            </el-button>
            <el-button type="success" @click="handleUploadFile">
              <el-icon><Upload /></el-icon>
              上传新文件
            </el-button>
          </div>
        </el-card>
      </div>
    </div>

    <!-- 编辑数据库弹窗 -->
    <el-dialog v-model="showEditDialog" title="修改数据库信息" width="500px">
      <el-form :model="editForm" label-width="100px">
        <el-form-item label="数据库名称">
          <el-input v-model="editForm.kbName" />
        </el-form-item>
        <el-form-item label="数据库描述">
          <el-input v-model="editForm.description" type="textarea" :rows="3" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showEditDialog = false">取消</el-button>
        <el-button type="primary" @click="submitEdit" :loading="editLoading">确定</el-button>
      </template>
    </el-dialog>

    <!-- 上传文件弹窗 -->
    <el-dialog v-model="showUploadDialog" title="上传新文件" width="500px" @close="handleUploadDialogClose">
      <el-form :model="uploadForm" label-width="100px">
        <el-form-item label="文件类型">
          <el-select v-model="uploadForm.fileType" placeholder="请选择文件类型">
            <el-option label="模型文件" value="model" />
            <el-option label="报告文件" value="report" />
            <el-option label="数据表" value="dataset" />
            <el-option label="日志文件" value="log" />
            <el-option label="文档文件" value="document" />
            <el-option label="存档文件" value="archived" />
          </el-select>
        </el-form-item>
        <el-form-item label="选择文件">
          <el-upload
            ref="uploadRef"
            :auto-upload="false"
            :on-change="handleFileChange"
            :limit="1"
            accept="*/*"
          >
            <el-button type="primary">选择文件</el-button>
          </el-upload>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="handleUploadDialogClose">取消</el-button>
        <el-button type="primary" @click="submitUpload" :loading="uploadLoading">上传</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Download,
  Edit,
  Upload,
  Document,
  Files,
  DataAnalysis,
  DocumentCopy,
  Collection,
  FolderOpened
} from '@element-plus/icons-vue'
import { getKnowledgeBase, getKnowledgeBaseWithTypes, updateKnowledgeBase } from '@/api/knowledge-bases'
import { getFileStatsByKb } from '@/api/knowledge-bases'
import { getFilesByKbAndType, getFilesGroupedByTask, downloadFile, deleteFile, uploadFile } from '@/api/files'
import FileList from './components/FileList.vue'

const route = useRoute()
const router = useRouter()

// 响应式数据
const knowledgeBase = ref(null)
const fileStats = ref({})
const filesByType = ref({
  model: [],
  report: [],
  dataset: [],
  log: [],
  document: [],
  archived: []
})
const groupedFilesByType = ref({
  model: {},
  report: {},
  dataset: {},
  log: {},
  document: {},
  archived: {}
})
const showGroupedView = ref(false)
const activeFileType = ref('virtual')
const showEditDialog = ref(false)
const showUploadDialog = ref(false)
const editForm = ref({ kbName: '', description: '' })
const uploadForm = ref({ fileType: '', file: null })
const downloadLoading = ref(false)
const editLoading = ref(false)
const uploadLoading = ref(false)
const uploadRef = ref()

// 子标签页变量
const activeVirtualType = ref('model')
const activeExperimentalType = ref('model')

// 获取数据库详情
const fetchKnowledgeBase = async () => {
  try {
    const kbId = route.params.id
    const res = await getKnowledgeBaseWithTypes(kbId)
    if (res.code === 200) {
      knowledgeBase.value = res.data
    }
  } catch (error) {
    console.error('获取数据库详情失败:', error)
    ElMessage.error('获取数据库详情失败')
  }
}

// 获取文件统计
const fetchFileStats = async () => {
  try {
    const kbId = route.params.id
    const res = await getFileStatsByKb(kbId)
    if (res.code === 200) {
      fileStats.value = res.data
    }
  } catch (error) {
    console.error('获取文件统计失败:', error)
    ElMessage.error('获取文件统计失败')
  }
}

// 获取文件列表
const fetchFiles = async () => {
  try {
    const kbId = route.params.id
    const fileTypes = ['model', 'report', 'dataset', 'log', 'document', 'archived']

    for (const fileType of fileTypes) {
      const res = await getFilesByKbAndType(kbId, fileType)
      if (res.code === 200) {
        filesByType.value[fileType] = res.data || []
      }
    }
  } catch (error) {
    console.error('获取文件列表失败:', error)
    ElMessage.error('获取文件列表失败')
  }
}

// 获取分组文件列表
const fetchGroupedFiles = async () => {
  try {
    const kbId = route.params.id
    const fileTypes = ['model', 'report', 'dataset', 'log', 'document', 'archived']

    for (const fileType of fileTypes) {
      const res = await getFilesGroupedByTask(kbId, fileType)
      if (res.code === 200) {
        groupedFilesByType.value[fileType] = res.data || {}
      }
    }
  } catch (error) {
    console.error('获取分组文件列表失败:', error)
    ElMessage.error('获取分组文件列表失败')
  }
}

// 文件类型图标映射
const getFileTypeIcon = (type) => {
  const iconMap = {
    model: Document,
    report: Files,
    dataset: DataAnalysis,
    log: DocumentCopy,
    document: Collection,
    archived: FolderOpened
  }
  return iconMap[type] || Document
}

// 文件类型标签映射
const getFileTypeLabel = (type) => {
  const labelMap = {
    model: '模型',
    report: '报告',
    dataset: '数据表',
    log: '日志',
    document: '文档',
    archived: '存档'
  }
  return labelMap[type] || type
}

// 格式化日期
const formatDate = (dateStr) => {
  if (!dateStr) return '未知'
  return new Date(dateStr).toLocaleString('zh-CN')
}

// 处理文件类型切换
const handleFileTypeChange = () => {
  // 可以在这里添加额外的逻辑
}

// 切换分组视图
const toggleGroupedView = async () => {
  showGroupedView.value = true
  if (Object.keys(groupedFilesByType.value.model).length === 0) {
    await fetchGroupedFiles()
  }
}

// 处理文件下载
const handleDownload = async (fileId) => {
  try {
    // 根据当前激活的标签页获取文件类型
    let currentFileType = 'model'
    if (activeFileType.value === 'virtual') {
      currentFileType = activeVirtualType.value
    } else if (activeFileType.value === 'experimental') {
      currentFileType = activeExperimentalType.value
    }
    
    // 先获取文件信息
    const fileInfo = filesByType.value[currentFileType].find(
      file => (file.fileId || file.file_id) === fileId
    )
    
    const res = await downloadFile(fileId)
    const blob = new Blob([res])
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = fileInfo?.originalName || fileInfo?.original_name || 'file'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)
    ElMessage.success('文件下载成功')
  } catch (error) {
    console.error('文件下载失败:', error)
    ElMessage.error('文件下载失败')
  }
}

// 处理文件删除
const handleDelete = async (fileId) => {
  try {
    await ElMessageBox.confirm('确定要删除这个文件吗？', '提示', {
      type: 'warning'
    })
    
    const res = await deleteFile(fileId)
    if (res.code === 200) {
      ElMessage.success('文件删除成功')
      await fetchFiles()
      await fetchFileStats()
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('文件删除失败:', error)
      ElMessage.error('文件删除失败')
    }
  }
}

// 处理下载数据库
const handleDownloadDatabase = async () => {
  downloadLoading.value = true
  try {
    // 这里可以实现下载整个数据库的逻辑
    ElMessage.info('数据库下载功能待实现')
  } catch (error) {
    console.error('数据库下载失败:', error)
    ElMessage.error('数据库下载失败')
  } finally {
    downloadLoading.value = false
  }
}

// 处理编辑数据库
const handleEditDatabase = () => {
  editForm.value = {
    kbName: knowledgeBase.value?.kbName || knowledgeBase.value?.kb_name || '',
    description: knowledgeBase.value?.description || ''
  }
  showEditDialog.value = true
}

// 提交编辑
const submitEdit = async () => {
  if (!editForm.value.kbName) {
    ElMessage.warning('数据库名称不能为空')
    return
  }
  
  editLoading.value = true
  try {
    const kbId = route.params.id
    const res = await updateKnowledgeBase({
      kbId: parseInt(kbId),
      ...editForm.value
    })
    if (res.code === 200) {
      ElMessage.success('修改成功')
      showEditDialog.value = false
      await fetchKnowledgeBase()
    }
  } catch (error) {
    console.error('修改数据库失败:', error)
    ElMessage.error('修改失败')
  } finally {
    editLoading.value = false
  }
}

// 处理上传文件
const handleUploadFile = () => {
  uploadForm.value = { fileType: '', file: null }
  showUploadDialog.value = true
}

// 处理文件选择
const handleFileChange = (file) => {
  uploadForm.value.file = file.raw
}

// 处理弹窗关闭
const handleUploadDialogClose = () => {
  uploadForm.value = { fileType: '', file: null }
  // 清空上传组件的文件列表
  if (uploadRef.value) {
    uploadRef.value.clearFiles()
  }
}

// 提交上传
const submitUpload = async () => {
  if (!uploadForm.value.fileType) {
    ElMessage.warning('请选择文件类型')
    return
  }
  if (!uploadForm.value.file) {
    ElMessage.warning('请选择文件')
    return
  }
  
  uploadLoading.value = true
  try {
    const kbId = route.params.id
    const res = await uploadFile(kbId, uploadForm.value.fileType, uploadForm.value.file)
    if (res.code === 200) {
      ElMessage.success('文件上传成功')
      showUploadDialog.value = false
      // 重置表单和上传组件
      uploadForm.value = { fileType: '', file: null }
      if (uploadRef.value) {
        uploadRef.value.clearFiles()
      }
      await fetchFiles()
      await fetchFileStats()
      // 刷新分组数据
      if (showGroupedView.value) {
        await fetchGroupedFiles()
      }
    }
  } catch (error) {
    console.error('文件上传失败:', error)
    ElMessage.error('文件上传失败')
  } finally {
    uploadLoading.value = false
  }
}

// 页面加载时获取数据
onMounted(async () => {
  await Promise.all([
    fetchKnowledgeBase(),
    fetchFileStats(),
    fetchFiles(),
    fetchGroupedFiles()
  ])
})
</script>

<style scoped>
.app-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.overview-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.view-toggle {
  display: flex;
  align-items: center;
}

.view-toggle .el-button-group .el-button {
  font-size: 12px;
  padding: 6px 12px;
}

.overview-content {
  padding: 16px 0;
}

.description {
  margin: 0;
  color: #606266;
  line-height: 1.6;
  font-size: 14px;
}

.stats-card {
  margin-bottom: 20px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: 20px;
  padding: 20px 0;
}

.stat-item {
  display: flex;
  align-items: center;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  color: white;
  transition: transform 0.3s ease;
}

.stat-item:hover {
  transform: translateY(-2px);
}

.stat-icon {
  margin-right: 12px;
}

.stat-content {
  flex: 1;
}

.stat-label {
  font-size: 12px;
  opacity: 0.9;
  margin-bottom: 4px;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
}

.main-content {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 20px;
}

.files-card,
.info-card {
  height: fit-content;
}

.file-tabs {
  margin-top: 16px;
}

.disabled-content {
  padding: 40px 0;
  text-align: center;
}

.info-content {
  margin-bottom: 24px;
}

.info-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 16px;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.info-item:last-child {
  border-bottom: none;
}

.info-label {
  font-weight: 500;
  color: #606266;
  min-width: 120px;
  margin-right: 12px;
}

.info-value {
  color: #303133;
  flex: 1;
}

.type-tag {
  margin-right: 8px;
  margin-bottom: 4px;
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.action-buttons .el-button {
  width: 100%;
  justify-content: flex-start;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .main-content {
    grid-template-columns: 1fr;
  }
  
  .stats-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .info-item {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .info-label {
    margin-bottom: 8px;
  }
}

.sub-tabs {
  margin-top: 16px;
  height: 500px;
  display: flex;
  flex-direction: column;
}

.sub-tabs .el-tabs__header {
  margin-bottom: 16px;
  flex-shrink: 0;
}

.sub-tabs .el-tabs__nav-wrap {
  padding-left: 0;
}

.sub-tabs .el-tabs__item {
  font-size: 14px;
  padding: 0 16px;
}

.sub-tabs .el-tabs__content {
  flex: 1;
  overflow: hidden;
}

.sub-tabs .el-tab-pane {
  height: 100%;
  overflow: hidden;
}
</style> 