<template>
  <div class="chat-container">
    <!-- 头部 -->
    <div class="chat-header">
      <div class="header-left">
        <el-button @click="goBack" icon="ArrowLeft" type="text">返回首页</el-button>
        <h2 class="chat-title">AI 对话</h2>
        <!-- 连接状态指示器 -->
        <div class="connection-status">
          <el-tag 
            :type="isConnected ? 'success' : 'danger'"
            size="small"
            class="status-tag"
          >
            {{ isConnected ? '已连接' : '未连接' }}
          </el-tag>
        </div>
      </div>
      <div class="header-right">
        <el-button @click="clearHistory" type="danger" size="small">清空历史</el-button>
      </div>
    </div>

    <!-- 对话内容区域 -->
    <div class="chat-content" ref="chatContentRef">
      <div v-if="chatHistory.length === 0" class="empty-state">
        <el-empty description="暂无对话记录">
          <el-button type="primary" @click="goBack">开始新对话</el-button>
        </el-empty>
      </div>
      
      <div v-else class="messages-container">
        <div 
          v-for="(message, index) in chatHistory" 
          :key="index"
          :class="['message-item', message.type]"
        >
          <div class="message-avatar">
            <el-avatar 
              :icon="message.type === 'user' ? 'User' : 'Service'" 
              :size="40"
              :style="{ backgroundColor: message.type === 'user' ? '#409eff' : '#67c23a' }"
            />
          </div>
          <div class="message-content">
            <div class="message-header">
              <span class="message-sender">{{ message.type === 'user' ? '用户' : 'AI助手' }}</span>
              <span class="message-time">{{ formatTime(message.timestamp) }}</span>
            </div>
            <div class="message-text">{{ message.content }}</div>
            
            <!-- 文件附件显示 -->
            <div v-if="message.files && message.files.length > 0" class="message-files">
              <div v-for="file in message.files" :key="file.name" class="file-item">
                <el-icon><Document /></el-icon>
                <span class="file-name">{{ file.name }}</span>
                <span class="file-size">{{ formatFileSize(file.size) }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 输入区域 -->
    <div class="chat-input-area">
      <div class="input-wrapper">
        <el-input
          v-model="inputMessage"
          type="textarea"
          :rows="3"
          placeholder="请输入您的问题..."
          class="message-input"
          @keydown.enter.prevent="handleEnterKey"
          ref="inputRef"
        />
        
        <!-- 历史记录下拉菜单 -->
        <div v-if="showHistory && historyRecords.length > 0" class="history-dropdown">
          <div
            v-for="(record, index) in filteredHistory"
            :key="index"
            class="history-item"
            @click="selectHistory(record)"
          >
            <el-icon><ChatDotRound /></el-icon>
            <span class="history-text">{{ record }}</span>
          </div>
        </div>
      </div>

      <div class="input-actions">
        <el-button 
          type="primary" 
          @click="sendMessage"
          :disabled="!inputMessage.trim()"
          :loading="sending"
          class="send-btn"
        >
          发送
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, nextTick, watch, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Document, ChatDotRound } from '@element-plus/icons-vue'
import { submitChat, getChatHistory, ChatWebSocket } from '@/api/chat'

const router = useRouter()

// 响应式数据
const inputMessage = ref('')
const showHistory = ref(false)
const sending = ref(false)
const chatHistory = ref([])
const historyRecords = ref([])
const isConnected = ref(false)
const connectionStatus = ref('disconnected')

// 组件引用
const inputRef = ref(null)
const chatContentRef = ref(null)

// WebSocket连接
let chatWebSocket = null

// 计算属性
const filteredHistory = computed(() => {
  if (!inputMessage.value) return historyRecords.value.slice(0, 5)
  return historyRecords.value
    .filter(record => record.toLowerCase().includes(inputMessage.value.toLowerCase()))
    .slice(0, 5)
})

// 方法
const goBack = () => {
  router.push('/index')
}

const clearHistory = async () => {
  try {
    await ElMessageBox.confirm('确定要清空所有对话历史吗？', '确认清空', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    chatHistory.value = []
    localStorage.removeItem('chatHistory')
    localStorage.removeItem('chatMessages')
    ElMessage.success('对话历史已清空')
  } catch {
    // 用户取消
  }
}

const handleEnterKey = (e) => {
  if (e.shiftKey) {
    // Shift + Enter 换行
    return
  }
  // Enter 发送
  sendMessage()
}

const selectHistory = (record) => {
  inputMessage.value = record
  showHistory.value = false
}

const sendMessage = async () => {
  if (!inputMessage.value.trim() || sending.value) return
  
  const userMessage = inputMessage.value.trim()
  
  // 添加用户消息
  addMessage('user', userMessage)
  
  // 清空输入框
  inputMessage.value = ''
  showHistory.value = false
  
  // 保存到历史记录
  if (!historyRecords.value.includes(userMessage)) {
    historyRecords.value.unshift(userMessage)
    if (historyRecords.value.length > 20) {
      historyRecords.value = historyRecords.value.slice(0, 20)
    }
    localStorage.setItem('chatHistory', JSON.stringify(historyRecords.value))
  }
  
  // 使用WebSocket发送消息
  sending.value = true
  try {
    if (chatWebSocket && chatWebSocket.isConnected) {
      // 通过WebSocket发送
      const success = chatWebSocket.sendChatMessage(userMessage)
      if (!success) {
        throw new Error('WebSocket发送失败')
      }
    } else {
      // 回退到HTTP API
    const response = await submitChat({
      message: userMessage,
      type: 'chat'
    })
    
    if (response.code === 200) {
      // 添加AI回复
      const aiResponse = response.data?.reply || generateAIResponse(userMessage)
      addMessage('ai', aiResponse)
    } else {
      ElMessage.error(response.msg || '发送失败')
      }
    }
    
  } catch (error) {
    ElMessage.error('发送失败，请重试')
    // 如果API调用失败，使用模拟回复
    const aiResponse = generateAIResponse(userMessage)
    addMessage('ai', aiResponse)
  } finally {
    sending.value = false
  }
}

const addMessage = (type, content, files = []) => {
  const message = {
    type,
    content,
    files,
    timestamp: new Date().toISOString()
  }
  
  chatHistory.value.push(message)
  
  // 保存到本地存储
  localStorage.setItem('chatMessages', JSON.stringify(chatHistory.value))
  
  // 滚动到底部
  nextTick(() => {
    scrollToBottom()
  })
}

const generateAIResponse = (userMessage) => {
  // 简单的AI回复逻辑
  const responses = [
    '我理解您的问题，让我为您详细解答...',
    '这是一个很好的问题，根据我的分析...',
    '基于您提供的信息，我建议...',
    '我可以帮您解决这个问题，具体方案如下...',
    '感谢您的提问，让我为您提供专业的建议...'
  ]
  
  return responses[Math.floor(Math.random() * responses.length)] + 
         '（这是模拟回复，实际应用中会调用真实的AI接口）'
}

// WebSocket连接管理
const connectWebSocket = () => {
  try {
    chatWebSocket = new ChatWebSocket(
      null, // sessionId，让服务器自动生成
      handleWebSocketMessage,
      handleWebSocketError,
      handleWebSocketClose
    )
    chatWebSocket.connect()
  } catch (error) {
    console.error('创建WebSocket连接失败:', error)
    ElMessage.error('WebSocket连接失败，将使用HTTP模式')
  }
}

const handleWebSocketMessage = (data) => {
  console.log('收到WebSocket消息:', data)
  
  switch (data.type) {
    case 'connection':
      isConnected.value = true
      connectionStatus.value = 'connected'
      ElMessage.success('WebSocket连接成功')
      break
      
    case 'user_message':
      // 用户消息确认，不需要额外处理
      break
      
    case 'ai_response':
      // AI回复
      addMessage('ai', data.message)
      sending.value = false
      break
      
    case 'system':
      ElMessage.info(data.message)
      break
      
    case 'error':
      ElMessage.error(data.message)
      sending.value = false
      break
      
    case 'pong':
      // 心跳响应，不需要处理
      break
      
    default:
      console.log('未知消息类型:', data.type)
  }
}

const handleWebSocketError = (error) => {
  console.error('WebSocket错误:', error)
  isConnected.value = false
  connectionStatus.value = 'error'
  ElMessage.error('WebSocket连接错误')
}

const handleWebSocketClose = (event) => {
  console.log('WebSocket连接关闭:', event)
  isConnected.value = false
  connectionStatus.value = 'disconnected'
  ElMessage.warning('WebSocket连接已断开')
}

const scrollToBottom = () => {
  if (chatContentRef.value) {
    chatContentRef.value.scrollTop = chatContentRef.value.scrollHeight
  }
}

const formatTime = (timestamp) => {
  const date = new Date(timestamp)
  return date.toLocaleString('zh-CN', {
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 加载历史数据
const loadHistory = () => {
  try {
    const savedHistory = localStorage.getItem('chatHistory')
    if (savedHistory) {
      historyRecords.value = JSON.parse(savedHistory)
    }
    
    const savedMessages = localStorage.getItem('chatMessages')
    if (savedMessages) {
      chatHistory.value = JSON.parse(savedMessages)
    }
  } catch (error) {
    console.error('加载历史数据失败:', error)
  }
}

// 点击外部关闭历史记录
const handleClickOutside = (event) => {
  if (inputRef.value && !inputRef.value.$el.contains(event.target)) {
    showHistory.value = false
  }
}

// 监听输入变化
watch(inputMessage, (newValue) => {
  showHistory.value = newValue.length > 0
})

// 生命周期
onMounted(() => {
  loadHistory()
  
  // 添加点击外部关闭历史记录的事件监听
  document.addEventListener('click', handleClickOutside)
  
  // 建立WebSocket连接
  connectWebSocket()
  
  // 滚动到底部
  nextTick(() => {
    scrollToBottom()
  })
})

// 组件卸载时移除事件监听和断开WebSocket连接
onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
  
  // 断开WebSocket连接
  if (chatWebSocket) {
    chatWebSocket.disconnect()
  }
})
</script>

<style scoped>
.chat-container {
  height: calc(100vh - 60px);
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.chat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.connection-status {
  display: flex;
  align-items: center;
}

.status-tag {
  font-size: 12px;
  font-weight: 500;
}

.chat-title {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #2c3e50;
}

.chat-content {
  flex: 1;
  overflow-y: auto;
  padding: 24px;
  scroll-behavior: smooth;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}

.messages-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.message-item {
  display: flex;
  gap: 12px;
  max-width: 80%;
}

.message-item.user {
  align-self: flex-end;
  flex-direction: row-reverse;
}

.message-item.ai {
  align-self: flex-start;
}

.message-avatar {
  flex-shrink: 0;
}

.message-content {
  flex: 1;
  min-width: 0;
}

.message-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.message-sender {
  font-weight: 600;
  color: #2c3e50;
  font-size: 14px;
}

.message-time {
  color: #909399;
  font-size: 12px;
}

.message-text {
  background: rgba(255, 255, 255, 0.9);
  padding: 12px 16px;
  border-radius: 12px;
  line-height: 1.6;
  color: #2c3e50;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  word-wrap: break-word;
}

.message-item.user .message-text {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.message-files {
  margin-top: 8px;
}

.file-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 8px;
  margin-top: 4px;
  font-size: 12px;
}

.file-name {
  flex: 1;
  color: #606266;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.file-size {
  color: #909399;
}

.chat-input-area {
  padding: 20px 24px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-top: 1px solid rgba(255, 255, 255, 0.2);
}

.input-wrapper {
  position: relative;
  margin-bottom: 16px;
}

.message-input {
  width: 100%;
}

.message-input :deep(.el-textarea__inner) {
  border-radius: 12px;
  border: 1px solid rgba(102, 126, 234, 0.2);
  background: rgba(255, 255, 255, 0.8);
  transition: all 0.3s ease;
  
  &:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
  }
}

.history-dropdown {
  position: absolute;
  bottom: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  max-height: 200px;
  overflow-y: auto;
}

.history-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  cursor: pointer;
  transition: background-color 0.2s;
  
  &:hover {
    background-color: #f5f7fa;
  }
  
  &:not(:last-child) {
    border-bottom: 1px solid #f0f0f0;
  }
}

.history-text {
  flex: 1;
  color: #606266;
  font-size: 14px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.input-actions {
  display: flex;
  justify-content: flex-end;
}

.send-btn {
  min-width: 100px;
  height: 40px;
  border-radius: 20px;
  font-weight: 600;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .chat-header {
    padding: 12px 16px;
  }
  
  .chat-content {
    padding: 16px;
  }
  
  .chat-input-area {
    padding: 16px;
  }
  
  .message-item {
    max-width: 90%;
  }
  
  .chat-title {
    font-size: 18px;
  }
}
</style>
