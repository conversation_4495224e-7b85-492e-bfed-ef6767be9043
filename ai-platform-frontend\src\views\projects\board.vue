<template>
  <div class="app-container">
    <!-- 类型多选筛选 -->
    <div class="filter-container">
      <div class="filter-types-search">
        <el-checkbox-group v-model="selectedTypes" class="mr-2" @change="handleTypeChange">
          <el-checkbox-button value="">全部</el-checkbox-button>
          <el-checkbox-button v-for="type in types" :key="type.typeId" :value="type.typeId">{{ type.displayName }}</el-checkbox-button>
        </el-checkbox-group>
        <el-button type="primary" class="ml-2" @click="showCreateDialog = true">创建新项目</el-button>
        <el-input
          v-model="searchQuery"
          placeholder="搜索项目名称"
          class="search-input ml-2"
          clearable
          @input="handleSearch"
          style="width: 220px;"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
      </div>
    </div>

    <!-- 项目列表 -->
    <el-row :gutter="20" class="projects-list">
      <el-col v-if="projects.length === 0" :span="24">
        <el-empty description="暂无项目数据" />
      </el-col>
      <el-col v-for="project in projects" :key="project.projectId || project.project_id" :xs="24" :sm="12" :md="8" :lg="6">
        <el-card class="project-card" shadow="hover" @dblclick="goToProjectDetail(project)">
          <template #header>
            <div class="project-header">
              <div class="project-title-section">
                <span class="project-name">
                  {{ project.projectName || project.project_name || '未命名项目' }}
                  <span class="project-id">(ID: {{ project.projectId || project.project_id }})</span>
                </span>
                <div class="project-actions">
                  <el-button size="small" type="primary" @click="editProject(project)">编辑</el-button>
                  <el-button size="small" type="danger" @click="deleteProject(project)">删除</el-button>
                </div>
              </div>
              <div class="project-types">
                <el-tag
                  v-for="typeId in (project.typeIds || project.type_ids || [])"
                  :key="typeId"
                  :type="getTypeTagType(typeId)"
                  size="small"
                  class="type-tag"
                >
                  {{ getTypeName(typeId) }}
                </el-tag>
                <span v-if="!project.typeIds && !project.type_ids || (project.typeIds && project.typeIds.length === 0) && (project.type_ids && project.type_ids.length === 0)" class="no-types">无类型</span>
                <span class="owner-info">负责人：{{ project.ownerName || project.owner_name || '未知用户' }}</span>
              </div>
            </div>
          </template>
          
          <div class="project-content">
            <div class="project-tasks">
              <div class="tasks-title">任务列表：</div>
              <div v-if="project.tasks && project.tasks.length > 0" class="tasks-list">
                <div v-for="task in project.tasks.slice(0, 2)" :key="task.taskId || task.task_id" class="task-item">
                  <div class="task-info">
                    <div class="task-status" :class="getTaskStatusClass(task.status)">
                      {{ getTaskStatusText(task.status) }}
                    </div>
                    <div class="task-name">
                      <span class="task-id">#{{ task.taskId || task.task_id }}</span>
                      <span class="task-name-text">{{ task.taskName || task.task_name }}</span>
                    </div>
                  </div>
                  <div class="task-actions">
                    <el-button 
                      size="small" 
                      type="primary" 
                      @click="viewTaskDetail(task)"
                      :disabled="task.status === 'pending'"
                    >
                      查看
                    </el-button>
                  </div>
                </div>

              </div>
              <div v-else class="no-tasks">暂无任务</div>
            </div>
            
            <div class="project-description">{{ project.description || '暂无描述' }}</div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[12, 24, 36, 48]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 新建/编辑项目弹窗 -->
    <el-dialog v-model="showCreateDialog" :title="editProjectData ? '编辑项目' : '新建项目'">
      <el-form :model="projectForm" label-width="80px">
        <el-form-item label="项目名称">
          <el-input v-model="projectForm.projectName" />
        </el-form-item>
        <el-form-item label="项目描述">
          <el-input v-model="projectForm.description" type="textarea" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showCreateDialog = false">取消</el-button>
        <el-button type="primary" @click="submitProject">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { Search } from '@element-plus/icons-vue'
import { getTypesList } from '@/api/types'
import { getProjectsByTypes, addProject, updateProject } from '@/api/projects'
import { getTasksListByProject } from '@/api/tasks'
import { ElMessage, ElMessageBox } from 'element-plus'

const router = useRouter()
const types = ref([])
const selectedTypes = ref([])
const searchQuery = ref('')
const projects = ref([])
const currentPage = ref(1)
const pageSize = ref(12)
const total = ref(0)
const showCreateDialog = ref(false)
const editProjectData = ref(null)
const projectForm = ref({ projectName: '', description: '' })

// 获取类型列表
const fetchTypes = async () => {
  try {
    const res = await getTypesList({ pageNum: 1, pageSize: 100, isActive: 1 })
    if (res.code === 200) {
      types.value = res.rows
    }
  } catch (error) {
    ElMessage.error('获取类型列表失败')
  }
}

// 获取项目列表
const fetchProjects = async () => {
  try {
    const typeIds = selectedTypes.value.length > 0 ? selectedTypes.value.join(',') : ''
    const res = await getProjectsByTypes({
      typeIds: typeIds || '',
      pageNum: currentPage.value,
      pageSize: pageSize.value,
      projectName: searchQuery.value || ''
    })
    
    if (res.code === 200) {
      projects.value = res.rows || []
      total.value = res.total || 0
      
      // 获取每个项目的任务列表
      for (const project of projects.value) {
        try {
          const projectId = project.projectId || project.project_id
          if (projectId) {
            const taskRes = await getTasksListByProject(projectId)
            project.tasks = (taskRes.code === 200 && Array.isArray(taskRes.data)) ? taskRes.data : []
          } else {
            project.tasks = []
          }
        } catch (error) {
          project.tasks = []
        }
      }
    }
  } catch (error) {
    ElMessage.error('获取项目列表失败')
  }
}

// 类型相关
const getTypeName = (typeId) => {
  const type = types.value.find(t => t.typeId === typeId)
  return type ? type.displayName : '未知类型'
}

const getTypeTagType = (typeId) => {
  const type = types.value.find(t => t.typeId === typeId)
  const colorMap = { success: 'success', warning: 'warning', danger: 'danger', info: 'info' }
  return colorMap[type?.colorCode] || 'info'
}

// 任务状态相关
const getTaskStatusText = (status) => {
  const statusMap = { 
    pending: '待提交',
    queued: '排队中',
    running: '进行中', 
    completed: '已完成', 
    failed: '失败'
  }
  return statusMap[status] || status
}

const getTaskStatusClass = (status) => {
  const classMap = { 
    pending: 'status-pending',
    queued: 'status-queued',
    running: 'status-running', 
    completed: 'status-completed', 
    failed: 'status-failed'
  }
  return classMap[status] || 'status-pending'
}

// 事件处理
const handleTypeChange = () => {
  // 如果选择了"全部"，清空其他选择；否则移除"全部"选项
  if (selectedTypes.value.includes('')) {
    selectedTypes.value = ['']
  } else {
    selectedTypes.value = selectedTypes.value.filter(type => type !== '')
  }
  
  currentPage.value = 1
  fetchProjects()
}

const handleSearch = () => {
  currentPage.value = 1
  fetchProjects()
}

const handleSizeChange = (val) => {
  pageSize.value = val
  currentPage.value = 1
  fetchProjects()
}

const handleCurrentChange = (val) => {
  currentPage.value = val
  fetchProjects()
}

// 新建/编辑项目
const submitProject = async () => {
  if (!projectForm.value.projectName) {
    ElMessage.warning('项目名称不能为空')
    return
  }
  
  try {
    if (editProjectData.value) {
      await updateProject({ ...editProjectData.value, ...projectForm.value })
      ElMessage.success('编辑成功')
    } else {
      await addProject(projectForm.value)
      ElMessage.success('创建成功')
    }
    showCreateDialog.value = false
    editProjectData.value = null
    projectForm.value = { projectName: '', description: '' }
    fetchProjects()
  } catch (error) {
    ElMessage.error('操作失败')
  }
}

const editProject = (project) => {
  editProjectData.value = project
  projectForm.value = { 
    projectName: project.projectName || project.project_name || '', 
    description: project.description || '' 
  }
  showCreateDialog.value = true
}

const deleteProject = (project) => {
  ElMessageBox.confirm('确定要删除该项目和对应的数据库吗？', '提示', { type: 'warning' })
    .then(async () => {
      try {
        const projectId = project.projectId || project.project_id
        await updateProject({ ...project, isDeleted: 1 })
        ElMessage.success('删除成功')
        fetchProjects()
      } catch (error) {
        ElMessage.error('删除失败')
      }
    })
    .catch(() => {
      // 用户取消删除
    })
}

// 双击跳转到项目详情页
const goToProjectDetail = (project) => {
  const projectId = project.projectId || project.project_id
  if (projectId) {
    router.push(`/projects/detail/${projectId}`)
  }
}

// 查看任务详情
const viewTaskDetail = (task) => {
  const taskId = task.taskId || task.task_id
  if (taskId) {
    router.push(`/tasks/detail/${taskId}`)
  } else {
    ElMessage.warning('任务ID不存在')
  }
}

onMounted(() => {
  fetchTypes()
  fetchProjects()
})
</script>

<style scoped>
.app-container {
  padding: 30px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.filter-container {
  display: flex;
  align-items: center;
  margin-bottom: 30px;
  flex-wrap: wrap;
  gap: 16px;
  padding: 24px;
  background: transparent;
  border-radius: 0;
  border: none;
  box-shadow: none;
}

.ml-2 {
  margin-left: 12px;
}

.mr-2 {
  margin-right: 12px;
}

.search-input {
  width: 220px;
  :deep(.el-input__wrapper) {
    border-radius: 6px;
    background: rgba(255, 255, 255, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow: none;
    transition: border-color 0.2s;
    &:hover, &.is-focus {
      border-color: #67aafc;
      box-shadow: none;
    }
  }
}

.projects-list {
  margin-bottom: 30px;
  min-height: 400px;
  
  :deep(.el-col) {
    display: flex;
  }
}

.project-card {
  margin-bottom: 24px;
  min-height: 300px;
  height: auto;
  width: 100%;
  border: none;
  border-radius: 16px;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  
  &:hover {
    transform: translateY(-8px);
    box-shadow: 0 16px 48px rgba(102, 126, 234, 0.2);
  }
  
  :deep(.el-card__header) {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    padding: 20px;
    flex-shrink: 0;
  }
  
  :deep(.el-card__body) {
    padding: 20px;
    flex: 1;
    display: flex;
    flex-direction: column;
  }
}

.project-header {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.project-title-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.project-name {
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  min-width: 0;
}

.project-id {
  margin-left: 8px;
  color: #999;
  font-size: 12px;
  font-weight: normal;
}

.project-actions {
  display: flex;
  gap: 8px;
  
  :deep(.el-button) {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
    
    &:hover {
      transform: translateY(-2px);
    }
  }
}

.project-types {
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
  min-height: 32px;
  align-items: center;
  
  :deep(.el-tag) {
    border-radius: 6px;
    font-weight: 500;
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow: none;
    background: rgba(255, 255, 255, 0.8);
  }
}

.type-tag {
  margin-right: 4px;
}

.project-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
  flex: 1;
}

.project-description {
  color: #606266;
  line-height: 1.6;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  min-height: 40px;
  margin-top: 12px;
  padding-top: 16px;
  border-top: 1px solid rgba(102, 126, 234, 0.1);
  font-size: 14px;
  flex-shrink: 0;
}

.project-tasks {
  display: flex;
  flex-direction: column;
  gap: 12px;
  flex: 1;
  min-height: 120px;
}

.tasks-title {
  font-weight: 600;
  color: #2c3e50;
  font-size: 14px;
  position: relative;
  padding-left: 12px;
  
  &::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 3px;
    height: 16px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 2px;
  }
}

.tasks-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
  width: 100%;
  flex: 1;
  min-height: 80px;
}

.task-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 8px 12px;
  background: rgba(255, 255, 255, 0.5);
  border-radius: 8px;
  transition: all 0.3s ease;
  width: 100%;
  min-width: 0;
  
  &:hover {
    background: rgba(102, 126, 234, 0.05);
    transform: translateX(4px);
  }
}

.task-info {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 10px;
}

.task-status {
  padding: 4px 10px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 600;
  min-width: 60px;
  text-align: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.status-completed {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
}

.status-running {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  color: white;
}

.status-failed {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  color: white;
}

.status-pending {
  background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
  color: white;
}

.status-queued {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  color: white;
}

.task-name {
  flex: 1;
  font-size: 13px;
  color: #374151;
  line-height: 1.4;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 8px;
}

.task-id {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 11px;
  font-weight: 600;
  min-width: 40px;
  text-align: center;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.task-name-text {
  flex: 1;
  word-break: break-word;
}

.task-actions {
  display: flex;
  gap: 8px;
}

.no-tasks {
  color: #9ca3af;
  font-size: 13px;
  font-style: italic;
  text-align: center;
  padding: 16px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 80px;
}



.no-types {
  color: #9ca3af;
  font-size: 12px;
  font-style: italic;
  padding: 4px 8px;
  background: rgba(255, 255, 255, 0.5);
  border-radius: 4px;
}

.owner-info {
  color: #6b7280;
  font-size: 12px;
  font-weight: 500;
  margin-left: auto;
  padding: 4px 8px;
  background: rgba(59, 130, 246, 0.1);
  border-radius: 4px;
  border: 1px solid rgba(59, 130, 246, 0.2);
}

.pagination-container {
  position: fixed;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  justify-content: center;
  padding: 15px 25px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  z-index: 1000;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .app-container {
    padding: 20px;
  }
  
  .filter-container {
    flex-direction: column;
    align-items: stretch;
    padding: 20px;
  }
  
  .search-input {
    width: 100%;
  }
  
  .project-title-section {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .project-actions {
    width: 100%;
    justify-content: flex-end;
  }
  
  .project-card {
    min-height: 250px;
  }
  
  .projects-list {
    min-height: 300px;
  }
  
  .project-tasks {
    min-height: 100px;
  }
  
  .tasks-list {
    min-height: 60px;
  }
  
  .no-tasks {
    min-height: 60px;
  }
}

.filter-types-search {
  display: flex;
  align-items: center;
  gap: 24px;
  width: 100%;
  margin-bottom: 8px;
}

:deep(.el-checkbox-group) .el-checkbox-button__inner {
  font-size: 16px;
  padding: 8px 24px;
  border-radius: 24px !important;
  margin-right: 12px;
  margin-bottom: 4px;
  height: 40px;
  min-width: 60px;
  background: #f5f7fa;
  border: 1.5px solid #e3eaf1;
  transition: all 0.2s;
}

:deep(.el-checkbox-button.is-checked) .el-checkbox-button__inner {
  background-color: #409eff !important;
  border-color: #409eff !important;
  color: #ffffff !important;
}
</style> 