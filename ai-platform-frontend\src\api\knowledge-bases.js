import request from '@/utils/request'

// 获取数据库列表
export function getKnowledgeBasesList(query) {
  return request({
    url: '/business/knowledge_bases/list',
    method: 'get',
    params: query
  })
}

// 获取数据库详情
export function getKnowledgeBase(kbId) {
  return request({
    url: `/business/knowledge_bases/${kbId}`,
    method: 'get'
  })
}

// 获取数据库详情（包含类型信息）
export function getKnowledgeBaseWithTypes(kbId) {
  return request({
    url: `/business/knowledge_bases/${kbId}/with_types`,
    method: 'get'
  })
}

// 添加数据库
export function addKnowledgeBase(data) {
  return request({
    url: '/business/knowledge_bases',
    method: 'post',
    data: data
  })
}

// 更新数据库
export function updateKnowledgeBase(data) {
  return request({
    url: '/business/knowledge_bases',
    method: 'put',
    data: data
  })
}

// 按类型多选、分页、搜索获取数据库列表
export function getKnowledgeBasesByTypes({ typeIds = '', pageNum = 1, pageSize = 12, kbName = '' }) {
  return request({
    url: '/business/kb_types_rel/knowledge_bases/by_types',
    method: 'get',
    params: { type_ids: typeIds, page_num: pageNum, page_size: pageSize, kb_name: kbName }
  })
}

// 获取数据库文件统计
export function getFileStatsByKb(kbId) {
  return request({
    url: `/business/files/stats/${kbId}`,
    method: 'get'
  })
}

// 获取数据库列表（包含类型信息）
export function getKnowledgeBasesListWithTypes(query) {
  return request({
    url: '/business/knowledge_bases/list_with_types',
    method: 'get',
    params: query
  })
} 