<template>
  <div class="app-container">
    <div class="task-creation-container">
      <!-- 页面标题 -->
      <div class="page-header">
        <h2>创建新任务</h2>
        <el-button @click="goBack" type="default">返回项目看板</el-button>
      </div>

      <!-- 主要内容区域 -->
      <div class="main-content">
        <!-- 左侧区域 -->
        <div class="left-panel">
          <!-- 工具简介 -->
          <div class="tool-intro-section">
            <h3>工具简介</h3>
            <div v-if="!selectedTool" class="no-tool-selected">
              <el-empty description="未选择工具">
                <el-button type="primary" @click="showToolSelector = true">选择工具</el-button>
              </el-empty>
            </div>
            <div v-else class="tool-info">
              <div class="tool-header">
                <h4>{{ selectedTool.toolName }}</h4>
                <el-button size="small" type="primary" @click="showToolSelector = true">切换工具</el-button>
              </div>
              <div class="tool-details">
                <p><strong>版本：</strong>{{ selectedTool.version || '未指定' }}</p>
                <p><strong>供应商：</strong>{{ selectedTool.vendor || '未指定' }}</p>
                <p><strong>描述：</strong>{{ selectedTool.description || '暂无描述' }}</p>
              </div>
            </div>
          </div>

          <!-- 参数填写指南 -->
          <div class="parameter-guide-section">
            <h3>参数填写指南</h3>
            <div class="guide-content">
              <p>填写参数时请注意以下几点：</p>
              <ol>
                <li>公称直径(DN)和公称压力(PN)必须符合国家标准</li>
                <li>材料选择会影响阀门的适用工况和性能</li>
                <li>连接方式需与管道系统匹配</li>
                <li>可以使用预设模板快速填充常用参数组合</li>
                <li>高级参数可选填，不填写时将使用默认值</li>
              </ol>
            </div>
          </div>
        </div>

        <!-- 右侧区域 -->
        <div class="right-panel">
          <!-- 参数配置 -->
          <div class="parameter-config-section">
            <h3>参数配置</h3>
            
            <!-- 项目信息 -->
            <!-- <div class="project-info-section">
              <h4>项目信息</h4>
              <el-form :model="projectInfo" label-width="100px" disabled>
                <el-form-item label="所属项目">
                  <el-input v-model="projectInfo.projectName" />
                </el-form-item>
              </el-form>
            </div> -->

            <!-- 任务信息 -->
            <div class="task-info-section">
              <h4>任务信息</h4>
              <el-form :model="taskForm" label-width="100px" :rules="taskRules" ref="taskFormRef">
                <el-form-item label="任务名称" prop="taskName">
                  <el-input v-model="taskForm.taskName" placeholder="请输入任务名称" />
                </el-form-item>
              </el-form>
            </div>

            <!-- 参数填写 -->
            <div v-if="selectedTool" class="parameters-section">
              <h4>参数填写</h4>
              <div class="form-grid" v-if="toolFormFields.length > 0">
                <div v-for="field in toolFormFields" :key="field.prop" class="form-item">
                  <div class="form-label">{{ field.label }}</div>
                  <div class="input-wrapper">
                    <!-- 文件上传组件 -->
                    <FileUpload
                      v-if="field.type === 'FileUpload'"
                      :file-list="cachedFileList[field.prop] || []"
                      v-bind="field.attrs"
                      :cache-mode="true"
                      :disabled="submitting"
                      @file-select="files => handleFileSelect(field.prop, files)"
                      @file-remove="() => handleFileRemove(field.prop)"
                    />
                    <!-- 其他组件 -->
                    <component
                      v-else
                      :is="field.type"
                      v-model="parametersForm[field.prop]"
                      v-bind="field.attrs"
                      :options="field.options"
                      :class="['form-input', field.unit ? 'has-unit' : '']"
                    >
                      <template v-if="field.type === 'el-select'" v-for="opt in field.options" :key="opt.value">
                        <el-option :label="opt.label" :value="opt.value" />
                      </template>
                    </component>
                    <span v-if="field.unit" class="unit-label">{{ field.unit }}</span>
                  </div>
                </div>
              </div>
              <el-empty v-else description="暂无表单项" />
            </div>

            <!-- 无工具选择时的提示 -->
            <div v-else-if="!selectedTool" class="no-tool-selected-hint">
              <el-alert
                title="请先选择工具"
                description="选择工具后将显示相应的参数配置项"
                type="info"
                show-icon
              />
            </div>

            <!-- 无配置模板时的提示 -->
            <div v-else-if="!selectedTool.configTemplate" class="no-config-template-hint">
              <el-alert
                title="该工具暂无配置模板"
                description="请选择其他已配置的工具，或等待工具配置完成后再创建任务"
                type="warning"
                show-icon
              />
            </div>
          </div>

          <!-- 操作按钮 -->
          <div class="action-buttons">
            <el-button @click="resetForm" size="large">重置</el-button>
            <el-button 
              type="primary" 
              size="large" 
              @click="submitTask"
              :loading="submitting"
              :disabled="!selectedTool || !taskForm.taskName"
            >
              创建任务
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 工具选择弹窗 -->
    <el-dialog v-model="showToolSelector" title="选择工具" width="800px">
      <div class="tool-selector">
        <!-- 工具类型筛选 -->
        <div class="tool-filter">
          <el-radio-group v-model="selectedToolType" @change="handleToolTypeChange">
            <el-radio-button value="">全部</el-radio-button>
            <el-radio-button 
              v-for="type in toolTypes" 
              :key="type.typeId" 
              :value="type.typeId"
            >
              {{ type.displayName }}
            </el-radio-button>
          </el-radio-group>
        </div>

        <!-- 工具搜索 -->
        <div class="tool-search">
          <el-input
            v-model="toolSearchQuery"
            placeholder="搜索工具名称"
            clearable
            @input="handleToolSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </div>

        <!-- 工具列表 -->
        <div class="tools-list">
          <el-card
            v-for="tool in filteredTools"
            :key="tool.toolId"
            class="tool-card"
            shadow="hover"
            @click="selectTool(tool)"
          >
            <div class="tool-card-content">
              <div class="tool-name">{{ tool.toolName }}</div>
              <div class="tool-description">{{ tool.description || '暂无描述' }}</div>
              <div class="tool-meta">
                <span class="tool-version">版本: {{ tool.version || '未指定' }}</span>
                <span class="tool-vendor">供应商: {{ tool.vendor || '未指定' }}</span>
              </div>
            </div>
          </el-card>
        </div>
      </div>
      
      <template #footer>
        <el-button @click="showToolSelector = false">取消</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, reactive, nextTick, getCurrentInstance, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { Search } from '@element-plus/icons-vue'
import { getTypesList } from '@/api/types'
import { getProjectDetail } from '@/api/projects'
import { getToolsList, getTool } from '@/api/tools'
import { addTask, updateTask, checkHistoricalMatch } from '@/api/tasks'
import { submitTaskToQueue } from '@/api/task-queue'
import { addProjectTypeRel } from '@/api/project-types-rel'
import { uploadFile } from '@/api/files'
import { ElMessage, ElMessageBox } from 'element-plus'
import FileUpload from '@/components/FileUpload/index.vue'
import { getKnowledgeBasesList } from '@/api/knowledge-bases'

const route = useRoute()
const router = useRouter()

// 响应式数据
const projectInfo = ref({})
const selectedTool = ref(null)
const showToolSelector = ref(false)
const toolTypes = ref([])
const selectedToolType = ref('')
const toolSearchQuery = ref('')
const tools = ref([])
const submitting = ref(false)

// 新增：缓存的文件对象和显示列表
const cachedFiles = ref({})         // { 字段名: 文件对象 }
const cachedFileList = ref({})      // { 字段名: [ { name, uid, raw } ] }

// 表单数据
const taskForm = reactive({
  taskName: ''
})

const parametersForm = reactive({})

// 表单验证规则
const taskRules = {
  taskName: [
    { required: true, message: '请输入任务名称', trigger: 'blur' }
  ]
}

// 表单引用
const taskFormRef = ref()

// 计算属性
const filteredTools = computed(() => {
  let filtered = tools.value

  // 按类型筛选
  if (selectedToolType.value && selectedToolType.value !== '') {
    filtered = filtered.filter(tool => tool.typeId === selectedToolType.value)
  }

  // 按搜索词筛选
  if (toolSearchQuery.value) {
    filtered = filtered.filter(tool => 
      tool.toolName.toLowerCase().includes(toolSearchQuery.value.toLowerCase())
    )
  }

  return filtered
})

const generateDynamicFormFields = (configTemplate) => {
  console.log('🔍 generateDynamicFormFields 被调用，配置模板:', configTemplate)
  
  if (!configTemplate) {
    console.log('❌ 配置模板为空，返回空数组')
    return []
  }

  const fields = []

  // 处理新的数据格式
  let orderedKeys = []
  let configData = configTemplate

  console.log('🔍 配置模板类型:', typeof configTemplate)
  console.log('🔍 配置模板内容:', configTemplate)

  // 检查是否为嵌套结构（如单向阀建模仿真工具）
  if (configTemplate._order && Array.isArray(configTemplate._order)) {
    // 检查是否为真正的嵌套结构（包含子部分如 model 或 simulation）
    const hasNestedSections = configTemplate._order.some(sectionKey => {
      const section = configTemplate[sectionKey]
      return section && typeof section === 'object' && section._order && Array.isArray(section._order)
    })
    
    if (hasNestedSections) {
      console.log('🔍 检测到嵌套结构，开始展平字段')
      // 如果是嵌套结构，需要展平所有字段
      const allFields = []
      
      configTemplate._order.forEach(sectionKey => {
        const section = configTemplate[sectionKey]
        console.log('🔍 处理部分:', sectionKey, section)
        if (section && typeof section === 'object' && section._order) {
          // 这是一个子部分，如 model 或 simulation
          section._order.forEach(fieldKey => {
            allFields.push({
              key: fieldKey,
              value: section[fieldKey],
              section: sectionKey
            })
          })
        }
      })
      
      // 使用展平后的字段
      orderedKeys = allFields.map(field => field.key)
      configData = {}
      allFields.forEach(field => {
        configData[field.key] = field.value
      })
      
      console.log('🔍 展平后的字段:', orderedKeys)
      console.log('🔍 展平后的配置数据:', configData)
    } else {
      console.log('🔍 检测到扁平结构，使用 _order 字段指定的顺序')
      // 这是扁平结构，直接使用 _order 字段指定的顺序
      orderedKeys = configTemplate._order
      configData = configTemplate
    }
  } else {
    console.log('🔍 使用兼容旧格式，Object.keys()')
    // 兼容旧格式，使用 Object.keys()
    orderedKeys = Object.keys(configTemplate).filter(key => key !== '_order')
  }
  
  orderedKeys.forEach((key, index) => {
    const field = {
      label: key,
      prop: generatePropName(key),
      type: 'el-input',
      attrs: { placeholder: `请输入${key}` },
      defaultValue: configData[key],
      order: index // 添加排序索引
    }

    // 根据字段名称智能判断输入类型
    if (key.includes('上传路径') || key.includes('模型上传') || key.includes('文件上传') || key.includes('上传文件')) {
      // 文件上传字段
      field.type = 'FileUpload'
      
      // 根据具体的字段类型设置不同的文件类型限制
      if (key.includes('模型') || key.includes('3D')) {
        // 3D模型文件
        field.attrs = { 
          limit: 1,
          fileSize: 100, // 100MB限制，因为3D模型文件可能较大
          fileType: ['step', 'stp', 'iges', 'igs', 'stl', 'obj', '3mf', 'ply'],
          isShowTip: true
        }
      } else if (key.includes('图片') || key.includes('图像')) {
        // 图片文件
        field.attrs = { 
          limit: 1,
          fileSize: 10,
          fileType: ['jpg', 'jpeg', 'png', 'gif', 'bmp'],
          isShowTip: true
        }
      } else {
        // 通用文件
        field.attrs = { 
          limit: 1,
          fileSize: 50,
          fileType: ['doc', 'docx', 'xls', 'xlsx', 'pdf', 'txt', 'zip', 'rar'],
          isShowTip: true
        }
      }
    } else if (key.includes('材质') || key.includes('材料')) {
      // 材质选择字段
      field.type = 'el-select'
      field.attrs = { placeholder: `请选择${key}` }
      
      // 根据默认值和字段名称生成选项
      if (key.includes('O型圈材质') || key.includes('O形圈材质')) {
        field.options = [
          { label: 'HNBR', value: 'HNBR' },
          { label: 'EPDM', value: 'EPDM' }
        ]
      } else if (key.includes('阀芯材质')) {
        field.options = [
          { label: 'PPS+40%GF', value: 'PPS+40%GF' },
          { label: 'PA66+30%GF', value: 'PA66+30%GF' }
        ]
      } else {
        // 通用材质选项，基于默认值生成
        const defaultValue = configTemplate[key].trim()
        field.options = [
          { label: defaultValue, value: defaultValue }
        ]
      }
    } else if (key.includes('流体工质') || key.includes('工质')) {
      // 流体工质选择字段
      field.type = 'el-select'
      field.attrs = { placeholder: '请选择流体工质' }
      field.options = [
        { label: 'R134a', value: 'R134a' },
        { label: 'R1234yf', value: 'R1234yf' },
        { label: 'R744', value: 'R744' },
        { label: '50EG', value: '50EG' }
      ]
    } else if (key.includes('温度') || key.includes('温（℃）')) {
      // 温度输入字段
      field.attrs.type = 'number'
      // field.unit = 'K'
    } else if (key.includes('直径') || key.includes('内径') || key.includes('(mm)')) {
      // 直径/长度输入字段
      field.attrs.type = 'number'
      // field.unit = 'mm'
    } else if(key.includes('过冷/过热度（K）')) {
        field.attrs.placeholder = '请输入过热度（用正数表示）或者过冷度（用负数表示），单位为K'
    } else if (key.includes('角度') || key.includes('斜度') || key.includes('(°)')) {
      // 角度输入字段
      field.attrs.type = 'number'
      // field.unit = '°'
    }

    fields.push(field)
  })

  // 确保按照原始顺序返回字段（虽然forEach已经保持了顺序，但为了更明确）
  const sortedFields = fields.sort((a, b) => a.order - b.order)
  console.log('动态表单字段顺序:', sortedFields.map(f => ({ label: f.label, order: f.order })))
  return sortedFields
}

const toolFormFields = computed(() => {
  console.log('🔍 toolFormFields 计算属性被调用')
  console.log('🔍 selectedTool.value:', selectedTool.value)
  console.log('🔍 selectedTool.value?.configTemplate:', selectedTool.value?.configTemplate)
  
  if (!selectedTool.value || !selectedTool.value.configTemplate) {
    console.log('❌ 没有选择工具或工具没有配置模板，返回空数组')
    return []
  }

  const fields = generateDynamicFormFields(selectedTool.value.configTemplate)
  console.log('🔍 生成的表单字段:', fields)
  return fields
})

// 生成属性名称的辅助函数
const generatePropName = (label) => {
  // 创建一个映射表，将中文标签转换为英文属性名
  const labelMap = {
    // 温度相关
    '应用低温（℃）': 'lowTemp',
    '应用高温（℃）': 'highTemp',
    '过冷/过热度（K）': 'operatingTemp',
    
    // 压力相关
    '入口绝对压力（MPa）': 'inletAbsolutePressure',
    '最小开阀压差（目标压降值）Dp（Pa）': 'minOpeningPressureDiff',

    
    // 流量相关
    '入口质量流量（kg/s）': 'inletMassFlow',
    
    // 材质相关
    'O型圈材质（HNBR/EPDM）': 'oRingMaterial',
    '阀芯材质（PPS+40%GF/PA66+30%GF）': 'coreMaterial',
    
    // 尺寸相关
    '阀口直径d（mm）': 'valveDiameter',
    '单向阀安装位置前后管路内径D1（mm）': 'pipeDiameter',
    '内壁密封斜度θ（°）': 'sealAngle',
    '阀芯导杆长度H4寻优范围（以设计值为中心）+（mm）': 'guideRodLengthOptRangePositive',
    '阀芯导杆长度H4寻优范围（以设计值为中心）-（mm）': 'guideRodLengthOptRangeNegative',
    
    // 工质相关
    '流体工质（R134a/R1234yf/R744/50EG）': 'fluidMedium',
    
    // 计算参数
    '迭代步长（mm）': 'iterationStep',
    '迭代步长mm': 'iterationStep',
    '最大步数': 'maxSteps',
    
    // 文件上传相关
    '模型上传路径': 'modelFilePath'
  }
  
  // 先检查是否有直接映射
  if (labelMap[label]) {
    return labelMap[label]
  }
  
  const result = label
    .replace(/[（）()℃°]/g, '')
    .replace(/[\/、]/g, '_')
    .replace(/\s+/g, '')
    .replace(/^(.)/, (match) => match.toLowerCase())

  return result
}

// 方法
const goBack = () => {
  router.back()
}

const fetchProjectInfo = async () => {
  try {
    const projectId = route.params.projectId
    const res = await getProjectDetail(projectId)
    if (res.code === 200) {
      projectInfo.value = res.data || {}
      
      // 根据项目ID查找关联的知识库
      try {
        const kbRes = await getKnowledgeBasesList({ projectId: projectId, pageNum: 1, pageSize: 1 })
        if (kbRes.code === 200 && kbRes.rows && kbRes.rows.length > 0) {
          const kbInfo = kbRes.rows[0]
          projectInfo.value.kbId = kbInfo.kbId || kbInfo.kb_id
          console.log('项目关联的知识库ID:', projectInfo.value.kbId)
        } else {
          console.warn('项目未关联知识库')
        }
      } catch (error) {
        console.error('获取项目关联知识库失败:', error)
      }
    }
  } catch (error) {
    ElMessage.error('获取项目信息失败')
  }
}

const fetchToolTypes = async () => {
  try {
    const res = await getTypesList({ pageNum: 1, pageSize: 100, isActive: 1 })
    if (res.code === 200) {
      toolTypes.value = res.rows
    }
  } catch (error) {
    ElMessage.error('获取工具类型失败')
  }
}

const fetchTools = async () => {
  try {
    const res = await getToolsList({ pageNum: 1, pageSize: 100, isActive: 1 })
    if (res.code === 200) {
      tools.value = res.rows || []
    }
  } catch (error) {
    ElMessage.error('获取工具列表失败')
  }
}

const handleToolTypeChange = () => {
  // 单选逻辑，不需要特殊处理
}

const handleToolSearch = () => {
  // 搜索逻辑已在计算属性中处理
}

const selectTool = async (tool) => {
  try {
    console.log('🔍 开始选择工具:', tool)
    // 获取工具详细信息
    const res = await getTool(tool.toolId)
    console.log('🔍 工具详情API响应:', res)
    
    if (res.code === 200) {
      selectedTool.value = res.data
      showToolSelector.value = false
      
      console.log('🔍 选择的工具详情:', selectedTool.value)
      console.log('🔍 工具配置模板:', selectedTool.value.configTemplate)
      
      // 重置参数表单
      Object.keys(parametersForm).forEach(key => {
        delete parametersForm[key]
      })
      
      // 根据configTemplate初始化参数表单
      if (selectedTool.value.configTemplate) {
        // 处理新的数据格式
        const configTemplate = selectedTool.value.configTemplate
        let configData = configTemplate
        let orderedKeys = []

        console.log('🔍 配置模板类型:', typeof configTemplate)
        console.log('🔍 配置模板内容:', configTemplate)

        // 检查是否为嵌套结构
        if (configTemplate._order && Array.isArray(configTemplate._order)) {
          // 检查是否为真正的嵌套结构（包含子部分如 model 或 simulation）
          const hasNestedSections = configTemplate._order.some(sectionKey => {
            const section = configTemplate[sectionKey]
            return section && typeof section === 'object' && section._order && Array.isArray(section._order)
          })
          
          if (hasNestedSections) {
            console.log('🔍 检测到嵌套结构，开始展平字段')
            // 如果是嵌套结构，需要展平所有字段
            const allFields = []
            
            configTemplate._order.forEach(sectionKey => {
              const section = configTemplate[sectionKey]
              console.log('🔍 处理部分:', sectionKey, section)
              if (section && typeof section === 'object' && section._order) {
                // 这是一个子部分，如 model 或 simulation
                section._order.forEach(fieldKey => {
                  allFields.push({
                    key: fieldKey,
                    value: section[fieldKey],
                    section: sectionKey
                  })
                })
              }
            })
            
            // 使用展平后的字段
            orderedKeys = allFields.map(field => field.key)
            configData = {}
            allFields.forEach(field => {
              configData[field.key] = field.value
            })
            
            console.log('🔍 展平后的字段:', orderedKeys)
            console.log('🔍 展平后的配置数据:', configData)
          } else {
            console.log('🔍 检测到扁平结构，使用 _order 字段指定的顺序')
            // 这是扁平结构，直接使用 _order 字段指定的顺序
            orderedKeys = configTemplate._order
            configData = configTemplate
          }
        } else {
          console.log('🔍 使用兼容旧格式，Object.keys()')
          // 兼容旧格式，使用 Object.keys()
          orderedKeys = Object.keys(configTemplate).filter(key => key !== '_order')
        }

        console.log('🔍 最终字段顺序:', orderedKeys)
        console.log('🔍 最终配置数据:', configData)

        // 按照处理后的顺序初始化参数，保持原始类型
        orderedKeys.forEach(key => {
          const propName = generatePropName(key)
          const defaultValue = configData[key]
          console.log('🔍 处理字段:', key, '->', propName, '原始值:', defaultValue, '类型:', typeof defaultValue)
          
          // 安全地设置响应式对象的属性
          if (!(propName in parametersForm)) {
            parametersForm[propName] = '';
          }
          
          // 文件上传字段初始化为空字符串
          if (key.includes('上传路径') || key.includes('模型上传') || key.includes('文件上传') || key.includes('上传文件')) {
            parametersForm[propName] = ''
          } else {
            // 根据默认值类型初始化
            if (defaultValue === null) {
              // 如果默认值为null，初始化为空字符串，但保留原始类型信息用于后续转换
              parametersForm[propName] = ''
            } else if (defaultValue === '') {
              // 如果默认值为空字符串，保持字符串类型
              parametersForm[propName] = ''
            } else {
              // 如果有具体默认值，使用该值
              parametersForm[propName] = defaultValue
            }
          }
        })
        
        console.log('🔍 初始化后的参数表单:', parametersForm)
      } else {
        console.warn('⚠️ 工具没有配置模板')
      }
      
      ElMessage.success(`已选择工具: ${selectedTool.value.toolName}`)
    } else {
      ElMessage.error('获取工具详细信息失败')
    }
  } catch (error) {
    ElMessage.error('获取工具详细信息失败')
    console.error('获取工具详细信息错误:', error)
  }
}

// 处理文件选择（缓存文件，不上传）
const handleFileSelect = (fieldKey, files) => {
  if (files && files.length > 0) {
    const file = files[0].raw || files[0];
    const propName = generatePropName(fieldKey);
    
    // 安全地设置响应式对象的属性
    if (!(propName in parametersForm)) {
      parametersForm[propName] = '';
    }
    
    cachedFiles.value[fieldKey] = file;
    cachedFileList.value[fieldKey] = [{ name: file.name, uid: Date.now(), raw: file }];
    parametersForm[propName] = file.name;
    console.log(`文件已缓存: ${fieldKey} -> ${file.name}`)
    console.log('当前缓存的文件:', cachedFiles.value)
    console.log('当前参数表单:', parametersForm)
  }
};

// 处理文件删除
const handleFileRemove = (fieldKey) => {
  const propName = generatePropName(fieldKey);
  
  delete cachedFiles.value[fieldKey];
  cachedFileList.value[fieldKey] = [];
  
  // 安全地设置响应式对象的属性
  if (!(propName in parametersForm)) {
    parametersForm[propName] = '';
  }
  parametersForm[propName] = '';
  
  console.log(`文件已删除: ${fieldKey}`)
  console.log('当前缓存的文件:', cachedFiles.value)
  console.log('当前参数表单:', parametersForm)
};


const resetForm = () => {
  taskForm.taskName = ''
  
  // 重置参数表单
  Object.keys(parametersForm).forEach(key => {
    delete parametersForm[key]
  })
  
  // 重新初始化参数默认值
  if (selectedTool.value && selectedTool.value.configTemplate) {
    // 按照原始字典顺序重置参数
    const orderedKeys = Object.keys(selectedTool.value.configTemplate)
    orderedKeys.forEach(key => {
      const propName = generatePropName(key)
      // 安全地设置响应式对象的属性
      if (!(propName in parametersForm)) {
        parametersForm[propName] = '';
      }
      // 文件上传字段初始化为空字符串
      if (key.includes('上传路径') || key.includes('模型上传') || key.includes('文件上传') || key.includes('上传文件')) {
        parametersForm[propName] = ''
      } else {
        parametersForm[propName] = selectedTool.value.configTemplate[key] || ''
      }
    })
  }
  
  if (taskFormRef.value) {
    taskFormRef.value.resetFields()
  }
}

// 修改提交任务函数
const submitTask = async () => {
  try {
    // 验证表单
    if (taskFormRef.value) {
      await taskFormRef.value.validate()
    }
    
    if (!selectedTool.value) {
      ElMessage.error('请先选择工具')
      return
    }
    
    submitting.value = true
    
    // 验证文件上传字段
    if (selectedTool.value.configTemplate) {
      // 按照原始字典顺序获取文件上传字段
      const orderedKeys = Object.keys(selectedTool.value.configTemplate)
      const fileUploadFields = orderedKeys.filter(key => 
        key.includes('上传路径') || key.includes('模型上传') || key.includes('文件上传') || key.includes('上传文件')
      )
      
      console.log('需要验证的文件上传字段:', fileUploadFields)
      console.log('当前参数表单:', parametersForm)
      console.log('当前缓存的文件:', cachedFiles.value)
      
      for (let key of fileUploadFields) {
        const propName = generatePropName(key)
        const fieldValue = parametersForm[propName]
        const hasCachedFile = cachedFiles.value[key]
        
        console.log(`检查字段 ${key}: propName=${propName}, fieldValue=${fieldValue}, hasCachedFile=${!!hasCachedFile}`)
        
        if (!fieldValue || fieldValue.trim() === '') {
          ElMessage.error(`请上传${key.replace(/路径|上传/g, '')}`)
          submitting.value = false
          return
        }
      }
      
      // 单向阀仿真任务特殊验证
      if (selectedTool.value.toolName && selectedTool.value.toolName.includes('单向阀仿真')) {
        // 字段名映射表
        const fieldNameMap = {
          operatingTemp: '工况温度',
          inletAbsolutePressure: '入口绝对压力',
          inletMassFlow: '入口质量流量',
          oRingMaterial: 'O型圈材质',
          coreMaterial: '阀芯材质',
          valveDiameter: '阀口直径',
          pipeDiameter: '管路内径',
          sealAngle: '密封斜度',
          iterationStep: '迭代步长',
          maxSteps: '最大步数',
          fluidMedium: '流体工质',
          modelFilePath: '模型文件'
        }

        // 只校验当前表单实际渲染的字段
        const requiredFields = toolFormFields.value
          .filter(field => field.required !== false) // 只校验必填项
          .map(field => field.prop)

        const missingFields = []
        for (const field of requiredFields) {
          const value = parametersForm[field]
          if (!value || value.toString().trim() === '') {
            missingFields.push(fieldNameMap[field] || field)
          }
        }
        if (missingFields.length > 0) {
          ElMessage.error(`请填写以下必填字段: ${missingFields.join('、')}`)
          submitting.value = false
          return
        }
        
        // 数值字段验证（入口质量流量已改为字符串验证）
        const numericFields = {
          'operatingTemp': '工况温度',
          'inletAbsolutePressure': '入口绝对压力',
          'valveDiameter': '阀口直径',
          'pipeDiameter': '管路内径',
          'sealAngle': '密封斜度',
          'iterationStep': '迭代步长',
          'maxSteps': '最大步数'
        }
        
        for (const [field, label] of Object.entries(numericFields)) {
          const value = parametersForm[field]
          if (value && isNaN(Number(value))) {
            ElMessage.error(`${label}必须是数字`)
            submitting.value = false
            return
          }
        }
        
        // 范围验证
        const temp = Number(parametersForm.operatingTemp)
        if (temp < -50 || temp > 200) {
          ElMessage.error('工况温度应在-50°C到200°C之间')
          submitting.value = false
          return
        }
        
        const pressure = Number(parametersForm.inletAbsolutePressure)
        if (pressure <= 0 || pressure > 10) {
          ElMessage.error('入口绝对压力应在0-10MPa之间')
          submitting.value = false
          return
        }
        
        // 入口质量流量只需要不为空即可（字符串类型）
        if (!parametersForm.inletMassFlow || parametersForm.inletMassFlow.toString().trim() === '') {
          ElMessage.error('请填写入口质量流量')
          submitting.value = false
          return
        }
        
        const diameter = Number(parametersForm.valveDiameter)
        if (diameter <= 0 || diameter > 100) {
          ElMessage.error('阀口直径应在0-100mm之间')
          submitting.value = false
          return
        }
        
        const pipeD = Number(parametersForm.pipeDiameter)
        if (pipeD <= 0 || pipeD > 500) {
          ElMessage.error('管路内径应在0-500mm之间')
          submitting.value = false
          return
        }
        
        const angle = Number(parametersForm.sealAngle)
        if (angle < 0 || angle > 90) {
          ElMessage.error('密封斜度应在0-90°之间')
          submitting.value = false
          return
        }
        
        const step = Number(parametersForm.iterationStep)
        if (step <= 0 || step > 10) {
          ElMessage.error('迭代步长应在0-10mm之间')
          submitting.value = false
          return
        }
        
        const maxStep = Number(parametersForm.maxSteps)
        if (maxStep <= 0 || maxStep > 10000) {
          ElMessage.error('最大步数应在0-10000之间')
          submitting.value = false
          return
        }
      }
    }

    // 通用参数验证：检查所有参数是否为空
    console.log('开始通用参数验证，当前参数:', parametersForm)

    // 检查是否有任何参数
    const hasAnyParams = Object.keys(parametersForm).length > 0
    if (!hasAnyParams) {
      ElMessage.error('请先配置任务参数')
      submitting.value = false
      return
    }

    // 检查是否所有参数都为空
    const hasValidParams = Object.values(parametersForm).some(value =>
      value !== null && value !== undefined && value.toString().trim() !== ''
    )
    if (!hasValidParams) {
      ElMessage.error('任务参数不能全部为空，请填写必要的参数')
      submitting.value = false
      return
    }

    // 检查必填参数（如果工具有配置模板）
    if (selectedTool.value.configTemplate && toolFormFields.value.length > 0) {
      const requiredFields = toolFormFields.value.filter(field => field.required !== false)
      const emptyRequiredFields = []

      for (const field of requiredFields) {
        const value = parametersForm[field.prop]
        if (!value || value.toString().trim() === '') {
          emptyRequiredFields.push(field.label || field.prop)
        }
      }

      if (emptyRequiredFields.length > 0) {
        ElMessage.error(`请填写以下必填字段: ${emptyRequiredFields.join('、')}`)
        submitting.value = false
        return
      }
    }

    console.log('通用参数验证通过')

    const typeId = parseInt(selectedTool.value.typeId)
    const toolId = parseInt(selectedTool.value.toolId)

    if (typeId != 3) { // 除了选型任务，其他任务都检查历史匹配
      ElMessage.info('正在检查历史任务...')

      try {
        const matchResult = await checkHistoricalMatch({
          project_id: parseInt(route.params.projectId),
          type_id: typeId,
          tool_id: toolId,
          parameters: parametersForm || {}
        })
        
        if (matchResult.code === 200 && matchResult.data.has_match) {
          // 找到历史匹配任务，询问用户是否使用历史结果
          try {
            await ElMessageBox.confirm(
              `检测到历史中存在相同参数的建模任务（任务ID: ${matchResult.data.historical_task.task_id}），是否直接使用历史结果？`,
              '历史任务匹配',
              {
                confirmButtonText: '使用历史结果',
                cancelButtonText: '创建新任务',
                type: 'info',
                distinguishCancelAndClose: true
              }
            )

            // 用户选择使用历史结果，直接跳转到历史任务详情页
            ElMessage.success('正在跳转到历史任务详情页...')
            submitting.value = false
            router.push(`/tasks/detail/${matchResult.data.historical_task.task_id}`)
            return // 直接返回，不创建新任务

          } catch (action) {
            if (action === 'cancel') {
              // 用户选择创建新任务，继续正常流程
              ElMessage.info('正在创建新任务...')
            } else {
              // 用户关闭对话框，取消操作
              ElMessage.info('操作已取消')
              submitting.value = false
              return
            }
          }
        } else {
          ElMessage.info('正在创建任务...')
        }
      } catch (error) {
        console.warn('检查历史匹配失败，继续创建新任务:', error)
        ElMessage.info('正在创建任务...')
      }
    } else {
      ElMessage.info('正在创建任务...')
    }

    // 处理参数类型转换（创建任务时）
    const processedParametersForCreate = processParameterTypes(parametersForm, selectedTool.value.configTemplate)
    console.log('创建任务时处理后的参数:', processedParametersForCreate)

    const taskData = {
      taskName: taskForm.taskName.trim(),
      projectId: parseInt(route.params.projectId),
      typeId: typeId,
      toolId: toolId,
      parameters: processedParametersForCreate || {}, // 使用类型转换后的参数
      description: `使用工具: ${selectedTool.value.toolName}`,
      status: 'pending', // 初始状态为待处理
      progress: 0,
      isDeleted: 0
    }
    
    console.log('创建任务数据:', taskData)
    console.log('参数表单数据:', parametersForm)
    
    // 创建任务
    const res = await addTask(taskData)
    
    console.log('API响应:', res)
    
    if (res.code !== 200) {
      console.error('API错误响应:', res)
      ElMessage.error(res.msg || res.message || '创建任务失败')
      submitting.value = false
      return
    }
    
    const taskId = res.data.taskId
    console.log('任务创建成功，任务ID:', taskId)
    
    // 步骤2: 上传文件并更新任务参数
    const kbId = projectInfo.value.kbId || projectInfo.value.kb_id
    
    if (!kbId) {
      ElMessage.error('项目关联的数据库ID不存在')
      submitting.value = false
      return
    }
    
    // 处理参数类型转换
    const processedParameters = processParameterTypes(parametersForm, selectedTool.value.configTemplate)
    console.log('处理后的参数:', processedParameters)
    
    // 如果有缓存的文件需要上传
    if (Object.keys(cachedFiles.value).length > 0) {
      ElMessage.info('正在上传文件...')
      
      const uploadedFiles = {}
      
      // 上传文件并获取文件路径
      for (const [fieldKey, file] of Object.entries(cachedFiles.value)) {
        try {
          // 根据字段名确定文件类型
          let fileType = 'document' // 默认类型
          let subFolder = null
          if (fieldKey.includes('模型') || fieldKey.includes('model')) {
            fileType = 'model'
            subFolder = 'input'
          } else if (fieldKey.includes('报告') || fieldKey.includes('report')) {
            fileType = 'report'
          } else if (fieldKey.includes('数据') || fieldKey.includes('dataset')) {
            fileType = 'dataset'
          } else if (fieldKey.includes('日志') || fieldKey.includes('log')) {
            fileType = 'log'
          } else if (fieldKey.includes('存档') || fieldKey.includes('压缩') || fieldKey.includes('archive')) {
            fileType = 'archived'
          }

          console.log(`开始上传文件: ${file.name}, 类型: ${fileType}`)

          // 上传文件，传递项目ID、任务ID和子文件夹
          const uploadRes = await uploadFile(kbId, fileType, file, parseInt(route.params.projectId),taskId,subFolder)
          if (uploadRes.code === 200) {
            uploadedFiles[fieldKey] = uploadRes.data.file_path
            // 更新表单中的文件路径
            const propName = generatePropName(fieldKey)
            processedParameters[propName] = uploadRes.data.file_path

            // 如果是模型文件，还需要保存MD5值用于历史任务匹配
            if (fieldKey.toLowerCase().includes('model') && uploadRes.data.file_md5) {
              processedParameters[`${propName}_md5`] = uploadRes.data.file_md5
              console.log(`模型文件MD5: ${uploadRes.data.file_md5}`)
            }

            console.log(`文件上传成功: ${file.name} -> ${uploadRes.data.file_path}`)
          } else {
            ElMessage.error(`文件 ${file.name} 上传失败`)
            submitting.value = false
            return
          }
        } catch (error) {
          console.error(`文件 ${file.name} 上传失败:`, error)
          ElMessage.error(`文件 ${file.name} 上传失败`)
          submitting.value = false
          return
        }
      }
      
      // 步骤3: 更新任务参数（包含文件路径）
      ElMessage.info('正在更新任务参数...')
      try {
        // 调用更新任务API，更新参数
        const updateTaskData = {
          taskId: taskId,
          projectId: parseInt(route.params.projectId),
          taskName: taskForm.taskName.trim(), // 添加任务名称
          typeId: parseInt(selectedTool.value.typeId), // 添加类型ID
          parameters: processedParameters
        }
        
        const updateRes = await updateTask(updateTaskData)
        if (updateRes.code === 200) {
          console.log('任务参数更新成功')
        } else {
          console.warn('任务参数更新失败:', updateRes)
        }
      } catch (error) {
        console.warn('更新任务参数失败:', error)
      }
    } else {
      // 没有文件上传时，直接更新参数
      try {
        const updateTaskData = {
          taskId: taskId,
          projectId: parseInt(route.params.projectId),
          taskName: taskForm.taskName.trim(),
          typeId: parseInt(selectedTool.value.typeId),
          parameters: processedParameters
        }
        
        const updateRes = await updateTask(updateTaskData)
        if (updateRes.code === 200) {
          console.log('任务参数更新成功')
        } else {
          console.warn('任务参数更新失败:', updateRes)
        }
      } catch (error) {
        console.warn('更新任务参数失败:', error)
      }
    }
    
    console.log('API响应:', res)
    
    if (res.code === 200) {
      ElMessage.success('任务创建成功！')

      // 检查是否需要添加项目类型关联
      const currentTypeIds = projectInfo.value.typeIds || projectInfo.value.type_ids || []
      if (!currentTypeIds.includes(selectedTool.value.typeId)) {
        try {
          // 添加项目类型关联
          await addProjectTypeRel({
            projectId: parseInt(route.params.projectId),
            typeId: parseInt(selectedTool.value.typeId)
          })
        } catch (error) {
          // 这个错误不应该阻碍主流程，只在控制台警告
          console.warn('添加项目类型关联失败:', error)
        }
      }

      router.push(`/projects/detail/${route.params.projectId}`)
    } else {
      console.error('API错误响应:', res)
      ElMessage.error(res.msg || res.message || '创建任务失败')
    }
  } catch (error) {
    console.error('创建任务错误:', error)
    
    // 处理不同类型的错误
    if (error.response) {
      // 服务器响应了错误状态码
      const status = error.response.status
      const data = error.response.data
      
      if (status === 422) {
        // 验证错误
        if (data.detail && Array.isArray(data.detail)) {
          const errorMessages = data.detail.map(err => err.msg).join(', ')
          ElMessage.error(`数据验证失败: ${errorMessages}`)
        } else {
          ElMessage.error('数据验证失败，请检查输入的数据格式')
        }
      } else if (status === 401) {
        ElMessage.error('未授权，请重新登录')
      } else if (status === 403) {
        ElMessage.error('权限不足')
      } else {
        ElMessage.error(data.msg || data.message || '创建任务失败')
      }
    } else if (error.request) {
      // 网络错误
      ElMessage.error('网络连接失败，请检查网络连接')
    } else {
      // 其他错误
      ElMessage.error('创建任务失败，请检查任务参数是否完整或稍后重试')
    }
  } finally {
    submitting.value = false
  }
}

// 生命周期
onMounted(() => {
  fetchProjectInfo()
  fetchToolTypes()
  fetchTools()
})

// 参数类型处理函数
const processParameterTypes = (parametersForm, configTemplate) => {
  const processedParams = { ...parametersForm }
  
  if (!configTemplate) {
    return processedParams
  }
  
  // 处理嵌套结构或扁平结构
  let configData = configTemplate
  let orderedKeys = []

  if (configTemplate._order && Array.isArray(configTemplate._order)) {
    // 检查是否为嵌套结构
    const hasNestedSections = configTemplate._order.some(sectionKey => {
      const section = configTemplate[sectionKey]
      return section && typeof section === 'object' && section._order && Array.isArray(section._order)
    })
    
    if (hasNestedSections) {
      // 嵌套结构，展平字段
      const allFields = []
      configTemplate._order.forEach(sectionKey => {
        const section = configTemplate[sectionKey]
        if (section && typeof section === 'object' && section._order) {
          section._order.forEach(fieldKey => {
            allFields.push({
              key: fieldKey,
              value: section[fieldKey]
            })
          })
        }
      })
      
      orderedKeys = allFields.map(field => field.key)
      configData = {}
      allFields.forEach(field => {
        configData[field.key] = field.value
      })
    } else {
      // 扁平结构
      orderedKeys = configTemplate._order
      configData = configTemplate
    }
  } else {
    // 兼容旧格式
    orderedKeys = Object.keys(configTemplate).filter(key => key !== '_order')
  }
  
  // 根据模板默认值类型转换参数类型
  orderedKeys.forEach(key => {
    const propName = generatePropName(key)
    const defaultValue = configData[key]
    const userValue = processedParams[propName]
    
    // 跳过文件路径字段
    if (key.includes('上传路径') || key.includes('模型上传') || key.includes('文件上传') || key.includes('上传文件')) {
      return
    }
    
    console.log(`处理参数类型: ${key} -> ${propName}, 模板默认值:`, defaultValue, `(${typeof defaultValue}), 用户值:`, userValue, `(${typeof userValue})`)
    
    if (defaultValue === null) {
      // 模板默认值为null，应该转换为数值类型（如果用户填写了值且不为空）
      if (userValue !== null && userValue !== undefined && userValue !== '') {
        const numValue = Number(userValue)
        if (!isNaN(numValue)) {
          processedParams[propName] = numValue
          console.log(`✅ 转换为数值: ${propName} = ${numValue}`)
        } else {
          // 如果无法转换为数值，保持原字符串（比如复杂表达式）
          console.log(`⚠️ 无法转换为数值，保持字符串: ${propName} = "${userValue}"`)
        }
      } else {
        // 用户没有填写值，保持null
        processedParams[propName] = null
        console.log(`✅ 保持null: ${propName} = null`)
      }
    } else if (defaultValue === '') {
      // 模板默认值为空字符串，保持字符串类型
      processedParams[propName] = userValue ? String(userValue) : ''
      console.log(`✅ 保持字符串: ${propName} = "${processedParams[propName]}"`)
    } else if (typeof defaultValue === 'number') {
      // 模板默认值为数字，转换为数值类型
      if (userValue !== null && userValue !== undefined && userValue !== '') {
        const numValue = Number(userValue)
        if (!isNaN(numValue)) {
          processedParams[propName] = numValue
          console.log(`✅ 转换为数值: ${propName} = ${numValue}`)
        } else {
          console.log(`⚠️ 无法转换为数值，保持字符串: ${propName} = "${userValue}"`)
        }
      }
    } else {
      // 其他情况保持原样
      console.log(`✅ 保持原样: ${propName} = ${processedParams[propName]}`)
    }
  })
  
  console.log('参数类型处理完成:', processedParams)
  return processedParams
}
</script>

<style scoped>
.app-container {
  padding: 20px;
  background: #f5f7fa;
  min-height: 100vh;
}

.task-creation-container {
  max-width: 1600px;
  margin: 0 auto;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 30px;
  border-bottom: 1px solid #e4e7ed;
  background: #fafafa;
}

.page-header h2 {
  margin: 0;
  color: #303133;
}

.main-content {
  display: flex;
  min-height: 600px;
}

.left-panel {
  width: 350px;
  border-right: 1px solid #e4e7ed;
  background: #fafafa;
}

.right-panel {
  flex: 1;
  padding: 30px;
  min-width: 550px;
}

.tool-intro-section,
.parameter-guide-section {
  padding: 20px;
  border-bottom: 1px solid #e4e7ed;
}

.tool-intro-section h3,
.parameter-guide-section h3 {
  margin: 0 0 15px 0;
  color: #303133;
  font-size: 16px;
}

.no-tool-selected {
  text-align: center;
  padding: 20px 0;
}

.tool-info {
  background: white;
  border-radius: 6px;
  padding: 15px;
  border: 1px solid #e4e7ed;
}

.tool-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.tool-header h4 {
  margin: 0;
  color: #303133;
}

.tool-details p {
  margin: 5px 0;
  color: #606266;
  font-size: 14px;
}

.guide-content {
  background: white;
  border-radius: 6px;
  padding: 15px;
  border: 1px solid #e4e7ed;
}

.guide-content p {
  margin: 0 0 10px 0;
  color: #606266;
  font-weight: 500;
}

.guide-content ol {
  margin: 0;
  padding-left: 20px;
  color: #606266;
}

.guide-content li {
  margin: 5px 0;
  line-height: 1.5;
}

.parameter-config-section h3 {
  margin: 0 0 20px 0;
  color: #303133;
  font-size: 18px;
}

.project-info-section,
.task-info-section,
.parameters-section {
  margin-bottom: 30px;
}

.project-info-section h4,
.task-info-section h4,
.parameters-section h4 {
  margin: 0 0 15px 0;
  color: #303133;
  font-size: 16px;
  padding-bottom: 8px;
  border-bottom: 2px solid #409eff;
}

.no-tool-selected-hint,
.no-config-template-hint {
  margin: 20px 0;
}

.parameter-description {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
  line-height: 1.4;
}

.action-buttons {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-top: 40px;
  padding-top: 20px;
  border-top: 1px solid #e4e7ed;
}

/* 工具选择器样式 */
.tool-selector {
  max-height: 500px;
  overflow-y: auto;
}

.tool-filter {
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #e4e7ed;
}

.tool-search {
  margin-bottom: 20px;
}

.tools-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 15px;
}

.tool-card {
  cursor: pointer;
  transition: all 0.3s ease;
}

.tool-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.tool-card-content {
  padding: 10px;
}

.tool-name {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 8px;
}

.tool-description {
  color: #606266;
  font-size: 14px;
  margin-bottom: 10px;
  line-height: 1.4;
}

.tool-meta {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #909399;
}

.tool-version,
.tool-vendor {
  background: #f5f7fa;
  padding: 2px 6px;
  border-radius: 3px;
}

/* 响应式设计 */
@media (max-width: 1400px) {
  .task-creation-container {
    max-width: 1400px;
  }
  
  .left-panel {
    width: 320px;
  }
  
  .right-panel {
    min-width: 500px;
  }
}

@media (max-width: 1200px) {
  .main-content {
    flex-direction: column;
  }
  
  .left-panel {
    width: 100%;
    border-right: none;
    border-bottom: 1px solid #e4e7ed;
  }
  
  .right-panel {
    min-width: auto;
  }
  
  .form-grid {
    grid-template-columns: 1fr;
  }
  
  .form-item {
    width: 100%;
  }
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: 15px;
    align-items: flex-start;
  }
  
  .action-buttons {
    flex-direction: column;
  }
  
  .tools-list {
    grid-template-columns: 1fr;
  }
}

/* 表单网格样式 */
.form-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.form-item {
  width: calc(50% - 8px);
}

.form-label {
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 8px;
  color: #606266;
}

.form-input {
  width: 100%;
  height: 50px;
}

.form-input.has-unit {
  padding-right: 25px;
}

.file-upload-input {
  height: auto;
  min-height: 50px;
}

.file-upload-input :deep(.upload-file) {
  width: 100%;
}

.file-upload-input :deep(.upload-file-uploader) {
  margin-bottom: 10px;
}

.file-upload-input :deep(.el-upload__tip) {
  margin: 5px 0;
  color: #909399;
  font-size: 12px;
}

.input-wrapper {
  position: relative;
}

.unit-label {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  color: #909399;
  font-size: 13px;
}
</style> 