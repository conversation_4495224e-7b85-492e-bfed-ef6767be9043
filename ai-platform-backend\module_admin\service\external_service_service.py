import time
import aiohttp
import asyncio
from datetime import datetime
from typing import List
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update

from module_admin.entity.vo.external_service_vo import ExternalServiceHealthModel, ExternalServiceMonitorModel
from module_business.entity.do.tools_do import RdTools
from utils.log_util import logger


class ExternalServiceService:
    """
    外部服务监控模块服务层
    """

    @classmethod
    async def get_external_services_monitor_info(cls, db: AsyncSession) -> ExternalServiceMonitorModel:
        """
        获取外部服务监控信息

        :param db: 数据库会话
        :return: 外部服务监控信息
        """
        try:
            # 获取所有激活的工具
            query = (
                select(RdTools)
                .where(
                    RdTools.is_active == 1,
                    RdTools.is_deleted == 0
                )
            )
            result = await db.execute(query)
            tools = result.scalars().all()

            services = []
            healthy_count = 0
            unhealthy_count = 0

            for tool in tools:
                # 如果工具没有executable_api，标记为不可用
                if not tool.executable_api:
                    service_health = ExternalServiceHealthModel(
                        toolId=tool.tool_id,
                        toolName=tool.tool_name,
                        executableApi=tool.executable_api,
                        healthUrl="",
                        status="unavailable",
                        responseTime=None,
                        lastCheckTime=datetime.now(),
                        errorMessage="未配置API地址",
                        isActive=tool.is_active,
                        isHealth=tool.is_health
                    )
                    unhealthy_count += 1
                    services.append(service_health)
                    continue
                
                # 根据数据库中的is_health字段判断状态
                if tool.is_health == 1:
                    status = "healthy"
                    healthy_count += 1
                    error_message = None
                else:
                    status = "unhealthy"
                    unhealthy_count += 1
                    error_message = tool.remark or "服务异常"

                service_health = ExternalServiceHealthModel(
                    toolId=tool.tool_id,
                    toolName=tool.tool_name,
                    executableApi=tool.executable_api,
                    healthUrl=tool.health_url or "",
                    status=status,
                    responseTime=None,
                    lastCheckTime=datetime.now(),
                    errorMessage=error_message,
                    isActive=tool.is_active,
                    isHealth=tool.is_health
                )
                services.append(service_health)

            return ExternalServiceMonitorModel(
                totalServices=len(services),
                healthyServices=healthy_count,
                unhealthyServices=unhealthy_count,
                services=services,
                lastUpdateTime=datetime.now()
            )

        except Exception as e:
            logger.error(f"获取外部服务监控信息失败: {e}")
            return ExternalServiceMonitorModel(
                totalServices=0,
                healthyServices=0,
                unhealthyServices=0,
                services=[],
                lastUpdateTime=datetime.now()
            )

    @classmethod
    async def test_service_health(cls, db: AsyncSession, tool_id: int) -> dict:
        """
        测试单个服务的健康状态

        :param db: 数据库会话
        :param tool_id: 工具ID
        :return: 测试结果
        """
        try:
            # 获取工具信息
            query = select(RdTools).where(RdTools.tool_id == tool_id)
            result = await db.execute(query)
            tool = result.scalar_one_or_none()
            
            if not tool:
                return {
                    "success": False,
                    "message": "工具不存在",
                    "status": "error"
                }
            
            if not tool.executable_api:
                return {
                    "success": False,
                    "message": "工具未配置API地址",
                    "status": "unavailable"
                }
            
            # 使用数据库中的health_url字段
            health_url = tool.health_url
            if not health_url:
                return {
                    "success": False,
                    "message": "工具未配置健康检查地址",
                    "status": "unavailable"
                }
            
            start_time = time.time()
            
            try:
                async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=10)) as session:
                    async with session.get(health_url) as response:
                        response_time = round((time.time() - start_time) * 1000, 2)
                        
                        if response.status == 200:
                            # 健康检查成功
                            await cls._update_tool_health_status(db, tool_id, 1, None)
                            return {
                                "success": True,
                                "message": "服务健康",
                                "status": "healthy",
                                "responseTime": response_time,
                                "healthUrl": health_url
                            }
                        else:
                            # 健康检查失败
                            error_msg = f"服务异常，HTTP状态码: {response.status}"
                            await cls._update_tool_health_status(db, tool_id, 0, error_msg)
                            return {
                                "success": False,
                                "message": error_msg,
                                "status": "unhealthy",
                                "responseTime": response_time,
                                "healthUrl": health_url
                            }
                            
            except asyncio.TimeoutError:
                # 请求超时
                error_msg = "请求超时"
                await cls._update_tool_health_status(db, tool_id, 0, error_msg)
                return {
                    "success": False,
                    "message": error_msg,
                    "status": "unhealthy",
                    "responseTime": None,
                    "healthUrl": health_url
                }
            except Exception as e:
                # 其他错误
                error_msg = f"连接失败: {str(e)}"
                await cls._update_tool_health_status(db, tool_id, 0, error_msg)
                return {
                    "success": False,
                    "message": error_msg,
                    "status": "unhealthy",
                    "responseTime": None,
                    "healthUrl": health_url
                }
                
        except Exception as e:
            logger.error(f"测试服务健康状态失败: {e}")
            return {
                "success": False,
                "message": f"测试失败: {str(e)}",
                "status": "error"
            }

    @staticmethod
    async def _update_tool_health_status(db: AsyncSession, tool_id: int, is_health: int, remark: str = None):
        """
        更新工具的健康状态和错误信息

        :param db: 数据库会话
        :param tool_id: 工具ID
        :param is_health: 健康状态 (1: 健康, 0: 异常)
        :param remark: 错误信息
        """
        try:
            stmt = (
                update(RdTools)
                .where(RdTools.tool_id == tool_id)
                .values(is_health=is_health, remark=remark)
            )
            await db.execute(stmt)
            await db.commit()
            logger.info(f"更新工具 {tool_id} 健康状态为: {is_health}, 错误信息: {remark}")
        except Exception as e:
            logger.error(f"更新工具健康状态失败: {e}")
            await db.rollback() 