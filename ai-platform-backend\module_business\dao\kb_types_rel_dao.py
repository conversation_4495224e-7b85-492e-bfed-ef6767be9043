from sqlalchemy import delete, select, update
from sqlalchemy.ext.asyncio import AsyncSession
from module_business.entity.do.kb_types_rel_do import RdKbTypesRel
from module_business.entity.vo.kb_types_rel_vo import KbTypesRelModel, KbTypesRelPageQueryModel
from utils.page_util import PageUtil
from typing import List
from module_business.entity.do.knowledge_bases_do import RdKnowledgeBases
from module_business.entity.do.types_do import RdSysTypes


class KbTypesRelDao:
    """
    数据库-类型关联关系模块数据库操作层
    """

    @classmethod
    async def get_kb_types_rel_detail_by_id(cls, db: AsyncSession, kb_id: int):
        """
        根据数据库id获取数据库-类型关联关系详细信息

        :param db: orm对象
        :param kb_id: 数据库id
        :return: 数据库-类型关联关系信息对象
        """
        kb_types_rel_info = (
            (
                await db.execute(
                    select(RdKbTypesRel)
                    .where(
                        RdKbTypesRel.kb_id == kb_id
                    )
                )
            )
            .scalars()
            .first()
        )

        return kb_types_rel_info

    @classmethod
    async def get_kb_types_rel_detail_by_info(cls, db: AsyncSession, kb_types_rel: KbTypesRelModel):
        """
        根据数据库-类型关联关系参数获取数据库-类型关联关系信息

        :param db: orm对象
        :param kb_types_rel: 数据库-类型关联关系参数对象
        :return: 数据库-类型关联关系信息对象
        """
        kb_types_rel_info = (
            (
                await db.execute(
                    select(RdKbTypesRel).where(
                    )
                )
            )
            .scalars()
            .first()
        )

        return kb_types_rel_info

    @classmethod
    async def get_kb_types_rel_list(cls, db: AsyncSession, query_object: KbTypesRelPageQueryModel, is_page: bool = False):
        """
        根据查询参数获取数据库-类型关联关系列表信息

        :param db: orm对象
        :param query_object: 查询参数对象
        :param is_page: 是否开启分页
        :return: 数据库-类型关联关系列表信息对象
        """
        query = (
            select(RdKbTypesRel)
            .where(
                RdKbTypesRel.assigned_at == query_object.assigned_at if query_object.assigned_at else True,
                RdKbTypesRel.assigned_by == query_object.assigned_by if query_object.assigned_by else True,
            )
            .order_by(RdKbTypesRel.kb_id)
            .distinct()
        )
        kb_types_rel_list = await PageUtil.paginate(db, query, query_object.page_num, query_object.page_size, is_page)

        return kb_types_rel_list

    @classmethod
    async def add_kb_types_rel_dao(cls, db: AsyncSession, kb_types_rel: KbTypesRelModel):
        """
        新增数据库-类型关联关系数据库操作

        :param db: orm对象
        :param kb_types_rel: 数据库-类型关联关系对象
        :return:
        """
        db_kb_types_rel = RdKbTypesRel(**kb_types_rel.model_dump(exclude={}))
        db.add(db_kb_types_rel)
        await db.flush()

        return db_kb_types_rel

    @classmethod
    async def edit_kb_types_rel_dao(cls, db: AsyncSession, kb_types_rel: dict):
        """
        编辑数据库-类型关联关系数据库操作

        :param db: orm对象
        :param kb_types_rel: 需要更新的数据库-类型关联关系字典
        :return:
        """
        await db.execute(update(RdKbTypesRel), [kb_types_rel])

    @classmethod
    async def delete_kb_types_rel_dao(cls, db: AsyncSession, kb_types_rel: KbTypesRelModel):
        """
        删除数据库-类型关联关系数据库操作

        :param db: orm对象
        :param kb_types_rel: 数据库-类型关联关系对象
        :return:
        """
        await db.execute(delete(RdKbTypesRel).where(RdKbTypesRel.kb_id.in_([kb_types_rel.kb_id])))

    @classmethod
    async def delete_kb_type_rel_by_kb_and_type(cls, db: AsyncSession, kb_id: int, type_id: int):
        """
        根据数据库ID和类型ID删除特定的数据库-类型关联关系

        :param db: orm对象
        :param kb_id: 数据库ID
        :param type_id: 类型ID
        :return:
        """
        await db.execute(
            delete(RdKbTypesRel).where(
                RdKbTypesRel.kb_id == kb_id,
                RdKbTypesRel.type_id == type_id
            )
        )

    @classmethod
    async def get_kb_types_rel_by_kb_id_and_type_id(cls, db: AsyncSession, kb_id: int, type_id: int):
        """
        根据数据库ID和类型ID查询关联关系是否存在

        :param db: orm对象
        :param kb_id: 数据库ID
        :param type_id: 类型ID
        :return: 关联关系对象，如果不存在则返回None
        """
        query = select(RdKbTypesRel).where(
            RdKbTypesRel.kb_id == kb_id, 
            RdKbTypesRel.type_id == type_id
        )
        result = await db.execute(query)
        return result.scalars().first()

    @classmethod
    async def get_kb_types_rel_list_by_kb_id(cls, db: AsyncSession, kb_id: int):
        """
        根据数据库ID获取所有关联的类型信息

        :param db: orm对象
        :param kb_id: 数据库ID
        :return: 数据库关联的所有类型信息列表
        """
        query = (
            select(RdKbTypesRel)
            .where(RdKbTypesRel.kb_id == kb_id)
            .order_by(RdKbTypesRel.assigned_at.desc())
        )
        result = await db.execute(query)
        return result.scalars().all()

    @classmethod
    async def get_knowledge_bases_by_types(cls, db: AsyncSession, type_ids: List[int], page_num: int = 1, page_size: int = 10, kb_name: str = None):
        """
        根据类型ID列表获取数据库列表，支持分页和搜索

        :param db: orm对象
        :param type_ids: 类型ID列表
        :param page_num: 页码
        :param page_size: 每页大小
        :param kb_name: 数据库名称搜索
        :return: 数据库列表
        """
        # 构建基础查询
        if type_ids and len(type_ids) > 0:
            # 为每个类型ID创建一个子查询
            subqueries = []
            for type_id in type_ids:
                subquery = (
                    select(RdKbTypesRel.kb_id)
                    .where(
                        RdKbTypesRel.type_id == type_id
                    )
                    .scalar_subquery()
                )
                subqueries.append(subquery)

            # 主查询：查找同时满足所有类型的数据库
            query = (
                select(RdKnowledgeBases)
                .where(
                    RdKnowledgeBases.kb_id.in_(subqueries[0]),
                    RdKnowledgeBases.is_deleted == False
                )
            )

            # 添加其他类型的条件
            for subquery in subqueries[1:]:
                query = query.where(RdKnowledgeBases.kb_id.in_(subquery))
        else:
            # 如果没有指定类型，获取所有数据库
            query = (
                select(RdKnowledgeBases)
                .where(RdKnowledgeBases.is_deleted == False)
            )
        
        # 添加名称搜索条件
        if kb_name:
            query = query.where(RdKnowledgeBases.kb_name.like(f'%{kb_name}%'))
        
        # 添加排序
        query = query.order_by(RdKnowledgeBases.kb_id.desc())
        
        # 执行分页查询
        result = await PageUtil.paginate(db, query, page_num, page_size, True)
        
        # 将PageResponseModel转换为字典
        if hasattr(result, 'model_dump'):
            result = result.model_dump()
        elif hasattr(result, 'dict'):
            result = result.dict()
        
        # 为每个数据库获取类型信息和负责人信息
        kb_list = result.get('rows', [])
        kb_with_types = []
        
        # 收集所有数据库的owner_id
        owner_ids = set()
        for kb in kb_list:
            if hasattr(kb, 'owner_id'):
                owner_id = kb.owner_id
            else:
                owner_id = kb.get('ownerId')
            if owner_id:
                owner_ids.add(owner_id)
        
        # 批量获取用户信息
        users_info = {}
        if owner_ids:
            from module_admin.entity.do.user_do import SysUser
            
            user_query = select(SysUser).where(SysUser.user_id.in_(owner_ids))
            user_result = await db.execute(user_query)
            users = user_result.scalars().all()
            
            for user in users:
                users_info[user.user_id] = user.nick_name or user.user_name
        
        for kb in kb_list:
            # 处理数据库对象，可能是ORM对象或字典
            if hasattr(kb, 'kb_id'):
                # ORM对象
                kb_dict = {
                    'kb_id': kb.kb_id,
                    'kb_name': kb.kb_name,
                    'kb_description': kb.kb_description,
                    'project_id': kb.project_id,
                    'owner_id': kb.owner_id,
                    'created_at': kb.created_at,
                    'updated_at': kb.updated_at,
                    'created_by': kb.created_by,
                    'updated_by': kb.updated_by,
                    'is_deleted': kb.is_deleted,
                    'type_ids': [],
                    'type_names': []
                }
                kb_id = kb.kb_id
                owner_id = kb.owner_id
            else:
                # 字典对象 - 使用驼峰格式字段名
                kb_dict = {
                    'kb_id': kb.get('kbId'),
                    'kb_name': kb.get('kbName'),
                    'description': kb.get('description'),
                    'kb_description': kb.get('description'),
                    'project_id': kb.get('projectId'),
                    'owner_id': kb.get('ownerId'),
                    'created_at': kb.get('createdAt'),
                    'updated_at': kb.get('updatedAt'),
                    'created_by': kb.get('ownerId'),
                    'updated_by': kb.get('ownerId'),
                    'is_deleted': kb.get('isDeleted'),
                    'type_ids': [],
                    'type_names': []
                }
                kb_id = kb.get('kbId')
                owner_id = kb.get('ownerId')
            
            # 获取该数据库关联的所有类型
            if kb_id:
                types_query = (
                    select(RdSysTypes)
                    .join(RdKbTypesRel, RdSysTypes.type_id == RdKbTypesRel.type_id)
                    .where(RdKbTypesRel.kb_id == kb_id)
                )
                types_result = await db.execute(types_query)
                types = types_result.scalars().all()
                
                for type_obj in types:
                    kb_dict['type_ids'].append(type_obj.type_id)
                    kb_dict['type_names'].append(type_obj.display_name)
            
            # 添加负责人信息
            if owner_id and owner_id in users_info:
                kb_dict['owner_name'] = users_info[owner_id]
            else:
                kb_dict['owner_name'] = '未知用户'
            
            kb_with_types.append(kb_dict)
        
        # 更新结果
        result['rows'] = kb_with_types
        return result

