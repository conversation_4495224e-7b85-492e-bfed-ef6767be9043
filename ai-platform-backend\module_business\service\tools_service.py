from sqlalchemy.ext.asyncio import AsyncSession
from typing import List
from config.constant import CommonConstant
from exceptions.exception import ServiceException
from module_admin.entity.vo.common_vo import CrudResponseModel
from module_business.dao.tools_dao import ToolsDao
from module_business.entity.vo.tools_vo import DeleteToolsModel, ToolsModel, ToolsPageQueryModel
from utils.common_util import CamelCaseUtil
from utils.excel_util import ExcelUtil


class ToolsService:
    """
    工具管理模块服务层
    """

    @classmethod
    async def get_tools_list_services(
        cls, query_db: AsyncSession, query_object: ToolsPageQueryModel, is_page: bool = False
    ):
        """
        获取工具管理列表信息service

        :param query_db: orm对象
        :param query_object: 查询参数对象
        :param is_page: 是否开启分页
        :return: 工具管理列表信息对象
        """
        tools_list_result = await ToolsDao.get_tools_list(query_db, query_object, is_page)

        return tools_list_result

    @classmethod
    async def check_tool_id_unique_services(cls, query_db: AsyncSession, page_object: ToolsModel):
        """
        检查工具id是否唯一service

        :param query_db: orm对象
        :param page_object: 工具管理对象
        :return: 校验结果
        """
        tool_id = -1 if page_object.tool_id is None else page_object.tool_id
        tools = await ToolsDao.get_tools_detail_by_info(query_db, ToolsModel(toolId=page_object.tool_id))
        if tools and tools.tool_id != tool_id:
            return CommonConstant.NOT_UNIQUE
        return CommonConstant.UNIQUE


    @classmethod
    async def add_tools_services(cls, query_db: AsyncSession, page_object: ToolsModel):
        """
        新增工具管理信息service

        :param query_db: orm对象
        :param page_object: 新增工具管理对象
        :return: 新增工具管理校验结果
        """
        if not await cls.check_tool_id_unique_services(query_db, page_object):
            raise ServiceException(message=f'新增工具管理{page_object.tool_id}失败，工具id已存在')
        try:
            await ToolsDao.add_tools_dao(query_db, page_object)
            await query_db.commit()
            return CrudResponseModel(is_success=True, message='新增成功')
        except Exception as e:
            await query_db.rollback()
            raise e

    @classmethod
    async def edit_tools_services(cls, query_db: AsyncSession, page_object: ToolsModel):
        """
        编辑工具管理信息service

        :param query_db: orm对象
        :param page_object: 编辑工具管理对象
        :return: 编辑工具管理校验结果
        """
        edit_tools = page_object.model_dump(exclude_unset=True, exclude={})
        tools_info = await cls.tools_detail_services(query_db, page_object.tool_id)
        if tools_info.tool_id:
            if not await cls.check_tool_id_unique_services(query_db, page_object):
                raise ServiceException(message=f'修改工具管理{page_object.tool_id}失败，工具id已存在')
            try:
                await ToolsDao.edit_tools_dao(query_db, edit_tools)
                await query_db.commit()
                return CrudResponseModel(is_success=True, message='更新成功')
            except Exception as e:
                await query_db.rollback()
                raise e
        else:
            raise ServiceException(message='工具管理不存在')



    @classmethod
    async def tools_detail_services(cls, query_db: AsyncSession, tool_id: int):
        """
        获取工具管理详细信息service

        :param query_db: orm对象
        :param tool_id: 工具id
        :return: 工具id对应的信息
        """
        tools = await ToolsDao.get_tools_detail_by_id(query_db, tool_id=tool_id)
        if tools:
            result = ToolsModel(**CamelCaseUtil.transform_result(tools))
        else:
            result = ToolsModel(**dict())

        return result

    @staticmethod
    async def export_tools_list_services(tools_list: List):
        """
        导出工具管理信息service

        :param tools_list: 工具管理信息列表
        :return: 工具管理信息对应excel的二进制数据
        """
        # 创建一个映射字典，将英文键映射到中文键
        mapping_dict = {
            'toolId': '工具id',
            'toolName': '工具名称',
            'typeId': '关联rd_sys_types中的类型',
            'description': '工具描述',
            'version': '版本号',
            'vendor': '供应商',
            'executableApi': '接口调用路径',
            'configTemplate': '配置模板(JSON格式)',
            'isActive': '是否激活',
            'isDeleted': '是否删除',
            'queueRequired': '是否需要队列',
            'createdAt': '创建时间',
            'updatedAt': '更新时间',
        }
        binary_data = ExcelUtil.export_list2excel(tools_list, mapping_dict)

        return binary_data
