#app {

  .main-container {
    min-height: 100%;
    transition: margin-left .28s;
    margin-left: $base-sidebar-width;
    position: relative;
  }

  .sidebarHide {
    margin-left: 0!important;
  }

  .sidebar-container {
    transition: width 0.28s;
    width: $base-sidebar-width !important;
    height: 100%;
    position: fixed;
    font-size: 0px;
    top: 0;
    bottom: 0;
    left: 0;
    z-index: 1001;
    overflow: hidden;
    box-shadow: 2px 0 8px rgba(0,0,0,0.08);
    background: #1a2746 !important;
    color: #fff;

    // reset element-ui css
    .horizontal-collapse-transition {
      transition: 0s width ease-in-out, 0s padding-left ease-in-out, 0s padding-right ease-in-out;
    }

    .scrollbar-wrapper {
      overflow-x: hidden !important;
    }

    .el-scrollbar__bar.is-vertical {
      right: 0px;
    }

    .el-scrollbar {
      height: 100%;
    }

    &.has-logo {
      .el-scrollbar {
        height: calc(100% - 50px);
      }
    }

    .is-horizontal {
      display: none;
    }

    a {
      display: inline-block;
      width: 100%;
      overflow: hidden;
    }

    .svg-icon {
      margin-right: 16px;
    }

    .el-menu {
      border: none;
      height: 100%;
      width: 100% !important;
      background: transparent !important;
      
      .el-menu-item, .el-sub-menu__title {
        color: #fff !important;
        font-weight: 500;
        
        &:hover {
          background-color: rgba(37, 99, 235, 0.15) !important;
          color: #fff !important;
        }
        
        &.is-active {
          background-color: #2563eb !important;
          color: #fff !important;
          border-right: 3px solid #2563eb;
        }
      }
      .el-menu-item .el-icon, .el-sub-menu__title .el-icon, .svg-icon {
        color: #fff !important;
      }
    }

    .el-menu-item, .menu-title {
      overflow: hidden !important;
      text-overflow: ellipsis !important;
      white-space: nowrap !important;
    }

    .el-menu-item .el-menu-tooltip__trigger {
      display: inline-block !important;
    }

    // menu hover
    .sub-menu-title-noDropdown,
    .el-sub-menu__title {
      &:hover {
        background-color: rgba(0, 0, 0, 0.06) !important;
      }
    }

    & .theme-dark .is-active > .el-sub-menu__title {
      color: $base-menu-color-active !important;
    }

    & .nest-menu .el-sub-menu>.el-sub-menu__title,
    & .el-sub-menu .el-menu-item {
      min-width: $base-sidebar-width !important;

      &:hover {
        background-color: rgba(0, 0, 0, 0.06) !important;
      }
    }

    & .theme-dark .nest-menu .el-sub-menu>.el-sub-menu__title,
    & .theme-dark .el-sub-menu .el-menu-item {
      background-color: $base-sub-menu-background;

      &:hover {
        background-color: $base-sub-menu-hover !important;
      }
    }

    .sidebar-action-link {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: flex-start;
      text-align: left;
      font-weight: 500;
      font-size: 15px;
      line-height: 48px;
      height: 48px;
      letter-spacing: 1px;
      color: #fff;
      background: none;
      border: none;
      margin: 0 auto;
      padding: 0 20px;
      box-sizing: border-box;
      transition: color 0.2s;
      overflow: hidden;
      white-space: nowrap;
      .el-icon {
        color: #fff !important;
      }
      &:hover {
        color: #fff;
        .el-icon {
          color: #fff !important;
        }
      }
    }

    .el-menu-item > .svg-icon {
      color: #000 !important;
      fill: #000 !important;
    }

    /* Force icon color to black */
    .el-menu-item .el-icon,
    .el-sub-menu__title .el-icon {
      color: #000 !important;
    }
    .el-menu-item .el-icon svg,
    .el-sub-menu__title .el-icon svg {
      fill: #000 !important;
    }
    .el-menu-item .el-icon svg path,
    .el-sub-menu__title .el-icon svg path {
      fill: #000 !important;
    }

    /* Aggressive override for all icons inside menu items */
    .el-menu-item svg, .el-sub-menu__title svg {
      color: #fff !important;
      fill: #fff !important;
    }
    .el-menu-item svg path, .el-sub-menu__title svg path {
      fill: #fff !important;
    }
  }

  .hideSidebar {
    .sidebar-container {
      width: 54px !important;
    }

    .main-container {
      margin-left: 54px;
    }

    .sub-menu-title-noDropdown {
      padding: 0 !important;
      position: relative;

      .el-tooltip {
        padding: 0 !important;

        .svg-icon {
          margin-left: 20px;
        }
      }
    }

    .el-sub-menu {
      overflow: hidden;

      &>.el-sub-menu__title {
        padding: 0 !important;

        .svg-icon {
          margin-left: 20px;
        }

      }
    }

    .el-menu--collapse {
      .el-sub-menu {
        &>.el-sub-menu__title {
          &>span {
            height: 0;
            width: 0;
            overflow: hidden;
            visibility: hidden;
            display: inline-block;
          }
          &>i {
            height: 0;
            width: 0;
            overflow: hidden;
            visibility: hidden;
            display: inline-block;
          }
        }
      }
    }
  }

  .el-menu--collapse .el-menu .el-sub-menu {
    min-width: $base-sidebar-width !important;
  }

  // mobile responsive
  .mobile {
    .main-container {
      margin-left: 0px;
    }

    .sidebar-container {
      transition: transform .28s;
      width: $base-sidebar-width !important;
    }

    &.hideSidebar {
      .sidebar-container {
        pointer-events: none;
        transition-duration: 0.3s;
        transform: translate3d(-$base-sidebar-width, 0, 0);
      }
    }
  }

  .withoutAnimation {

    .main-container,
    .sidebar-container {
      transition: none;
    }
  }
}

// when menu collapsel-m
.el-menu-item svg use {
  fill: #fff !important;
}
enu--vertical {
  &>.el-menu {
    .svg-icon {
      margin-right: 16px;
    }
  }

  .nest-menu .el-sub-menu>.el-sub-menu__title,
  .el-menu-item {
    &:hover {
      // you can use $sub-menuHover
      background-color: rgba(0, 0, 0, 0.06) !important;
    }
  }

  // the scroll bar appears when the sub-menu is too long
  >.el-menu--popup {
    max-height: 100vh;
    overflow-y: auto;

    &::-webkit-scrollbar-track-piece {
      background: #d3dce6;
    }

    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-thumb {
      background: #99a9bf;
      border-radius: 20px;
    }
  }
  
}
