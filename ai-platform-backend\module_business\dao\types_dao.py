from sqlalchemy import delete, select, update
from sqlalchemy.ext.asyncio import AsyncSession
from module_business.entity.do.types_do import RdSysTypes
from module_business.entity.vo.types_vo import TypesModel, TypesPageQueryModel
from utils.page_util import PageUtil


class TypesDao:
    """
    通用类型管理模块数据库操作层
    """

    @classmethod
    async def get_types_detail_by_id(cls, db: AsyncSession, type_id: int):
        """
        根据获取通用类型管理详细信息

        :param db: orm对象
        :param type_id: 
        :return: 通用类型管理信息对象
        """
        types_info = (
            (
                await db.execute(
                    select(RdSysTypes)
                    .where(
                        RdSysTypes.type_id == type_id
                    )
                )
            )
            .scalars()
            .first()
        )

        return types_info

    @classmethod
    async def get_types_detail_by_info(cls, db: AsyncSession, types: TypesModel):
        """
        根据通用类型管理参数获取通用类型管理信息

        :param db: orm对象
        :param types: 通用类型管理参数对象
        :return: 通用类型管理信息对象
        """
        types_info = (
            (
                await db.execute(
                    select(RdSysTypes).where(
                    )
                )
            )
            .scalars()
            .first()
        )

        return types_info

    @classmethod
    async def get_types_list(cls, db: AsyncSession, query_object: TypesPageQueryModel, is_page: bool = False):
        """
        根据查询参数获取通用类型管理列表信息

        :param db: orm对象
        :param query_object: 查询参数对象
        :param is_page: 是否开启分页
        :return: 通用类型管理列表信息对象
        """
        query = (
            select(RdSysTypes)
            .where(
                RdSysTypes.type_name.like(f'%{query_object.type_name}%') if query_object.type_name else True,
                RdSysTypes.display_name.like(f'%{query_object.display_name}%') if query_object.display_name else True,
                RdSysTypes.color_code == query_object.color_code if query_object.color_code else True,
                RdSysTypes.is_active == query_object.is_active if query_object.is_active else True,
                RdSysTypes.is_deleted == False,
                RdSysTypes.created_at == query_object.created_at if query_object.created_at else True,
            )
            .order_by(RdSysTypes.type_id)
            .distinct()
        )
        types_list = await PageUtil.paginate(db, query, query_object.page_num, query_object.page_size, is_page)

        return types_list

    @classmethod
    async def add_types_dao(cls, db: AsyncSession, types: TypesModel):
        """
        新增通用类型管理数据库操作

        :param db: orm对象
        :param types: 通用类型管理对象
        :return:
        """
        db_types = RdSysTypes(**types.model_dump(exclude={}))
        db.add(db_types)
        await db.flush()

        return db_types

    @classmethod
    async def edit_types_dao(cls, db: AsyncSession, types: dict):
        """
        编辑通用类型管理数据库操作

        :param db: orm对象
        :param types: 需要更新的通用类型管理字典
        :return:
        """
        await db.execute(update(RdSysTypes), [types])

    @classmethod
    async def delete_types_dao(cls, db: AsyncSession, types: TypesModel):
        """
        删除通用类型管理数据库操作

        :param db: orm对象
        :param types: 通用类型管理对象
        :return:
        """
        await db.execute(delete(RdSysTypes).where(RdSysTypes.type_id.in_([types.type_id])))

