"""
权限验证工具类
"""
from typing import List, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from config.role_constants import RoleConstants
from exceptions.exception import PermissionException
from module_admin.entity.vo.user_vo import CurrentUserModel
from module_business.dao.project_members_dao import ProjectMembersDao
from module_business.dao.projects_dao import ProjectsDao
from module_business.entity.vo.projects_vo import ProjectsModel


class PermissionUtil:
    """
    权限验证工具类
    """

    @staticmethod
    def check_super_admin(user_role_keys: List[str]) -> bool:
        """
        检查是否为超级管理员

        :param user_role_keys: 用户角色键值列表
        :return: 是否为超级管理员
        """
        return RoleConstants.is_super_admin(user_role_keys)

    @staticmethod
    def check_project_manager(user_role_keys: List[str]) -> bool:
        """
        检查是否为项目管理员

        :param user_role_keys: 用户角色键值列表
        :return: 是否为项目管理员
        """
        return RoleConstants.is_project_manager(user_role_keys)

    @staticmethod
    def check_project_member(user_role_keys: List[str]) -> bool:
        """
        检查是否为项目成员

        :param user_role_keys: 用户角色键值列表
        :return: 是否为项目成员
        """
        return RoleConstants.is_project_member(user_role_keys)

    @staticmethod
    def check_common_user(user_role_keys: List[str]) -> bool:
        """
        检查是否为普通用户

        :param user_role_keys: 用户角色键值列表
        :return: 是否为普通用户
        """
        return RoleConstants.is_common_user(user_role_keys)

    @staticmethod
    async def check_project_permission(
        query_db: AsyncSession,
        current_user: CurrentUserModel,
        project_id: int,
        required_role: Optional[str] = None,
        allow_owner: bool = True
    ) -> bool:
        """
        检查用户对项目的权限

        :param query_db: 数据库会话
        :param current_user: 当前用户
        :param project_id: 项目ID
        :param required_role: 需要的角色类型
        :param allow_owner: 是否允许项目所有者访问
        :return: 是否有权限
        """
        user_id = current_user.user.user_id
        user_role_keys = [role.role_key for role in current_user.user.role]

        # 超级管理员拥有所有权限
        if PermissionUtil.check_super_admin(user_role_keys):
            return True

        # 检查项目是否存在
        project = await ProjectsDao.get_projects_detail_by_info(
            query_db, ProjectsModel(project_id=project_id)
        )
        if not project:
            raise PermissionException(message='项目不存在')

        # 检查是否为项目所有者
        if allow_owner and project.owner_id == user_id:
            return True

        # 检查项目成员权限
        has_permission = await ProjectMembersDao.check_user_project_permission(
            query_db, user_id, project_id, required_role
        )

        return has_permission

    @staticmethod
    async def check_task_edit_permission(
        query_db: AsyncSession,
        current_user: CurrentUserModel,
        task_id: int
    ) -> bool:
        """
        检查任务编辑权限

        :param query_db: 数据库会话
        :param current_user: 当前用户
        :param task_id: 任务ID
        :return: 是否有权限
        """
        from module_business.dao.tasks_dao import TasksDao
        from module_business.entity.vo.tasks_vo import TasksModel

        user_id = current_user.user.user_id
        user_role_keys = [role.role_key for role in current_user.user.role]

        # 超级管理员拥有所有权限
        if PermissionUtil.check_super_admin(user_role_keys):
            return True

        # 获取任务信息
        task = await TasksDao.get_tasks_detail_by_info(
            query_db, TasksModel(task_id=task_id)
        )
        if not task:
            raise PermissionException(message='任务不存在')

        # 检查是否为任务创建者
        if task.assigned_to == user_id:
            return True

        # 检查是否为项目管理员
        if PermissionUtil.check_project_manager(user_role_keys):
            has_manager_permission = await ProjectMembersDao.check_user_project_permission(
                query_db, user_id, task.project_id, RoleConstants.PROJECT_ROLE_MANAGER
            )
            if has_manager_permission:
                return True

        # 检查是否为项目所有者
        project = await ProjectsDao.get_projects_detail_by_info(
            query_db, ProjectsModel(project_id=task.project_id)
        )
        if project and project.owner_id == user_id:
            return True

        return False

    @staticmethod
    async def get_user_accessible_projects(
        query_db: AsyncSession,
        current_user: CurrentUserModel
    ) -> List[int]:
        """
        获取用户可访问的项目ID列表

        :param query_db: 数据库会话
        :param current_user: 当前用户
        :return: 项目ID列表
        """
        user_id = current_user.user.user_id
        user_role_keys = [role.role_key for role in current_user.user.role]

        # 超级管理员可以访问所有项目
        if PermissionUtil.check_super_admin(user_role_keys):
            return []  # 空列表表示所有项目

        # 项目管理员和项目成员只能访问自己参与的项目
        if (PermissionUtil.check_project_manager(user_role_keys) or 
            PermissionUtil.check_project_member(user_role_keys)):
            
            # 获取用户参与的项目ID列表
            user_projects = await ProjectMembersDao.get_user_projects(query_db, user_id)
            project_ids = [project.project_id for project in user_projects]
            
            # 获取用户拥有的项目ID列表
            owned_projects = await ProjectsDao.get_projects_by_owner(query_db, user_id)
            owned_project_ids = [project.project_id for project in owned_projects]
            
            # 合并项目ID列表
            all_project_ids = list(set(project_ids + owned_project_ids))
            return all_project_ids

        # 普通用户无法访问任何项目
        return [-1]  # 返回不存在的项目ID

    @staticmethod
    def check_menu_permission(user_role_keys: List[str], menu_path: str) -> bool:
        """
        检查菜单访问权限

        :param user_role_keys: 用户角色键值列表
        :param menu_path: 菜单路径
        :return: 是否有权限访问
        """
        # 所有角色都可以访问的菜单
        common_paths = ['/dashboard', '/tools']
        if menu_path in common_paths:
            return True

        # 项目成员及以上角色可以访问的菜单
        if menu_path in ['/projects']:
            return (PermissionUtil.check_project_member(user_role_keys) or
                    PermissionUtil.check_project_manager(user_role_keys) or
                    PermissionUtil.check_super_admin(user_role_keys))

        # 项目管理员及以上角色可以访问的菜单
        if menu_path in ['/database', '/knowledge-bases']:
            return (PermissionUtil.check_project_manager(user_role_keys) or
                    PermissionUtil.check_super_admin(user_role_keys))

        # 超级管理员可以访问的菜单
        if menu_path in ['/system']:
            return PermissionUtil.check_super_admin(user_role_keys)

        return False

    @staticmethod
    def check_interface_permission(user_role_keys: List[str], interface_code: str) -> bool:
        """
        检查接口访问权限

        :param user_role_keys: 用户角色键值列表
        :param interface_code: 接口权限代码
        :return: 是否有权限访问
        """
        # 超级管理员拥有所有接口权限
        if PermissionUtil.check_super_admin(user_role_keys):
            return True

        # 项目管理员权限
        if PermissionUtil.check_project_manager(user_role_keys):
            project_manager_permissions = [
                'business:projects:list',
                'business:projects:add',
                'business:projects:edit',
                'business:projects:remove',
                'business:projectMembers:list',
                'business:projectMembers:add',
                'business:projectMembers:edit',
                'business:projectMembers:remove',
                'business:tasks:list',
                'business:tasks:add',
                'business:tasks:edit',
                'business:tasks:remove',
                'business:database:view',
            ]
            if interface_code in project_manager_permissions:
                return True

        # 项目成员权限
        if PermissionUtil.check_project_member(user_role_keys):
            project_member_permissions = [
                'business:tasks:list',
                'business:tasks:add',
                'business:tasks:view',
            ]
            if interface_code in project_member_permissions:
                return True

        # 普通用户权限
        if PermissionUtil.check_common_user(user_role_keys):
            common_user_permissions = [
                'dashboard:view',
                'tools:view',
            ]
            if interface_code in common_user_permissions:
                return True

        return False

    @staticmethod
    async def ensure_project_permission(
        query_db: AsyncSession,
        current_user: CurrentUserModel,
        project_id: int,
        required_role: Optional[str] = None,
        allow_owner: bool = True
    ):
        """
        确保用户对项目有权限，如果没有权限则抛出异常

        :param query_db: 数据库会话
        :param current_user: 当前用户
        :param project_id: 项目ID
        :param required_role: 需要的角色类型
        :param allow_owner: 是否允许项目所有者访问
        :raises PermissionException: 权限不足时抛出异常
        """
        has_permission = await PermissionUtil.check_project_permission(
            query_db, current_user, project_id, required_role, allow_owner
        )
        
        if not has_permission:
            if required_role:
                role_name = RoleConstants.ROLE_NAMES.get(required_role, "相应")
                raise PermissionException(message=f'您没有该项目的{role_name}权限')
            else:
                raise PermissionException(message='您不是该项目的成员')

    @staticmethod
    async def ensure_task_edit_permission(
        query_db: AsyncSession,
        current_user: CurrentUserModel,
        task_id: int
    ):
        """
        确保用户对任务有编辑权限，如果没有权限则抛出异常

        :param query_db: 数据库会话
        :param current_user: 当前用户
        :param task_id: 任务ID
        :raises PermissionException: 权限不足时抛出异常
        """
        has_permission = await PermissionUtil.check_task_edit_permission(
            query_db, current_user, task_id
        )
        
        if not has_permission:
            raise PermissionException(message='您没有编辑该任务的权限')
