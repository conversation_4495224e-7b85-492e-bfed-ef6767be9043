<template>
  <div class="upload-file">
    <el-upload
      multiple
      :action="uploadFileUrl"
      :before-upload="handleBeforeUpload"
      :file-list="fileListProp"
      :limit="limit"
      :on-error="handleUploadError"
      :on-exceed="handleExceed"
      :on-success="handleUploadSuccess"
      :show-file-list="true"
      :headers="headers"
      :auto-upload="!cacheMode"
      class="upload-file-uploader"
      ref="fileUpload"
      v-if="!disabled"
      :on-change="handleChange"
      :on-remove="handleRemove"
    >
      <el-button type="primary">{{ cacheMode ? '选择文件' : '选取文件' }}</el-button>
    </el-upload>
    <!-- 上传提示 -->
    <div class="el-upload__tip" v-if="showTip && !disabled">
      请上传
      <template v-if="fileSize"> 大小不超过 <b style="color: #f56c6c">{{ fileSize }}MB</b> </template>
      <template v-if="fileType"> 格式为 <b style="color: #f56c6c">{{ fileType.join("/") }}</b> </template>
      的文件
      <template v-if="cacheMode">（文件将在创建任务时上传）</template>
    </div>
  </div>
</template>

<script setup>
import { getToken } from "@/utils/auth";
import { ref, computed, getCurrentInstance, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { uploadFile } from '@/api/files';

const props = defineProps({
  // 由父组件传入的文件列表
  fileList: {
    type: Array,
    default: () => []
  },
  // 数量限制
  limit: {
    type: Number,
    default: 5
  },
  // 大小限制(MB)
  fileSize: {
    type: Number,
    default: 5
  },
  // 文件类型, 例如['png', 'jpg', 'jpeg']
  fileType: {
    type: Array,
    default: () => ["doc", "docx", "xls", "xlsx", "ppt", "pptx", "txt", "pdf"]
  },
  // 是否显示提示
  isShowTip: {
    type: Boolean,
    default: true
  },
  // 禁用组件（仅查看文件）
  disabled: {
    type: Boolean,
    default: false
  },
  // 缓存模式（选择文件时缓存，不立即上传）
  cacheMode: {
    type: Boolean,
    default: false
  },
  // 即时上传
  immediateUpload: {
    type: Boolean,
    default: false
  },
  // 知识库ID（即时上传时需要）
  kbId: {
    type: [String, Number],
    default: null
  },
  // 项目ID（即时上传时需要）
  projectId: {
    type: [String, Number],
    default: null
  },
  // 源任务ID（即时上传时需要）
  sourceTaskId: {
    type: [String, Number],
    default: null
  }
});

const { proxy } = getCurrentInstance();
const emit = defineEmits(['file-select', 'file-remove', 'upload-success', 'upload-error']);
const number = ref(0);
const uploadList = ref([]);
const baseUrl = import.meta.env.VITE_APP_BASE_API;
const uploadFileUrl = ref(import.meta.env.VITE_APP_BASE_API + "/common/upload"); // 上传文件服务器地址
const headers = ref({ Authorization: "Bearer " + getToken() });
const showTip = computed(
  () => props.isShowTip && (props.fileType || props.fileSize)
);

// 组件内部 fileList 只做非缓存模式下的回显，缓存模式下完全由父组件控制
const fileListProp = computed(() => props.cacheMode ? props.fileList : uploadList.value);

// 上传前校检格式和大小
async function handleBeforeUpload(file) {
  // 校检文件类型
  if (props.fileType.length) {
    const fileName = file.name.split('.');
    const fileExt = fileName[fileName.length - 1];
    // 支持大小写文件格式验证
    const isTypeOk = props.fileType.some(type => 
      fileExt.toLowerCase() === type.toLowerCase()
    );
    if (!isTypeOk) {
      ElMessage.error(`文件格式不正确，请上传${props.fileType.join("/")}格式文件!`);
      return false;
    }
  }
  // 校检文件名是否包含特殊字符
  if (file.name.includes(',')) {
    ElMessage.error('文件名不正确，不能包含英文逗号!');
    return false;
  }
  // 校检文件大小
  if (props.fileSize) {
    const isLt = file.size / 1024 / 1024 < props.fileSize;
    if (!isLt) {
      ElMessage.error(`上传文件大小不能超过 ${props.fileSize} MB!`);
      return false;
    }
  }
  
  // 缓存模式下，直接返回false阻止上传
  if (props.cacheMode) {
    return false;
  }

  // 如果设置了即时上传，立即上传文件
  if (props.immediateUpload && props.kbId) {
    try {
      ElMessage.info(`正在上传文件: ${file.name}`)
      
      const uploadRes = await uploadFile(
        props.kbId, 
        props.fileType || 'document', 
        file.raw, 
        props.projectId, 
        props.sourceTaskId
      )
      
      if (uploadRes.code === 200) {
        // 更新文件路径
        file.url = uploadRes.data.file_path
        file.status = 'success'
        
        // 触发上传成功事件
        emit('upload-success', {
          file: file,
          filePath: uploadRes.data.file_path
        })
        
        ElMessage.success(`文件上传成功: ${file.name}`)
      } else {
        throw new Error(uploadRes.msg || '上传失败')
      }
    } catch (error) {
      console.error('文件上传失败:', error)
      file.status = 'fail'
      ElMessage.error(`文件上传失败: ${file.name}`)
      
      // 触发上传失败事件
      emit('upload-error', {
        file: file,
        error: error
      })
    }
  }
  return true;
}

// 文件个数超出
function handleExceed() {
  ElMessage.error(`上传文件数量不能超过 ${props.limit} 个!`);
}

// 上传失败
function handleUploadError(err) {
  ElMessage.error("上传文件失败");
}

// 上传成功回调
function handleUploadSuccess(res, file) {
  if (res.code === 200) {
    uploadList.value.push({ name: res.fileName, url: res.fileName });
    uploadedSuccessfully();
  } else {
    number.value--;
    proxy.$modal.closeLoading();
    ElMessage.error(res.msg);
    proxy.$refs.fileUpload.handleRemove(file);
    uploadedSuccessfully();
  }
}

// 选择文件变化
function handleChange(file, fileList) {
  if (props.cacheMode) {
    // 只允许单文件
    if (fileList.length > 1) fileList = [fileList[fileList.length - 1]];
    emit('file-select', fileList);
  }
}

// 删除文件
function handleRemove(file, fileList) {
  if (props.cacheMode) {
    emit('file-remove');
  }
}

// 上传结束处理
function uploadedSuccessfully() {
  if (number.value > 0 && uploadList.value.length === number.value) {
    uploadList.value = uploadList.value.filter(f => f.url !== undefined).concat(uploadList.value);
    number.value = 0;
    proxy.$modal.closeLoading();
  }
}
</script>

<style scoped lang="scss">
.upload-file-uploader {
  margin-bottom: 5px;
}
.upload-file-list .el-upload-list__item {
  border: 1px solid #e4e7ed;
  line-height: 2;
  margin-bottom: 10px;
  position: relative;
}
.upload-file-list .ele-upload-list__item-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: inherit;
}
.ele-upload-list__item-content-action .el-link {
  margin-right: 10px;
}
</style>
