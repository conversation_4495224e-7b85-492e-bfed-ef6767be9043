"""
权限验证工具测试
"""
import pytest
from unittest.mock import AsyncMock, patch
from sqlalchemy.ext.asyncio import AsyncSession

from utils.permission_util import PermissionUtil
from config.role_constants import RoleConstants
from module_admin.entity.vo.user_vo import CurrentUserModel, UserModel
from module_admin.entity.do.role_do import SysRole
from exceptions.exception import PermissionException


class TestPermissionUtil:
    """权限验证工具测试类"""

    @pytest.fixture
    def mock_db(self):
        """模拟数据库会话"""
        return AsyncMock(spec=AsyncSession)

    @pytest.fixture
    def super_admin_user(self):
        """超级管理员用户"""
        role = SysRole(role_id=1, role_key='super_admin', role_name='超级管理员')
        user = UserModel(user_id=1, user_name='admin', role=[role])
        return CurrentUserModel(user=user)

    @pytest.fixture
    def project_manager_user(self):
        """项目管理员用户"""
        role = SysRole(role_id=2, role_key='project_manager', role_name='项目管理员')
        user = UserModel(user_id=2, user_name='pm', role=[role])
        return CurrentUserModel(user=user)

    @pytest.fixture
    def project_member_user(self):
        """项目成员用户"""
        role = SysRole(role_id=3, role_key='project_member', role_name='项目成员')
        user = UserModel(user_id=3, user_name='member', role=[role])
        return CurrentUserModel(user=user)

    @pytest.fixture
    def common_user(self):
        """普通用户"""
        role = SysRole(role_id=4, role_key='common_user', role_name='普通用户')
        user = UserModel(user_id=4, user_name='user', role=[role])
        return CurrentUserModel(user=user)

    def test_check_super_admin(self):
        """测试超级管理员检查"""
        assert PermissionUtil.check_super_admin(['super_admin']) is True
        assert PermissionUtil.check_super_admin(['project_manager']) is False
        assert PermissionUtil.check_super_admin(['project_member']) is False
        assert PermissionUtil.check_super_admin(['common_user']) is False

    def test_check_project_manager(self):
        """测试项目管理员检查"""
        assert PermissionUtil.check_project_manager(['super_admin']) is False
        assert PermissionUtil.check_project_manager(['project_manager']) is True
        assert PermissionUtil.check_project_manager(['project_member']) is False
        assert PermissionUtil.check_project_manager(['common_user']) is False

    def test_check_project_member(self):
        """测试项目成员检查"""
        assert PermissionUtil.check_project_member(['super_admin']) is False
        assert PermissionUtil.check_project_member(['project_manager']) is False
        assert PermissionUtil.check_project_member(['project_member']) is True
        assert PermissionUtil.check_project_member(['common_user']) is False

    def test_check_common_user(self):
        """测试普通用户检查"""
        assert PermissionUtil.check_common_user(['super_admin']) is False
        assert PermissionUtil.check_common_user(['project_manager']) is False
        assert PermissionUtil.check_common_user(['project_member']) is False
        assert PermissionUtil.check_common_user(['common_user']) is True

    @pytest.mark.asyncio
    async def test_check_project_permission_super_admin(self, mock_db, super_admin_user):
        """测试超级管理员项目权限"""
        result = await PermissionUtil.check_project_permission(
            mock_db, super_admin_user, 1
        )
        assert result is True

    @pytest.mark.asyncio
    async def test_check_project_permission_project_not_exists(self, mock_db, project_manager_user):
        """测试项目不存在的情况"""
        with patch('module_business.dao.projects_dao.ProjectsDao.get_projects_detail_by_info') as mock_project:
            mock_project.return_value = None
            
            with pytest.raises(PermissionException) as exc_info:
                await PermissionUtil.check_project_permission(
                    mock_db, project_manager_user, 1
                )
            
            assert str(exc_info.value) == '项目不存在'

    @pytest.mark.asyncio
    async def test_check_project_permission_project_owner(self, mock_db, project_manager_user):
        """测试项目所有者权限"""
        with patch('module_business.dao.projects_dao.ProjectsDao.get_projects_detail_by_info') as mock_project:
            mock_project_obj = AsyncMock()
            mock_project_obj.owner_id = 2  # 与用户ID匹配
            mock_project.return_value = mock_project_obj
            
            result = await PermissionUtil.check_project_permission(
                mock_db, project_manager_user, 1, allow_owner=True
            )
            assert result is True

    @pytest.mark.asyncio
    async def test_check_project_permission_project_member(self, mock_db, project_member_user):
        """测试项目成员权限"""
        with patch('module_business.dao.projects_dao.ProjectsDao.get_projects_detail_by_info') as mock_project, \
             patch('module_business.dao.project_members_dao.ProjectMembersDao.check_user_project_permission') as mock_permission:
            
            mock_project_obj = AsyncMock()
            mock_project_obj.owner_id = 1  # 与用户ID不匹配
            mock_project.return_value = mock_project_obj
            mock_permission.return_value = True
            
            result = await PermissionUtil.check_project_permission(
                mock_db, project_member_user, 1
            )
            assert result is True

    @pytest.mark.asyncio
    async def test_check_project_permission_no_permission(self, mock_db, common_user):
        """测试无项目权限的情况"""
        with patch('module_business.dao.projects_dao.ProjectsDao.get_projects_detail_by_info') as mock_project, \
             patch('module_business.dao.project_members_dao.ProjectMembersDao.check_user_project_permission') as mock_permission:
            
            mock_project_obj = AsyncMock()
            mock_project_obj.owner_id = 1  # 与用户ID不匹配
            mock_project.return_value = mock_project_obj
            mock_permission.return_value = False
            
            result = await PermissionUtil.check_project_permission(
                mock_db, common_user, 1
            )
            assert result is False

    @pytest.mark.asyncio
    async def test_check_task_edit_permission_super_admin(self, mock_db, super_admin_user):
        """测试超级管理员任务编辑权限"""
        result = await PermissionUtil.check_task_edit_permission(
            mock_db, super_admin_user, 1
        )
        assert result is True

    @pytest.mark.asyncio
    async def test_check_task_edit_permission_task_creator(self, mock_db, project_member_user):
        """测试任务创建者编辑权限"""
        with patch('module_business.dao.tasks_dao.TasksDao.get_tasks_detail_by_info') as mock_task:
            mock_task_obj = AsyncMock()
            mock_task_obj.assigned_to = 3  # 与用户ID匹配
            mock_task.return_value = mock_task_obj
            
            result = await PermissionUtil.check_task_edit_permission(
                mock_db, project_member_user, 1
            )
            assert result is True

    @pytest.mark.asyncio
    async def test_check_task_edit_permission_project_manager(self, mock_db, project_manager_user):
        """测试项目管理员任务编辑权限"""
        with patch('module_business.dao.tasks_dao.TasksDao.get_tasks_detail_by_info') as mock_task, \
             patch('module_business.dao.project_members_dao.ProjectMembersDao.check_user_project_permission') as mock_permission:
            
            mock_task_obj = AsyncMock()
            mock_task_obj.assigned_to = 1  # 与用户ID不匹配
            mock_task_obj.project_id = 1
            mock_task.return_value = mock_task_obj
            mock_permission.return_value = True
            
            result = await PermissionUtil.check_task_edit_permission(
                mock_db, project_manager_user, 1
            )
            assert result is True

    def test_check_menu_permission(self):
        """测试菜单权限检查"""
        # 所有角色都可以访问的菜单
        assert PermissionUtil.check_menu_permission(['common_user'], '/dashboard') is True
        assert PermissionUtil.check_menu_permission(['project_member'], '/tools') is True
        
        # 项目成员及以上可以访问的菜单
        assert PermissionUtil.check_menu_permission(['common_user'], '/tasks') is False
        assert PermissionUtil.check_menu_permission(['project_member'], '/tasks') is True
        assert PermissionUtil.check_menu_permission(['project_manager'], '/tasks') is True
        assert PermissionUtil.check_menu_permission(['super_admin'], '/tasks') is True
        
        # 项目管理员及以上可以访问的菜单
        assert PermissionUtil.check_menu_permission(['project_member'], '/projects') is False
        assert PermissionUtil.check_menu_permission(['project_manager'], '/projects') is True
        assert PermissionUtil.check_menu_permission(['super_admin'], '/database') is True
        
        # 超级管理员可以访问的菜单
        assert PermissionUtil.check_menu_permission(['project_manager'], '/system') is False
        assert PermissionUtil.check_menu_permission(['super_admin'], '/system') is True

    def test_check_interface_permission(self):
        """测试接口权限检查"""
        # 超级管理员拥有所有权限
        assert PermissionUtil.check_interface_permission(['super_admin'], 'any:permission') is True
        
        # 项目管理员权限
        assert PermissionUtil.check_interface_permission(['project_manager'], 'business:projects:list') is True
        assert PermissionUtil.check_interface_permission(['project_manager'], 'business:tasks:edit') is True
        assert PermissionUtil.check_interface_permission(['project_manager'], 'system:user:add') is False
        
        # 项目成员权限
        assert PermissionUtil.check_interface_permission(['project_member'], 'business:tasks:list') is True
        assert PermissionUtil.check_interface_permission(['project_member'], 'business:projects:edit') is False
        
        # 普通用户权限
        assert PermissionUtil.check_interface_permission(['common_user'], 'dashboard:view') is True
        assert PermissionUtil.check_interface_permission(['common_user'], 'business:tasks:list') is False

    @pytest.mark.asyncio
    async def test_get_user_accessible_projects_super_admin(self, mock_db, super_admin_user):
        """测试超级管理员可访问项目"""
        result = await PermissionUtil.get_user_accessible_projects(mock_db, super_admin_user)
        assert result == []  # 空列表表示所有项目

    @pytest.mark.asyncio
    async def test_get_user_accessible_projects_project_member(self, mock_db, project_member_user):
        """测试项目成员可访问项目"""
        with patch('module_business.dao.project_members_dao.ProjectMembersDao.get_user_projects') as mock_user_projects, \
             patch('module_business.dao.projects_dao.ProjectsDao.get_projects_by_owner') as mock_owned_projects:
            
            mock_user_projects.return_value = [AsyncMock(project_id=1), AsyncMock(project_id=2)]
            mock_owned_projects.return_value = [AsyncMock(project_id=3)]
            
            result = await PermissionUtil.get_user_accessible_projects(mock_db, project_member_user)
            assert set(result) == {1, 2, 3}

    @pytest.mark.asyncio
    async def test_get_user_accessible_projects_common_user(self, mock_db, common_user):
        """测试普通用户可访问项目"""
        result = await PermissionUtil.get_user_accessible_projects(mock_db, common_user)
        assert result == [-1]  # 无法访问任何项目

    @pytest.mark.asyncio
    async def test_ensure_project_permission_success(self, mock_db, super_admin_user):
        """测试确保项目权限成功"""
        # 不应该抛出异常
        await PermissionUtil.ensure_project_permission(mock_db, super_admin_user, 1)

    @pytest.mark.asyncio
    async def test_ensure_project_permission_failure(self, mock_db, common_user):
        """测试确保项目权限失败"""
        with patch('module_business.dao.projects_dao.ProjectsDao.get_projects_detail_by_info') as mock_project, \
             patch('module_business.dao.project_members_dao.ProjectMembersDao.check_user_project_permission') as mock_permission:
            
            mock_project_obj = AsyncMock()
            mock_project_obj.owner_id = 1  # 与用户ID不匹配
            mock_project.return_value = mock_project_obj
            mock_permission.return_value = False
            
            with pytest.raises(PermissionException) as exc_info:
                await PermissionUtil.ensure_project_permission(
                    mock_db, common_user, 1, required_role='project_manager'
                )
            
            assert '您没有该项目的项目管理员权限' in str(exc_info.value)
