from sqlalchemy.ext.asyncio import AsyncSession
from typing import List
from config.constant import CommonConstant
from exceptions.exception import ServiceException
from module_admin.entity.vo.common_vo import CrudResponseModel
from module_business.dao.types_dao import TypesDao
from module_business.entity.vo.types_vo import DeleteTypesModel, TypesModel, TypesPageQueryModel
from utils.common_util import CamelCaseUtil
from utils.excel_util import ExcelUtil


class TypesService:
    """
    通用类型管理模块服务层
    """

    @classmethod
    async def get_types_list_services(
        cls, query_db: AsyncSession, query_object: TypesPageQueryModel, is_page: bool = False
    ):
        """
        获取通用类型管理列表信息service

        :param query_db: orm对象
        :param query_object: 查询参数对象
        :param is_page: 是否开启分页
        :return: 通用类型管理列表信息对象
        """
        types_list_result = await TypesDao.get_types_list(query_db, query_object, is_page)

        return types_list_result


    @classmethod
    async def add_types_services(cls, query_db: AsyncSession, page_object: TypesModel):
        """
        新增通用类型管理信息service

        :param query_db: orm对象
        :param page_object: 新增通用类型管理对象
        :return: 新增通用类型管理校验结果
        """
        try:
            await TypesDao.add_types_dao(query_db, page_object)
            await query_db.commit()
            return CrudResponseModel(is_success=True, message='新增成功')
        except Exception as e:
            await query_db.rollback()
            raise e

    @classmethod
    async def edit_types_services(cls, query_db: AsyncSession, page_object: TypesModel):
        """
        编辑通用类型管理信息service

        :param query_db: orm对象
        :param page_object: 编辑通用类型管理对象
        :return: 编辑通用类型管理校验结果
        """
        edit_types = page_object.model_dump(exclude_unset=True, exclude={})
        types_info = await cls.types_detail_services(query_db, page_object.type_id)
        if types_info.type_id:
            try:
                await TypesDao.edit_types_dao(query_db, edit_types)
                await query_db.commit()
                return CrudResponseModel(is_success=True, message='更新成功')
            except Exception as e:
                await query_db.rollback()
                raise e
        else:
            raise ServiceException(message='通用类型管理不存在')

    @classmethod
    async def delete_types_services(cls, query_db: AsyncSession, page_object: DeleteTypesModel):
        """
        删除通用类型管理信息service

        :param query_db: orm对象
        :param page_object: 删除通用类型管理对象
        :return: 删除通用类型管理校验结果
        """
        if page_object.type_ids:
            type_id_list = page_object.type_ids.split(',')
            try:
                for type_id in type_id_list:
                    await TypesDao.delete_types_dao(query_db, TypesModel(typeId=type_id))
                await query_db.commit()
                return CrudResponseModel(is_success=True, message='删除成功')
            except Exception as e:
                await query_db.rollback()
                raise e
        else:
            raise ServiceException(message='传入为空')

    @classmethod
    async def types_detail_services(cls, query_db: AsyncSession, type_id: int):
        """
        获取通用类型管理详细信息service

        :param query_db: orm对象
        :param type_id: 
        :return: 对应的信息
        """
        types = await TypesDao.get_types_detail_by_id(query_db, type_id=type_id)
        if types:
            result = TypesModel(**CamelCaseUtil.transform_result(types))
        else:
            result = TypesModel(**dict())

        return result

    @staticmethod
    async def export_types_list_services(types_list: List):
        """
        导出通用类型管理信息service

        :param types_list: 通用类型管理信息列表
        :return: 通用类型管理信息对应excel的二进制数据
        """
        # 创建一个映射字典，将英文键映射到中文键
        mapping_dict = {
            'typeId': '',
            'typeName': '类型名称(英文标识)',
            'displayName': '显示名称',
            'colorCode': 'UI显示颜色',
            'isActive': '是否激活',
            'isDeleted': '',
            'createdAt': '',
        }
        binary_data = ExcelUtil.export_list2excel(types_list, mapping_dict)

        return binary_data
