<template>
  <div class="login-container">
    <!-- 左侧产品介绍区域 -->
    <div class="product-intro">
      <div class="intro-content">
        <div class="logo-section">
          <div class="logo">
            <img src="../assets/logo/logo.png" alt="公司Logo" class="company-logo" />
            <div class="platform-info">
              <h1 class="platform-title">CV-AI产品设计平台</h1>
              <p class="platform-subtitle">专业的CV-AI智能建模与仿真解决方案，助力工程师提升设计效率与精度</p>
            </div>
          </div>
        </div>
        
        <div class="features-section">
          <div class="feature-list">
            <div class="feature-item" style="animation-delay: 0.1s;">
              <div class="feature-icon">
                <svg-icon icon-class="message" class="icon" />
              </div>
              <div class="feature-content">
                <h3>智能问答</h3>
                <p>AI助手为您解答设计难题</p>

              </div>
            </div>
            
            <div class="feature-item" style="animation-delay: 0.2s;">
              <div class="feature-icon">
                <svg-icon icon-class="tool" class="icon" />
              </div>
              <div class="feature-content">
                <h3>工具广场</h3>
                <p>丰富的建模与仿真工具一站式使用</p>
              </div>
            </div>
            
            <div class="feature-item" style="animation-delay: 0.3s;">
              <div class="feature-icon">
                <svg-icon icon-class="chart" class="icon" />
              </div>
              <div class="feature-content">
                <h3>项目管理</h3>
                <p>完整的项目生命周期管理</p>
              </div>
            </div>
            
            <div class="feature-item" style="animation-delay: 0.4s;">
              <div class="feature-icon">
                <svg-icon icon-class="database" class="icon" />
              </div>
              <div class="feature-content">
                <h3>数据库管理</h3>
                <p>高效管理设计资源和数据</p>
              </div>
            </div>
          </div>
        </div>
        
      </div>
      
      <!-- 装饰性背景元素 -->
      <div class="bg-decoration">
        <div class="circle circle-1"></div>
        <div class="circle circle-2"></div>
        <div class="circle circle-3"></div>
      </div>
    </div>

    <!-- 右侧登录表单区域 -->
    <div class="login-section">
      <div v-if="!isForgotPassword" class="login-form-container" key="login">
        <div class="login-header">
          <h2 class="welcome-title">欢迎回来</h2>
          <p class="welcome-subtitle">登录您的CV-AI账户，开启智能设计之旅</p>
        </div>
        
        <el-form ref="loginRef" :model="loginForm" :rules="loginRules" class="login-form">
          <el-form-item prop="username">
            <el-input
              v-model="loginForm.username"
              type="text"
              size="large"
              auto-complete="off"
              placeholder="请输入账号"
            >
              <template #prefix><svg-icon icon-class="user" class="el-input__icon input-icon" /></template>
            </el-input>
          </el-form-item>
          
          <el-form-item prop="password">
            <el-input
              v-model="loginForm.password"
              type="password"
              size="large"
              auto-complete="off"
              placeholder="请输入密码"
              @keyup.enter="handleLogin"
            >
              <template #prefix><svg-icon icon-class="password" class="el-input__icon input-icon" /></template>
            </el-input>
          </el-form-item>
          
          <el-form-item prop="code" v-if="captchaEnabled">
            <el-input
              v-model="loginForm.code"
              size="large"
              auto-complete="off"
              placeholder="验证码"
              style="width: 63%"
              @keyup.enter="handleLogin"
            >
              <template #prefix><svg-icon icon-class="validCode" class="el-input__icon input-icon" /></template>
            </el-input>
            <div class="login-code">
              <img :src="codeUrl" @click="getCode" class="login-code-img"/>
            </div>
          </el-form-item>
          
          <div class="form-options">
            <el-checkbox v-model="loginForm.rememberMe">记住密码</el-checkbox>
            <a href="#" class="forgot-password" @click.prevent="handleForgetPassword">忘记密码？</a>
          </div>
          
          <el-form-item style="width:100%;">
            <el-button
              :loading="loading"
              size="large"
              type="primary"
              style="width:100%;"
              @click.prevent="handleLogin"
            >
              <span v-if="!loading">登 录</span>
              <span v-else>登 录 中...</span>
            </el-button>
          </el-form-item>
          
          <div class="register-link" v-if="register">
            <span>还没有账户？</span>
            <router-link class="link-type" :to="'/register'">立即注册</router-link>
          </div>
        </el-form>
      </div>

      <div v-if="isForgotPassword" class="login-form-container" key="forgot-password">
        <div class="login-header">
          <h2 class="welcome-title">忘记密码</h2>
          <p class="welcome-subtitle">请输入手机号和验证码，重置您的密码</p>
        </div>
        
        <el-form ref="forgotPasswordRef" :model="forgotPasswordForm" :rules="forgotPasswordRules" autocomplete="off">
          <el-form-item prop="phone">
            <el-input
              v-model="forgotPasswordForm.phone"
              size="large"
              placeholder="请输入手机号"
              autocomplete="off"
              name="forgot-phone"
            >
              <template #prefix><svg-icon icon-class="phone" class="el-input__icon input-icon" /></template>
            </el-input>
          </el-form-item>
          
          <el-form-item prop="verificationCode">
            <el-input
              v-model="forgotPasswordForm.verificationCode"
              size="large"
              placeholder="请输入验证码"
              style="width: 63%"
              autocomplete="off"
              name="forgot-verification-code"
            >
              <template #prefix><svg-icon icon-class="validCode" class="el-input__icon input-icon" /></template>
            </el-input>
            <el-button
              :loading="codeLoading"
              size="large"
              type="primary"
              style="width: 33%; margin-left: 4%;"
              @click="handleGetVerificationCode"
            >
              <span v-if="!codeLoading">获取验证码</span>
              <span v-else>发送中...</span>
            </el-button>
          </el-form-item>
          
          <el-form-item prop="newPassword">
            <el-input
              v-model="forgotPasswordForm.newPassword"
              type="password"
              size="large"
              placeholder="请输入新密码"
              autocomplete="off"
              name="forgot-new-password"
            >
              <template #prefix><svg-icon icon-class="password" class="el-input__icon input-icon" /></template>
            </el-input>
          </el-form-item>
          
          <el-form-item prop="confirmPassword">
            <el-input
              v-model="forgotPasswordForm.confirmPassword"
              type="password"
              size="large"
              placeholder="请再次输入新密码"
              autocomplete="off"
              name="forgot-confirm-password"
            >
              <template #prefix><svg-icon icon-class="password" class="el-input__icon input-icon" /></template>
            </el-input>
          </el-form-item>
          
          <el-form-item style="width:100%;">
            <el-button
              :loading="forgotPasswordLoading"
              size="large"
              type="primary"
              style="width:100%;"
              @click="handleConfirmForgetPassword"
            >
              <span v-if="!forgotPasswordLoading">确认</span>
              <span v-else>处理中...</span>
            </el-button>
          </el-form-item>
          
          <div class="register-link">
            <a href="#" class="link-type" @click.prevent="isForgotPassword = false">返回登录</a>
          </div>
        </el-form>
      </div>
    </div>

    <!-- 底部版权信息 -->
    <div class="el-login-footer">
      <span>Copyright © 2025 SanHua R&D All Rights Reserved.</span>
    </div>
  </div>
</template>

<script setup>
import { getCodeImg, getVerificationCode, confirmForgetPassword } from "@/api/login";
import Cookies from "js-cookie";
import { encrypt, decrypt } from "@/utils/jsencrypt";
import useUserStore from '@/store/modules/user'

const userStore = useUserStore()
const route = useRoute();
const router = useRouter();
const { proxy } = getCurrentInstance();

const isForgotPassword = ref(false);
const codeLoading = ref(false);
const forgotPasswordLoading = ref(false);

const loginForm = ref({
  username: "",
  password: "",
  rememberMe: false,
  code: "",
  uuid: ""
});

const forgotPasswordForm = ref({
  phone: "",
  verificationCode: "",
  newPassword: "",
  confirmPassword: ""
});

const loginRules = {
  username: [{ required: true, trigger: "blur", message: "请输入您的账号" }],
  password: [{ required: true, trigger: "blur", message: "请输入您的密码" }],
  code: [{ required: true, trigger: "change", message: "请输入验证码" }]
};

const forgotPasswordRules = {
  phone: [
    { required: true, trigger: "blur", message: "请输入手机号" },
    { pattern: /^1[3-9]\d{9}$/, trigger: "blur", message: "请输入正确的手机号" }
  ],
  verificationCode: [
    { required: true, trigger: "blur", message: "请输入验证码" },
    { pattern: /^\d{6}$/, trigger: "blur", message: "验证码为6位数字" }
  ],
  newPassword: [
    { required: true, trigger: "blur", message: "请输入新密码" },
    { min: 6, trigger: "blur", message: "密码长度不能少于6位" }
  ],
  confirmPassword: [
    { required: true, trigger: "blur", message: "请再次输入新密码" },
    {
      validator: (rule, value, callback) => {
        if (value !== forgotPasswordForm.value.newPassword) {
          callback(new Error("两次输入的密码不一致"));
        } else {
          callback();
        }
      },
      trigger: "blur"
    }
  ]
};

const codeUrl = ref("");
const loading = ref(false);
// 验证码开关
const captchaEnabled = ref(true);
// 注册开关
const register = ref(false);
const redirect = ref(undefined);

watch(route, (newRoute) => {
    redirect.value = newRoute.query && newRoute.query.redirect;
}, { immediate: true });

function handleForgetPassword() {
  isForgotPassword.value = true;
  forgotPasswordForm.value = {
    phone: "",
    verificationCode: "",
    newPassword: "",
    confirmPassword: ""
  };
}

function handleGetVerificationCode() {
  proxy.$refs.forgotPasswordRef.validateField('phone', (valid) => {
    if (!valid) {
      codeLoading.value = true;
      getVerificationCode(forgotPasswordForm.value.phone).then(res => {
        proxy.$modal.msgSuccess("验证码已发送到您的手机");
        codeLoading.value = false;
      }).catch(() => {
        codeLoading.value = false;
      });
    }
  });
}

function handleConfirmForgetPassword() {
  proxy.$refs.forgotPasswordRef.validate(valid => {
    if (valid) {
      forgotPasswordLoading.value = true;
      const data = {
        phone: forgotPasswordForm.value.phone,
        verificationCode: forgotPasswordForm.value.verificationCode,
        newPassword: forgotPasswordForm.value.newPassword
      };
      confirmForgetPassword(data).then(res => {
        proxy.$modal.msgSuccess("密码重置成功，请使用新密码登录");
        isForgotPassword.value = false;
        forgotPasswordForm.value = {
          phone: "",
          verificationCode: "",
          newPassword: "",
          confirmPassword: ""
        };
        forgotPasswordLoading.value = false;
      }).catch(() => {
        forgotPasswordLoading.value = false;
      });
    }
  });
}

function handleLogin() {
  proxy.$refs.loginRef.validate(valid => {
    if (valid) {
      loading.value = true;
      // 勾选了需要记住密码设置在 cookie 中设置记住用户名和密码
      if (loginForm.value.rememberMe) {
        Cookies.set("username", loginForm.value.username, { expires: 30 });
        Cookies.set("password", encrypt(loginForm.value.password), { expires: 30 });
        Cookies.set("rememberMe", loginForm.value.rememberMe, { expires: 30 });
      } else {
        // 否则移除
        Cookies.remove("username");
        Cookies.remove("password");
        Cookies.remove("rememberMe");
      }
      // 调用action的登录方法
      userStore.login(loginForm.value).then(() => {
        const query = route.query;
        const otherQueryParams = Object.keys(query).reduce((acc, cur) => {
          if (cur !== "redirect") {
            acc[cur] = query[cur];
          }
          return acc;
        }, {});
        router.push({ path: redirect.value || "/", query: otherQueryParams });
      }).catch(() => {
        loading.value = false;
        // 重新获取验证码
        if (captchaEnabled.value) {
          getCode();
        }
      });
    }
  });
}

function getCode() {
  getCodeImg().then(res => {
    captchaEnabled.value = res.captchaEnabled === undefined ? true : res.captchaEnabled;
    register.value = res.registerEnabled === undefined ? false : res.registerEnabled;
    if (captchaEnabled.value) {
      codeUrl.value = "data:image/gif;base64," + res.img;
      loginForm.value.uuid = res.uuid;
    }
  });
}

function getCookie() {
  const username = Cookies.get("username");
  const password = Cookies.get("password");
  const rememberMe = Cookies.get("rememberMe");
  loginForm.value = {
    username: username === undefined ? loginForm.value.username : username,
    password: password === undefined ? loginForm.value.password : decrypt(password),
    rememberMe: rememberMe === undefined ? false : Boolean(rememberMe)
  };
}

getCode();
getCookie();
</script>

<style lang='scss' scoped>
.login-container {
  display: flex;
  min-height: 100vh;
  background: linear-gradient(90deg, #1e3c72 0%, #1e3c72 50%, #ffffff 50%, #ffffff 100%);
  position: relative;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
      radial-gradient(circle at 20% 80%, rgba(42, 82, 152, 0.2) 0%, transparent 50%),
      radial-gradient(circle at 80% 20%, rgba(30, 60, 114, 0.2) 0%, transparent 50%);
    z-index: 1;
  }
}

.product-intro {
  flex: 1 1 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 60px 80px;
  position: relative;
  color: white;
  z-index: 2;
  min-width: 0;
  box-sizing: border-box;
  
  .intro-content {
    position: relative;
    z-index: 3;
    
    .logo-section {
      margin-bottom: 30px;
      text-align: center;
      
      .logo {
        display: flex;
        align-items: center;
        margin-bottom: 20px;
        justify-content: center;
        flex-direction: column;
        
        .company-logo {
          width: 60px;
          height: 60px;
          margin-right: 0;
          margin-bottom: 20px;
          padding: 6px;
          object-fit: contain;
          filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.2));
          background: rgba(255, 255, 255, 0.9);
          border-radius: 16px;
        }
        
        .platform-info {
          .platform-title {
            font-size: 48px;
            font-weight: 700;
            color: #ffffff;
            margin: 0 0 12px 0;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
            line-height: 1.2;
          }
          
          .platform-subtitle {
            font-size: 20px;
            color: rgba(255, 255, 255, 0.9);
            margin: 0;
            font-weight: 300;
          }
        }
      }
    }
    
    .features-section {
      margin-bottom: 60px;
      
      
      .feature-list {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 30px;
        
        .feature-item {
          display: flex;
          align-items: flex-start;
          padding: 20px;
          background: rgba(255, 255, 255, 0.1);
          border-radius: 16px;
          backdrop-filter: blur(10px);
          border: 1px solid rgba(255, 255, 255, 0.2);
          transition: all 0.3s ease;
          animation: fadeInUp 0.6s ease-out forwards;
          opacity: 0;
          transform: translateY(30px);
          
          &:hover {
            transform: translateY(-5px);
            background: rgba(255, 255, 255, 0.15);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
          }
          
          .feature-icon {
            width: 50px;
            height: 50px;
            border-radius: 12px;
            background: rgba(255, 255, 255, 0.2);
            margin-right: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
            
            .icon {
              width: 24px;
              height: 24px;
              color: #ffffff;
            }
          }
          
          .feature-content {
            h3 {
              font-size: 18px;
              font-weight: 600;
              color: #ffffff;
              margin: 0 0 8px 0;
            }
            
            p {
              font-size: 14px;
              color: rgba(255, 255, 255, 0.8);
              margin: 0;
              line-height: 1.5;
            }
          }
        }
      }
    }
    

  }
  
  .bg-decoration {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    z-index: 1;
    
    .circle {
      position: absolute;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.1);
      animation: float 6s ease-in-out infinite;
      
      &.circle-1 {
        width: 300px;
        height: 300px;
        top: -150px;
        left: -150px;
        animation-delay: 0s;
      }
      
      &.circle-2 {
        width: 200px;
        height: 200px;
        bottom: -100px;
        right: -100px;
        animation-delay: 2s;
      }
      
      &.circle-3 {
        width: 150px;
        height: 150px;
        top: 50%;
        left: 10%;
        animation-delay: 4s;
      }
    }
    
  }
}

.login-section {
  flex: 1 1 0;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40px;
  position: relative;
  z-index: 2;
  min-width: 0;
  box-sizing: border-box;
}

.login-form-container {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  width: 100%;
  max-width: 420px;
  padding: 50px 40px;
  border-radius: 24px;
  box-shadow: 0 25px 80px rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.3);
  animation: slideInRight 0.8s ease-out;
  
  .login-header {
    text-align: center;
    margin-bottom: 40px;
    
    .welcome-title {
      font-size: 28px;
      font-weight: 700;
      color: #2c3e50;
      margin-bottom: 12px;
    }
    
    .welcome-subtitle {
      font-size: 16px;
      color: #7f8c8d;
      margin: 0;
      line-height: 1.5;
    }
  }
  
  .el-input {
    height: 56px;
    
    :deep(.el-input__wrapper) {
      border-radius: 16px;
      background: rgba(255, 255, 255, 0.9);
      border: 2px solid rgba(30, 60, 114, 0.1);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
      transition: all 0.3s ease;
      
      &:hover {
        border-color: rgba(30, 60, 114, 0.3);
        box-shadow: 0 6px 20px rgba(30, 60, 114, 0.1);
      }
      
      &.is-focus {
        border-color: #1e3c72;
        box-shadow: 0 6px 20px rgba(30, 60, 114, 0.2);
      }
    }
    
    input {
      height: 56px;
      font-size: 16px;
      color: #2c3e50;
      
      &::placeholder {
        color: #bdc3c7;
      }
    }
  }
  
  .input-icon {
    height: 56px;
    width: 18px;
    margin-left: 0px;
    color: #1e3c72;
  }
  
  .el-form-item {
    margin-bottom: 24px;
  }
  
  .form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 32px;
    
    .el-checkbox {
      :deep(.el-checkbox__label) {
        color: #606266;
        font-size: 14px;
      }
    }
    
    .forgot-password {
      color: #1e3c72;
      text-decoration: none;
      font-weight: 500;
      font-size: 14px;
      transition: all 0.3s ease;
      
      &:hover {
        color: #2a5298;
        text-decoration: underline;
      }
    }
  }
  
  .el-button {
    height: 56px;
    border-radius: 16px;
    font-size: 16px;
    font-weight: 600;
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
    border: none;
    box-shadow: 0 8px 25px rgba(30, 60, 114, 0.3);
    transition: all 0.3s ease;
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 12px 35px rgba(30, 60, 114, 0.4);
    }
    
    &:active {
      transform: translateY(0);
    }
  }
  
  .register-link {
    text-align: center;
    margin-top: 24px;
    font-size: 14px;
    color: #7f8c8d;
    
    .link-type {
      color: #1e3c72;
      text-decoration: none;
      font-weight: 600;
      margin-left: 8px;
      transition: all 0.3s ease;
      
      &:hover {
        color: #2a5298;
        text-decoration: underline;
      }
    }
  }
}

.login-code {
  width: 33%;
  height: 56px;
  float: right;
  
  img {
    cursor: pointer;
    vertical-align: middle;
    border-radius: 12px;
    transition: all 0.3s ease;
    
    &:hover {
      transform: scale(1.05);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    }
  }
}

.login-code-img {
  height: 56px;
  padding-left: 12px;
  border-radius: 12px;
}

.el-login-footer {
  height: 40px;
  line-height: 40px;
  position: fixed;
  bottom: 0;
  width: 100%;
  text-align: center;
  color: rgba(255, 255, 255, 0.9);
  font-family: Arial;
  font-size: 12px;
  letter-spacing: 1px;
  z-index: 3;
  backdrop-filter: blur(10px);
  background: rgba(0, 0, 0, 0.1);
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(100px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

@keyframes wave {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(40px);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .login-container {
    flex-direction: column;
    min-height: 100vh;
  }
  .product-intro, .login-section {
    width: 100%;
    min-width: 0;
    padding: 40px 30px;
  }
  .login-form-container {
    max-width: 500px;
    width: 100%;
    padding: 40px 30px;
  }
}

@media (max-width: 1024px) {
  .login-container {
    flex-direction: column;
    min-height: 100vh;
  }
  .product-intro, .login-section {
    width: 100%;
    min-width: 0;
    padding: 30px 20px;
  }
  .login-form-container {
    max-width: 400px;
    width: 100%;
    padding: 30px 20px;
  }
}

@media (max-width: 768px) {
  .product-intro, .login-section {
    padding: 20px 10px;
  }
  .login-form-container {
    max-width: 100%;
    padding: 20px 8px;
  }
  .platform-title {
    font-size: 22px !important;
  }
}

@media (max-width: 480px) {
  .product-intro, .login-section {
    padding: 10px 2px;
  }
  .login-form-container {
    max-width: 100%;
    padding: 10px 2px;
  }
  .platform-title {
    font-size: 16px !important;
  }
}
</style>

