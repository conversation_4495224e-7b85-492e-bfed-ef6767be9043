from fastapi import APIRouter, Depends, Form, Request
from pydantic_validation_decorator import Validate<PERSON>ields
from sqlalchemy.ext.asyncio import AsyncSession
from config.enums import BusinessType
from config.get_db import get_db
from module_admin.annotation.log_annotation import Log
from module_admin.aspect.interface_auth import CheckUserInterfaceAuth
from module_admin.entity.vo.user_vo import CurrentUserModel
from module_admin.service.login_service import LoginService
from module_business.service.types_service import TypesService
from module_business.entity.vo.types_vo import DeleteTypesModel, TypesModel, TypesPageQueryModel
from utils.common_util import bytes2file_response
from utils.log_util import logger
from utils.page_util import PageResponseModel
from utils.response_util import ResponseUtil
from datetime import datetime


typesController = APIRouter(prefix='/business/types', dependencies=[Depends(LoginService.get_current_user)])


@typesController.get(
    '/list', response_model=PageResponseModel  # dependencies=[Depends(CheckUserInterfaceAuth('business:types:list'))]
)
async def get_business_types_list(
    request: Request,
types_page_query: TypesPageQueryModel = Depends(),
    query_db: AsyncSession = Depends(get_db),
):
    # 获取分页数据
    types_page_query_result = await TypesService.get_types_list_services(query_db, types_page_query, is_page=True)
    logger.info('获取成功')

    return ResponseUtil.success(model_content=types_page_query_result)


@typesController.post('')  # dependencies=[Depends(CheckUserInterfaceAuth('business:types:add'))]
@ValidateFields(validate_model='add_types')
@Log(title='通用类型管理', business_type=BusinessType.INSERT)
async def add_business_types(
    request: Request,
    add_types: TypesModel,
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    add_types.created_at = datetime.now()
    add_types_result = await TypesService.add_types_services(query_db, add_types)
    logger.info(add_types_result.message)

    return ResponseUtil.success(msg=add_types_result.message)


@typesController.get(
    '/{type_id}', response_model=TypesModel  # dependencies=[Depends(CheckUserInterfaceAuth('business:types:query'))]
)
async def query_detail_business_types(request: Request, type_id: int, query_db: AsyncSession = Depends(get_db)):
    types_detail_result = await TypesService.types_detail_services(query_db, type_id)
    logger.info(f'获取type_id为{type_id}的信息成功')

    return ResponseUtil.success(data=types_detail_result)


@typesController.post('/export')  # dependencies=[Depends(CheckUserInterfaceAuth('business:types:export'))]
@Log(title='通用类型管理', business_type=BusinessType.EXPORT)
async def export_business_types_list(
    request: Request,
    types_page_query: TypesPageQueryModel = Form(),
    query_db: AsyncSession = Depends(get_db),
):
    # 获取全量数据
    types_query_result = await TypesService.get_types_list_services(query_db, types_page_query, is_page=False)
    types_export_result = await TypesService.export_types_list_services(types_query_result)
    logger.info('导出成功')

    return ResponseUtil.streaming(data=bytes2file_response(types_export_result))
