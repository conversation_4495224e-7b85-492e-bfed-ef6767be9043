from datetime import datetime
from pydantic import BaseModel, ConfigDict, Field
from pydantic.alias_generators import to_camel
from pydantic_validation_decorator import NotBlank
from typing import Optional
from module_admin.annotation.pydantic_annotation import as_query




class KnowledgeBasesModel(BaseModel):
    """
    数据库管理表对应pydantic模型
    """
    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True)

    kb_id: Optional[int] = Field(default=None, description='数据库id')
    project_id: Optional[int] = Field(default=None, description='NULL表示通用数据库')
    kb_name: Optional[str] = Field(default=None, description='数据库名称')
    description: Optional[str] = Field(default=None, description='数据库描述')
    owner_id: Optional[int] = Field(default=None, description='数据库所有者')
    vector_store_id: Optional[str] = Field(default=None, description='向量库标识')
    is_public: Optional[int] = Field(default=0, description='是否公开')
    created_at: Optional[datetime] = Field(default=None, description='创建时间')
    updated_at: Optional[datetime] = Field(default=None, description='更新时间')
    is_deleted: Optional[int] = Field(default=0, description='是否删除')
    file_type_stats: Optional[dict] = Field(default=None, description='JSON示例: model=5, report=3, log=12')
    type_ids: Optional[list] = Field(default=None, description='类型ID列表')
    type_names: Optional[list] = Field(default=None, description='类型名称列表')

    @NotBlank(field_name='kb_name', message='数据库名称不能为空')
    def get_kb_name(self):
        return self.kb_name




    def validate_fields(self):
        # 如果是逻辑删除操作，不验证kb_name字段
        if self.is_deleted != 1:
            self.get_kb_name()
        # self.get_owner_id()




class KnowledgeBasesQueryModel(KnowledgeBasesModel):
    """
    数据库管理不分页查询模型
    """
    pass


@as_query
class KnowledgeBasesPageQueryModel(BaseModel):
    """
    数据库管理分页查询模型
    """
    model_config = ConfigDict(alias_generator=to_camel)

    page_num: int = Field(default=1, description='当前页码')
    page_size: int = Field(default=10, description='每页记录数')
    kb_name: Optional[str] = Field(default=None, description='数据库名称')
    description: Optional[str] = Field(default=None, description='数据库描述')
    owner_id: Optional[int] = Field(default=None, description='数据库所有者')
    project_id: Optional[int] = Field(default=None, description='项目ID')
    is_deleted: Optional[int] = Field(default=0, description='是否删除')


class DeleteKnowledgeBasesModel(BaseModel):
    """
    删除数据库管理模型
    """

    model_config = ConfigDict(alias_generator=to_camel)

    kb_ids: str = Field(description='需要删除的数据库id')
