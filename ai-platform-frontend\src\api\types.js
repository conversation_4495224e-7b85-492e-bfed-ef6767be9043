import request from '@/utils/request'

// 查询类型列表
export function getTypesList(query) {
  return request({
    url: '/business/types/list',
    method: 'get',
    params: query
  })
}

// 查询类型详细
export function getType(typeId) {
  return request({
    url: '/business/types/' + typeId,
    method: 'get'
  })
}

// 新增类型
export function addType(data) {
  return request({
    url: '/business/types',
    method: 'post',
    data: data
  })
}

// 修改类型
export function updateType(data) {
  return request({
    url: '/business/types',
    method: 'put',
    data: data
  })
}

// 删除类型
export function delType(typeIds) {
  return request({
    url: '/business/types/' + typeIds,
    method: 'delete'
  })
}

// 导出类型
export function exportType(query) {
  return request({
    url: '/business/types/export',
    method: 'post',
    params: query
  })
} 