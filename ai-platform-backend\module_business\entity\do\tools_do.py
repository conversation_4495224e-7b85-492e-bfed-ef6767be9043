from sqlalchemy import Column, JSO<PERSON>, String, Integer, TIMESTAMP, Text, SmallInteger
from config.database import Base


class RdTools(Base):
    """
    工具管理表
    """

    __tablename__ = 'rd_tools'

    tool_id = Column(Integer, primary_key=True, autoincrement=True, nullable=False, comment='工具id')
    tool_name = Column(String(100), nullable=False, comment='工具名称')
    type_id = Column(Integer, nullable=False, comment='关联rd_sys_types中的类型')
    description = Column(Text, nullable=True, comment='工具描述')
    version = Column(String(50), nullable=True, comment='版本号')
    vendor = Column(String(100), nullable=True, comment='供应商')
    executable_api = Column(String(255), nullable=True, comment='接口调用路径')
    health_url = Column(String(255), nullable=True, comment='健康检查URL')
    config_template = Column(JSON, nullable=True, comment='配置模板(JSON格式)')
    is_active = Column(SmallInteger, nullable=True, comment='是否激活')
    is_deleted = Column(SmallInteger, nullable=True, comment='是否删除')
    queue_required = Column(SmallInteger, nullable=True, comment='是否需要队列')
    is_health = Column(SmallInteger, nullable=True, comment='服务是否健康')
    remark = Column(Text, nullable=True, comment='备注/错误信息')
    created_at = Column(TIMESTAMP, nullable=True, comment='创建时间')
    updated_at = Column(TIMESTAMP, nullable=True, comment='更新时间')



