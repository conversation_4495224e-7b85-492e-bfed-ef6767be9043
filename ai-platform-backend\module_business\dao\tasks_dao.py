from datetime import datetime
from sqlalchemy import delete, select, update, and_
from sqlalchemy.ext.asyncio import AsyncSession
from module_business.entity.do.tasks_do import RdTasks
from module_business.entity.vo.tasks_vo import TasksModel, TasksPageQueryModel
from utils.page_util import PageUtil


class TasksDao:
    """
    任务信息管理模块数据库操作层
    """

    @classmethod
    async def get_tasks_detail_by_id(cls, db: AsyncSession, task_id: int):
        """
        根据获取任务信息管理详细信息

        :param db: orm对象
        :param task_id: 
        :return: 任务信息管理信息对象
        """
        tasks_info = (
            (
                await db.execute(
                    select(RdTasks)
                    .where(
                        RdTasks.task_id == task_id
                    )
                )
            )
            .scalars()
            .first()
        )

        return tasks_info

    @classmethod
    async def get_tasks_detail_by_info(cls, db: AsyncSession, tasks: TasksModel):
        """
        根据任务信息管理参数获取任务信息管理信息

        :param db: orm对象
        :param tasks: 任务信息管理参数对象
        :return: 任务信息管理信息对象
        """
        tasks_info = (
            (
                await db.execute(
                    select(RdTasks).where(
                    )
                )
            )
            .scalars()
            .first()
        )

        return tasks_info

    @classmethod
    async def get_tasks_list(cls, db: AsyncSession, query_object: TasksPageQueryModel, data_scope_sql: str = None, is_page: bool = False):
        """
        根据查询参数获取任务信息管理列表信息

        :param db: orm对象
        :param query_object: 查询参数对象
        :param data_scope_sql: 数据权限过滤条件
        :param is_page: 是否开启分页
        :return: 任务信息管理列表信息对象
        """
        query = (
            select(RdTasks)
            .where(
                RdTasks.project_id == query_object.project_id if query_object.project_id else True,
                RdTasks.task_name.like(f'%{query_object.task_name}%') if query_object.task_name else True,
                RdTasks.type_id == query_object.type_id if query_object.type_id else True,
                RdTasks.description == query_object.description if query_object.description else True,
                RdTasks.assigned_to == query_object.assigned_to if query_object.assigned_to else True,
                RdTasks.status == query_object.status if query_object.status else True,
                RdTasks.progress == query_object.progress if query_object.progress else True,
                RdTasks.tool_id == query_object.tool_id if query_object.tool_id else True,
                RdTasks.parameters == query_object.parameters if query_object.parameters else True,
                RdTasks.created_at == query_object.created_at if query_object.created_at else True,
                RdTasks.updated_at == query_object.updated_at if query_object.updated_at else True,
                RdTasks.started_at == query_object.started_at if query_object.started_at else True,
                RdTasks.completed_at == query_object.completed_at if query_object.completed_at else True,
                RdTasks.is_deleted == False,
                RdTasks.average_execution_time == query_object.average_execution_time if query_object.average_execution_time else True,
            )
            .order_by(RdTasks.task_id)
            .distinct()
        )

        # 添加数据权限过滤
        if data_scope_sql and data_scope_sql != "all":
            if data_scope_sql == "none":
                # 普通用户无法查看任何任务
                query = query.where(RdTasks.task_id == -1)  # 永远不会匹配的条件
            elif data_scope_sql.startswith("project_ids:"):
                # 用户参与的项目的任务
                project_ids_str = data_scope_sql.split(":", 1)[1]
                if project_ids_str:
                    project_ids = [int(pid) for pid in project_ids_str.split(",")]
                    query = query.where(RdTasks.project_id.in_(project_ids))
                else:
                    query = query.where(RdTasks.task_id == -1)
            elif data_scope_sql.startswith("owner_only:"):
                # 只能查看自己拥有项目的任务
                owner_id = int(data_scope_sql.split(":", 1)[1])
                # 这里需要关联项目表来过滤
                from module_business.entity.do.projects_do import RdProjects
                query = query.join(RdProjects, RdTasks.project_id == RdProjects.project_id).where(RdProjects.owner_id == owner_id)

        tasks_list = await PageUtil.paginate(db, query, query_object.page_num, query_object.page_size, is_page)

        return tasks_list

    @classmethod
    async def add_tasks_dao(cls, db: AsyncSession, tasks: TasksModel):
        """
        新增任务信息管理数据库操作

        :param db: orm对象
        :param tasks: 任务信息管理对象
        :return:
        """
        # 只获取数据库模型中存在的字段，排除VO模型中的额外字段
        task_dict = tasks.model_dump(
            exclude_unset=False,
            by_alias=False,
            exclude={'tool_queue_required', 'creator_name', 'tool_name'}  # 排除不属于数据库模型的字段
        )
        
        db_tasks = RdTasks(**task_dict)
        db.add(db_tasks)
        await db.flush()

        return db_tasks

    @classmethod
    async def edit_tasks_dao(cls, db: AsyncSession, tasks: dict):
        """
        编辑任务信息管理数据库操作

        :param db: orm对象
        :param tasks: 需要更新的任务信息管理字典
        :return:
        """
        await db.execute(update(RdTasks), [tasks])

    @classmethod
    async def delete_tasks_dao(cls, db: AsyncSession, tasks: TasksModel):
        """
        删除任务信息管理数据库操作

        :param db: orm对象
        :param tasks: 任务信息管理对象
        :return:
        """
        await db.execute(delete(RdTasks).where(RdTasks.task_id.in_([tasks.task_id])))

    @classmethod
    async def get_tasks_list_by_project_id(cls, db: AsyncSession, project_id: int):
        """
        根据项目ID获取所有任务信息

        :param db: orm对象
        :param project_id: 项目ID
        :return: 项目关联的所有任务信息列表
        """
        query = (
            select(RdTasks)
            .where(
                RdTasks.project_id == project_id,
                RdTasks.is_deleted == False
            )
            .order_by(RdTasks.created_at.desc())
        )
        result = (await db.execute(query)).scalars().all()
        return result

    @classmethod
    async def update_task_progress(cls, db: AsyncSession, task_id: int, progress: int):
        """
        更新任务进度

        :param db: orm对象
        :param task_id: 任务ID
        :param progress: 进度值 (0-100)
        :return:
        """
        await db.execute(
            update(RdTasks)
            .where(RdTasks.task_id == task_id)
            .values(
                progress=progress,
                updated_at=datetime.now()
            )
        )

    @classmethod
    async def get_completed_tasks_by_tool(cls, db: AsyncSession, type_id: int, tool_id: int):
        """
        根据类型ID和工具ID获取已完成的任务列表

        :param db: orm对象
        :param type_id: 任务类型ID
        :param tool_id: 工具ID
        :return: 已完成的任务列表
        """
        result = await db.execute(
            select(RdTasks)
            .where(
                and_(
                    RdTasks.type_id == type_id,
                    RdTasks.tool_id == tool_id,
                    RdTasks.status == 'completed'
                )
            )
            .order_by(RdTasks.created_at.desc())
        )

        return result.scalars().all()

    @classmethod
    async def get_completed_tasks_by_criteria(cls, db: AsyncSession, project_id: int, type_id: int, tool_id: int, parameters_md5: str):
        """
        根据项目ID、类型ID、工具ID和参数MD5获取已完成的任务列表

        :param db: orm对象
        :param project_id: 项目ID
        :param type_id: 任务类型ID
        :param tool_id: 工具ID
        :param parameters_md5: 参数MD5值
        :return: 已完成的任务列表
        """
        result = await db.execute(
            select(RdTasks)
            .where(
                and_(
                    RdTasks.project_id == project_id,
                    RdTasks.type_id == type_id,
                    RdTasks.tool_id == tool_id,
                    RdTasks.parameters_md5 == parameters_md5,
                    RdTasks.status == 'completed',
                    RdTasks.is_deleted == 0
                )
            )
            .order_by(RdTasks.created_at.desc())
        )

        return result.scalars().all()

