"""
队列维护服务
提供队列健康检查、清理和恢复功能
"""

import asyncio
import time
from datetime import datetime, timedelta
from typing import Dict, Any, List
from redis import asyncio as aioredis
from sqlalchemy.ext.asyncio import AsyncSession

from module_scheduler.service.task_queue_service import TaskQueueService
from module_business.dao.tasks_dao import TasksDao
from utils.log_util import logger


class QueueMaintenanceService:
    """
    队列维护服务
    提供定期维护、健康检查和故障恢复功能
    """
    
    def __init__(self, redis_client: aioredis.Redis, db_session: AsyncSession):
        self.redis = redis_client
        self.db_session = db_session
        self.queue_service = TaskQueueService(redis_client)
        self.is_running = False
        self.maintenance_task = None
    
    async def start_maintenance(self, interval_seconds: int = 300):
        """
        启动定期维护任务
        
        Args:
            interval_seconds: 维护间隔（秒），默认5分钟
        """
        if self.is_running:
            logger.warning("队列维护服务已在运行")
            return
        
        self.is_running = True
        self.maintenance_task = asyncio.create_task(self._maintenance_loop(interval_seconds))
        logger.info(f"🔧 队列维护服务已启动，间隔: {interval_seconds}秒")
    
    async def stop_maintenance(self):
        """停止定期维护任务"""
        if not self.is_running:
            return
        
        self.is_running = False
        if self.maintenance_task:
            self.maintenance_task.cancel()
            try:
                await self.maintenance_task
            except asyncio.CancelledError:
                pass
        
        logger.info("🔧 队列维护服务已停止")
    
    async def _maintenance_loop(self, interval_seconds: int):
        """维护循环"""
        while self.is_running:
            try:
                await self.perform_maintenance()
                await asyncio.sleep(interval_seconds)
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"❌ 队列维护循环出错: {e}")
                await asyncio.sleep(60)  # 出错后等待1分钟
    
    async def perform_maintenance(self) -> Dict[str, Any]:
        """
        执行完整的队列维护
        
        Returns:
            维护结果统计
        """
        logger.info("🔧 开始队列维护")
        start_time = time.time()
        
        results = {
            "start_time": datetime.now().isoformat(),
            "recovered_orphans": 0,
            "cleaned_locks": 0,
            "cleaned_expired_tasks": 0,
            "fixed_inconsistencies": 0,
            "errors": []
        }
        
        try:
            # 1. 恢复孤儿任务
            results["recovered_orphans"] = await self.queue_service.recover_orphaned_tasks()
            
            # 2. 清理过期锁
            results["cleaned_locks"] = await self.queue_service.cleanup_expired_locks()
            
            # 3. 清理过期任务
            results["cleaned_expired_tasks"] = await self._cleanup_expired_tasks()
            
            # 4. 修复状态不一致
            results["fixed_inconsistencies"] = await self._fix_status_inconsistencies()
            
            # 5. 检查队列完整性
            await self._check_queue_integrity()
            
        except Exception as e:
            error_msg = f"维护过程中出错: {e}"
            logger.error(f"❌ {error_msg}")
            results["errors"].append(error_msg)
        
        duration = time.time() - start_time
        results["duration_seconds"] = round(duration, 2)
        results["end_time"] = datetime.now().isoformat()
        
        logger.info(f"✅ 队列维护完成，耗时: {duration:.2f}秒，结果: {results}")
        return results
    
    async def _cleanup_expired_tasks(self) -> int:
        """
        清理过期任务（超过24小时未完成的任务）
        
        Returns:
            清理的任务数量
        """
        try:
            cleaned_count = 0
            cutoff_time = time.time() - (24 * 3600)  # 24小时前
            
            # 检查主队列中的过期任务
            main_task_ids = await self.redis.lrange("task_queue_new", 0, -1)
            
            for task_id in main_task_ids:
                try:
                    task_id = int(task_id)
                    task_info_key = f"task_info:{task_id}"
                    task_data = await self.redis.hgetall(task_info_key)
                    
                    if task_data and 'created_at' in task_data:
                        created_at = float(task_data['created_at'])
                        if created_at < cutoff_time:
                            # 任务过期，移除
                            await self.queue_service.remove_task_from_queue(task_id)
                            cleaned_count += 1
                            logger.info(f"🧹 清理过期任务: {task_id}")
                            
                except Exception as e:
                    logger.warning(f"⚠️ 检查任务 {task_id} 时出错: {e}")
            
            return cleaned_count
            
        except Exception as e:
            logger.error(f"❌ 清理过期任务失败: {e}")
            return 0
    
    async def _fix_status_inconsistencies(self) -> int:
        """
        修复Redis和数据库之间的状态不一致
        
        Returns:
            修复的任务数量
        """
        try:
            fixed_count = 0
            
            # 获取处理队列中的所有任务
            processing_task_ids = await self.redis.lrange("task_processing_queue_new", 0, -1)
            
            for task_id in processing_task_ids:
                try:
                    task_id = int(task_id)
                    
                    # 获取数据库中的任务状态
                    db_task = await TasksDao.get_tasks_detail_by_id(self.db_session, task_id)
                    
                    if not db_task:
                        # 数据库中不存在，从队列移除
                        await self.redis.lrem("task_processing_queue_new", 1, task_id)
                        await self.queue_service._cleanup_task_data(task_id)
                        fixed_count += 1
                        logger.info(f"🔧 修复不存在任务: {task_id}")
                        continue
                    
                    # 获取Redis中的任务状态
                    task_status_key = f"task_status:{task_id}"
                    redis_status_data = await self.redis.hgetall(task_status_key)
                    redis_status = redis_status_data.get('status', 'unknown') if redis_status_data else 'unknown'
                    
                    # 检查状态一致性
                    if db_task.status != redis_status:
                        # 状态不一致，以数据库为准
                        await self.queue_service._update_task_status(
                            task_id, 
                            db_task.status, 
                            f"状态同步: {redis_status} -> {db_task.status}"
                        )
                        fixed_count += 1
                        logger.info(f"🔧 修复状态不一致，任务 {task_id}: {redis_status} -> {db_task.status}")
                    
                    # 如果数据库中任务已完成，但仍在处理队列中
                    if db_task.status in ['completed', 'failed', 'cancelled']:
                        await self.redis.lrem("task_processing_queue_new", 1, task_id)
                        fixed_count += 1
                        logger.info(f"🔧 从处理队列移除已完成任务: {task_id}")
                        
                except Exception as e:
                    logger.warning(f"⚠️ 修复任务 {task_id} 状态时出错: {e}")
            
            return fixed_count
            
        except Exception as e:
            logger.error(f"❌ 修复状态不一致失败: {e}")
            return 0
    
    async def _check_queue_integrity(self):
        """检查队列完整性"""
        try:
            # 检查是否有重复任务
            main_tasks = await self.redis.lrange("task_queue_new", 0, -1)
            processing_tasks = await self.redis.lrange("task_processing_queue_new", 0, -1)
            
            # 检查主队列重复
            main_unique = set(main_tasks)
            if len(main_tasks) != len(main_unique):
                logger.warning(f"⚠️ 主队列存在重复任务: {len(main_tasks)} vs {len(main_unique)}")
            
            # 检查处理队列重复
            processing_unique = set(processing_tasks)
            if len(processing_tasks) != len(processing_unique):
                logger.warning(f"⚠️ 处理队列存在重复任务: {len(processing_tasks)} vs {len(processing_unique)}")
            
            # 检查任务是否同时在两个队列中
            overlap = main_unique.intersection(processing_unique)
            if overlap:
                logger.warning(f"⚠️ 发现同时在两个队列中的任务: {overlap}")
                
        except Exception as e:
            logger.error(f"❌ 检查队列完整性失败: {e}")
    
    async def get_maintenance_status(self) -> Dict[str, Any]:
        """
        获取维护服务状态
        
        Returns:
            维护服务状态信息
        """
        queue_status = await self.queue_service.get_queue_status()
        
        return {
            "is_running": self.is_running,
            "queue_status": queue_status,
            "last_check": datetime.now().isoformat()
        }
    
    async def emergency_cleanup(self) -> Dict[str, Any]:
        """
        紧急清理（清理所有异常状态）
        
        Returns:
            清理结果
        """
        logger.warning("🚨 开始紧急清理")
        
        results = {
            "cleaned_locks": 0,
            "recovered_orphans": 0,
            "removed_invalid_tasks": 0
        }
        
        try:
            # 清理所有过期锁
            results["cleaned_locks"] = await self.queue_service.cleanup_expired_locks()
            
            # 恢复所有孤儿任务
            results["recovered_orphans"] = await self.queue_service.recover_orphaned_tasks()
            
            # 移除无效任务
            results["removed_invalid_tasks"] = await self._remove_invalid_tasks()
            
            logger.warning(f"🚨 紧急清理完成: {results}")
            return results
            
        except Exception as e:
            logger.error(f"❌ 紧急清理失败: {e}")
            raise
    
    async def _remove_invalid_tasks(self) -> int:
        """移除无效任务"""
        try:
            removed_count = 0
            
            # 检查所有队列中的任务
            all_queues = ["task_queue_new", "task_processing_queue_new"]
            
            for queue_key in all_queues:
                task_ids = await self.redis.lrange(queue_key, 0, -1)
                
                for task_id in task_ids:
                    try:
                        task_id = int(task_id)
                        
                        # 检查任务信息是否存在
                        task_info_key = f"task_info:{task_id}"
                        exists = await self.redis.hexists(task_info_key, "id")
                        
                        if not exists:
                            # 任务信息不存在，从队列移除
                            await self.redis.lrem(queue_key, 1, task_id)
                            removed_count += 1
                            logger.info(f"🧹 移除无效任务: {task_id} from {queue_key}")
                            
                    except Exception as e:
                        logger.warning(f"⚠️ 检查任务 {task_id} 时出错: {e}")
            
            return removed_count
            
        except Exception as e:
            logger.error(f"❌ 移除无效任务失败: {e}")
            return 0
