<template>
  <div>
    <div class="pageHeaderContent">
      <div class="avatar">
        <a-avatar size="large" :src="currentUser.avatar" />
      </div>
      <div class="content">
        <div class="contentTitle">
          你好{{ currentUser.name }}，欢迎来到CV-AI产品设计平台！
        </div>
        <div>{{ currentUser.title }} |{{ currentUser.group }}</div>
      </div>
    </div>

    <div style="padding: 10px">
      <a-row :gutter="24">
        <a-col :xl="16" :lg="24" :md="24" :sm="24" :xs="24">
          <a-card
            class="projectList"
            :style="{ marginBottom: '24px' }"
            title="最近的项目"
            :bordered="false"
            :loading="false"
            :body-style="{ padding: 0 }"
          >
            <template #extra>
              <a href=""> <span style="color: #1890ff">全部项目</span> </a>
            </template>
            <a-card-grid
              v-for="item in projectNotice"
              :key="item.id"
              class="projectGrid"
            >
              <a-card
                :body-style="{ padding: 0 }"
                style="box-shadow: none"
                :bordered="false"
              >
                <a-card-meta :description="item.description" class="w-full">
                  <template #title>
                    <div class="cardTitle">
                      <a-avatar size="small" :src="item.logo" />
                      <a :href="item.href">
                        {{ item.title }}
                      </a>
                    </div>
                  </template>
                </a-card-meta>
                <div class="projectItemContent">
                  <a :href="item.memberLink">
                    {{ item.member || "" }}
                  </a>
                  <span class="datetime" ml-2 :title="item.updatedAt">
                    {{ item.updatedAt }}
                  </span>
                </div>
              </a-card>
            </a-card-grid>
          </a-card>
        </a-col>
      </a-row>
    </div>
  </div>
</template>

<script>
import {
  Statistic,
  Row,
  Col,
  Card,
  CardGrid,
  CardMeta,
  List,
  ListItem,
  ListItemMeta,
  Avatar,
} from "ant-design-vue";
import 'ant-design-vue/dist/reset.css';

export default {
  components: {
    AStatistic: Statistic,
    ARow: Row,
    ACol: Col,
    ACard: Card,
    ACardGrid: CardGrid,
    ACardMeta: CardMeta,
    AList: List,
    AListItem: ListItem,
    AListItemMeta: ListItemMeta,
    AAvatar: Avatar,
  },
};
</script>


<script setup>
import EditableLinkGroup from "./editable-link-group.vue";

defineOptions({
  name: "DashBoard",
});

const currentUser = {
  avatar: "profile.png",
  name: "管理员",
  userid: "00000001",
  email: "<EMAIL>",
  title: "研发工程师",
  group: "三花研究院－2030实验室－先进技术部－数字化团队",
};

const projectNotice = [
  {
    id: "0001",
    title: "JM-CV-001-XXX",
    description: "JM-CV-001-XXX项目",
    updatedAt: "几秒前",
    member: "数字化团队",
    href: "",
    memberLink: "",
  },
  {
    id: "0002",
    title: "O-CV-001-XXX",
    description: "O-CV-001-XXX项目",
    updatedAt: "几秒前",
    member: "数字化团队",
    href: "",
    memberLink: "",
  },
  {
    id: "0003",
    title: "JM-CFD-CV-001-XXX",
    description: "JM-CFD-CV-001-XXX项目",
    updatedAt: "几秒前",
    member: "数字化团队",
    href: "",
    memberLink: "",
  },
];

</script>

<style scoped lang="less">
.textOverflow() {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  word-break: break-all;
}

// mixins for clearfix
// ------------------------
.clearfix() {
  zoom: 1;
  &::before,
  &::after {
    display: table;
    content: " ";
  }
  &::after {
    clear: both;
    height: 0;
    font-size: 0;
    visibility: hidden;
  }
}

.activitiesList {
  padding: 0 24px 8px 24px;
  .username {
    color: rgba(0, 0, 0, 0.65);
  }
  .event {
    font-weight: normal;
  }
}

.pageHeaderContent {
  display: flex;
  padding: 12px;
  margin-bottom: 24px;
  box-shadow: rgba(0, 0, 0, 0.1) 0px 4px 12px;
  .avatar {
    flex: 0 1 72px;
    & > span {
      display: block;
      width: 72px;
      height: 72px;
      border-radius: 72px;
    }
  }
  .content {
    position: relative;
    top: 4px;
    flex: 1 1 auto;
    margin-left: 24px;
    color: rgba(0, 0, 0, 0.45);
    line-height: 22px;
    .contentTitle {
      margin-bottom: 12px;
      color: rgba(0, 0, 0, 0.85);
      font-weight: 500;
      font-size: 20px;
      line-height: 28px;
    }
  }
}

.extraContent {
  .clearfix();

  float: right;
  white-space: nowrap;
  .statItem {
    position: relative;
    display: inline-block;
    padding: 0 32px;
    > p:first-child {
      margin-bottom: 4px;
      color: rgba(0, 0, 0, 0.45);
      font-size: 14px;
      line-height: 22px;
    }
    > p {
      margin: 0;
      color: rgba(0, 0, 0, 0.85);
      font-size: 30px;
      line-height: 38px;
      > span {
        color: rgba(0, 0, 0, 0.45);
        font-size: 20px;
      }
    }
    &::after {
      position: absolute;
      top: 8px;
      right: 0;
      width: 1px;
      height: 40px;
      background-color: #e8e8e8;
      content: "";
    }
    &:last-child {
      padding-right: 0;
      &::after {
        display: none;
      }
    }
  }
}

.members {
  a {
    display: block;
    height: 24px;
    margin: 12px 0;
    color: rgba(0, 0, 0, 0.65);
    transition: all 0.3s;
    .textOverflow();
    .member {
      margin-left: 12px;
      font-size: 14px;
      line-height: 24px;
      vertical-align: top;
    }
    &:hover {
      color: #1890ff;
    }
  }
}

.projectList {
  :deep(.ant-card-meta-description) {
    height: 44px;
    overflow: hidden;
    color: rgba(0, 0, 0, 0.45);
    line-height: 22px;
  }
  .cardTitle {
    font-size: 0;
    a {
      display: inline-block;
      height: 24px;
      margin-left: 12px;
      color: rgba(0, 0, 0, 0.85);
      font-size: 14px;
      line-height: 24px;
      vertical-align: top;
      &:hover {
        color: #1890ff;
      }
    }
  }
  .projectGrid {
    width: 33.33%;
  }
  .projectItemContent {
    display: flex;
    flex-basis: 100%;
    height: 20px;
    margin-top: 8px;
    overflow: hidden;
    font-size: 12px;
    line-height: 20px;
    .textOverflow();
    a {
      display: inline-block;
      flex: 1 1 0;
      color: rgba(0, 0, 0, 0.45);
      .textOverflow();
      &:hover {
        color: #1890ff;
      }
    }
    .datetime {
      flex: 0 0 auto;
      float: right;
      color: rgba(0, 0, 0, 0.25);
    }
  }
}

.datetime {
  color: rgba(0, 0, 0, 0.25);
}

@media screen and (max-width: 1200px) and (min-width: 992px) {
  .activeCard {
    margin-bottom: 24px;
  }
  .members {
    margin-bottom: 0;
  }
  .extraContent {
    margin-left: -44px;
    .statItem {
      padding: 0 16px;
    }
  }
}

@media screen and (max-width: 992px) {
  .activeCard {
    margin-bottom: 24px;
  }
  .members {
    margin-bottom: 0;
  }
  .extraContent {
    float: none;
    margin-right: 0;
    .statItem {
      padding: 0 16px;
      text-align: left;
      &::after {
        display: none;
      }
    }
  }
}

@media screen and (max-width: 768px) {
  .extraContent {
    margin-left: -16px;
  }
  .projectList {
    .projectGrid {
      width: 50%;
    }
  }
}

@media screen and (max-width: 576px) {
  .pageHeaderContent {
    display: block;
    .content {
      margin-left: 0;
    }
  }
  .extraContent {
    .statItem {
      float: none;
    }
  }
}

@media screen and (max-width: 480px) {
  .projectList {
    .projectGrid {
      width: 100%;
    }
  }
}
</style>