from fastapi import Depends, HTTPException, status
from typing import Optional, Union, List
from sqlalchemy.ext.asyncio import AsyncSession
from config.get_db import get_db
from config.role_constants import RoleConstants
from exceptions.exception import PermissionException
from module_admin.entity.vo.user_vo import CurrentUserModel
from module_admin.service.login_service import LoginService
from module_business.dao.project_members_dao import ProjectMembersDao
from module_business.dao.projects_dao import ProjectsDao
from module_business.entity.vo.projects_vo import ProjectsModel


class CheckProjectPermission:
    """
    检查用户对项目的权限
    """

    def __init__(
        self,
        required_role: Optional[str] = None,
        allow_owner: bool = True,
        project_id_param: str = 'project_id'
    ):
        """
        初始化项目权限检查器

        :param required_role: 需要的项目角色类型 ('project_manager' 或 'project_member')，None表示任何项目成员都可以
        :param allow_owner: 是否允许项目所有者访问
        :param project_id_param: 项目ID参数名称
        """
        self.required_role = required_role
        self.allow_owner = allow_owner
        self.project_id_param = project_id_param

    def __call__(
        self,
        project_id: int,
        current_user: CurrentUserModel = Depends(LoginService.get_current_user),
        query_db: AsyncSession = Depends(get_db)
    ):
        """
        执行项目权限检查

        :param project_id: 项目ID
        :param current_user: 当前用户
        :param query_db: 数据库会话
        :return: 权限检查结果
        """
        return self._check_permission(project_id, current_user, query_db)

    async def _check_permission(
        self,
        project_id: int,
        current_user: CurrentUserModel,
        query_db: AsyncSession
    ) -> bool:
        """
        检查用户对项目的权限

        :param project_id: 项目ID
        :param current_user: 当前用户
        :param query_db: 数据库会话
        :return: 是否有权限
        """
        user_id = current_user.user.user_id
        user_role_keys = [role.role_key for role in current_user.user.role]

        # 超级管理员拥有所有权限
        if RoleConstants.is_super_admin(user_role_keys):
            return True

        # 检查项目是否存在
        project = await ProjectsDao.get_projects_detail_by_info(
            query_db, ProjectsModel(project_id=project_id)
        )
        if not project:
            raise PermissionException(message='项目不存在')

        # 检查是否为项目所有者
        if self.allow_owner and project.owner_id == user_id:
            return True

        # 检查项目成员权限
        has_permission = await ProjectMembersDao.check_user_project_permission(
            query_db, user_id, project_id, self.required_role
        )

        if not has_permission:
            if self.required_role:
                raise PermissionException(message=f'您没有该项目的{RoleConstants.ROLE_NAMES.get(self.required_role, "相应")}权限')
            else:
                raise PermissionException(message='您不是该项目的成员')

        return True


class CheckProjectManagerPermission(CheckProjectPermission):
    """
    检查项目管理员权限
    """

    def __init__(self, allow_owner: bool = True, project_id_param: str = 'project_id'):
        super().__init__(
            required_role=RoleConstants.PROJECT_ROLE_MANAGER,
            allow_owner=allow_owner,
            project_id_param=project_id_param
        )


class CheckProjectMemberPermission(CheckProjectPermission):
    """
    检查项目成员权限（包括项目管理员）
    """

    def __init__(self, allow_owner: bool = True, project_id_param: str = 'project_id'):
        super().__init__(
            required_role=None,  # 任何项目成员都可以
            allow_owner=allow_owner,
            project_id_param=project_id_param
        )


class ProjectDataScope:
    """
    项目数据权限过滤器
    """

    def __init__(self, query_alias: str = 'RdProjects'):
        """
        初始化项目数据权限过滤器

        :param query_alias: 查询表的别名
        """
        self.query_alias = query_alias

    def __call__(
        self,
        current_user: CurrentUserModel = Depends(LoginService.get_current_user),
        query_db: AsyncSession = Depends(get_db)
    ):
        """
        生成项目数据权限过滤条件

        :param current_user: 当前用户
        :param query_db: 数据库会话
        :return: 数据权限过滤条件
        """
        return self._generate_data_scope_sql(current_user, query_db)

    async def _generate_data_scope_sql(
        self,
        current_user: CurrentUserModel,
        query_db: AsyncSession
    ) -> str:
        """
        生成项目数据权限SQL条件

        :param current_user: 当前用户
        :param query_db: 数据库会话
        :return: SQL条件字符串
        """
        user_id = current_user.user.user_id
        user_role_keys = [role.role_key for role in current_user.user.role]

        # 超级管理员可以查看所有项目
        if RoleConstants.is_super_admin(user_role_keys):
            return "all"

        # 项目管理员和项目成员只能查看自己参与的项目
        if RoleConstants.is_project_manager(user_role_keys) or RoleConstants.is_project_member(user_role_keys):
            # 获取用户参与的项目ID列表
            user_projects = await ProjectMembersDao.get_user_projects(query_db, user_id)
            project_ids = [project.project_id for project in user_projects]

            # 获取用户拥有的项目ID列表
            owned_projects = await ProjectsDao.get_projects_by_owner(query_db, user_id)
            owned_project_ids = [project.project_id for project in owned_projects]

            # 合并项目ID列表
            all_project_ids = list(set(project_ids + owned_project_ids))

            if all_project_ids:
                return f"project_ids:{','.join(map(str, all_project_ids))}"
            else:
                return f"owner_only:{user_id}"

        # 普通用户无法查看任何项目
        return "none"


class TaskDataScope:
    """
    任务数据权限过滤器
    """

    def __init__(self, query_alias: str = 'RdTasks'):
        """
        初始化任务数据权限过滤器

        :param query_alias: 查询表的别名
        """
        self.query_alias = query_alias

    def __call__(
        self,
        current_user: CurrentUserModel = Depends(LoginService.get_current_user),
        query_db: AsyncSession = Depends(get_db)
    ):
        """
        生成任务数据权限过滤条件

        :param current_user: 当前用户
        :param query_db: 数据库会话
        :return: 数据权限过滤条件
        """
        return self._generate_data_scope_sql(current_user, query_db)

    async def _generate_data_scope_sql(
        self,
        current_user: CurrentUserModel,
        query_db: AsyncSession
    ) -> str:
        """
        生成任务数据权限SQL条件

        :param current_user: 当前用户
        :param query_db: 数据库会话
        :return: SQL条件字符串
        """
        user_id = current_user.user.user_id
        user_role_keys = [role.role_key for role in current_user.user.role]

        # 超级管理员可以查看所有任务
        if RoleConstants.is_super_admin(user_role_keys):
            return "1 == 1"

        # 项目管理员和项目成员只能查看自己参与项目的任务
        if RoleConstants.is_project_manager(user_role_keys) or RoleConstants.is_project_member(user_role_keys):
            # 获取用户参与的项目ID列表
            user_projects = await ProjectMembersDao.get_user_projects(query_db, user_id)
            project_ids = [str(project.project_id) for project in user_projects]
            
            # 获取用户拥有的项目ID列表
            owned_projects = await ProjectsDao.get_projects_by_owner(query_db, user_id)
            owned_project_ids = [str(project.project_id) for project in owned_projects]
            
            all_project_ids = list(set(project_ids + owned_project_ids))
            
            if all_project_ids:
                return f"{self.query_alias}.project_id.in_([{','.join(all_project_ids)}])"
            else:
                return "1 == 0"

        # 普通用户无法查看任何任务
        return "1 == 0"


def check_task_edit_permission(
    task_id: int,
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
    query_db: AsyncSession = Depends(get_db)
):
    """
    检查任务编辑权限
    
    :param task_id: 任务ID
    :param current_user: 当前用户
    :param query_db: 数据库会话
    :return: 权限检查结果
    """
    return _check_task_edit_permission(task_id, current_user, query_db)


async def _check_task_edit_permission(
    task_id: int,
    current_user: CurrentUserModel,
    query_db: AsyncSession
) -> bool:
    """
    检查任务编辑权限的具体实现
    
    :param task_id: 任务ID
    :param current_user: 当前用户
    :param query_db: 数据库会话
    :return: 是否有权限
    """
    from module_business.dao.tasks_dao import TasksDao
    from module_business.entity.vo.tasks_vo import TasksModel
    
    user_id = current_user.user.user_id
    user_role_keys = [role.role_key for role in current_user.user.role]

    # 超级管理员拥有所有权限
    if RoleConstants.is_super_admin(user_role_keys):
        return True

    # 获取任务信息
    task = await TasksDao.get_tasks_detail_by_info(
        query_db, TasksModel(task_id=task_id)
    )
    if not task:
        raise PermissionException(message='任务不存在')

    # 检查是否为任务创建者
    if task.assigned_to == user_id:
        return True

    # 检查是否为项目管理员
    if RoleConstants.is_project_manager(user_role_keys):
        has_manager_permission = await ProjectMembersDao.check_user_project_permission(
            query_db, user_id, task.project_id, RoleConstants.PROJECT_ROLE_MANAGER
        )
        if has_manager_permission:
            return True

    # 检查是否为项目所有者
    project = await ProjectsDao.get_projects_detail_by_info(
        query_db, ProjectsModel(project_id=task.project_id)
    )
    if project and project.owner_id == user_id:
        return True

    raise PermissionException(message='您没有编辑该任务的权限')


async def _check_project_edit_permission(
    project_id: int,
    current_user: CurrentUserModel,
    query_db: AsyncSession
) -> bool:
    """
    检查项目编辑权限的具体实现

    :param project_id: 项目ID
    :param current_user: 当前用户
    :param query_db: 数据库会话
    :return: 是否有权限
    """
    user_id = current_user.user.user_id
    user_role_keys = [role.role_key for role in current_user.user.role]

    # 超级管理员拥有所有权限
    if RoleConstants.is_super_admin(user_role_keys):
        return True

    # 获取项目信息
    project = await ProjectsDao.get_projects_detail_by_info(
        query_db, ProjectsModel(project_id=project_id)
    )
    if not project:
        raise PermissionException(message='项目不存在')

    # 检查是否为项目所有者
    if project.owner_id == user_id:
        return True

    # 检查是否为项目管理员
    if RoleConstants.is_project_manager(user_role_keys):
        has_manager_permission = await ProjectMembersDao.check_user_project_permission(
            query_db, user_id, project_id, RoleConstants.PROJECT_ROLE_MANAGER
        )
        if has_manager_permission:
            return True

    raise PermissionException(message='您没有编辑该项目的权限')
