from fastapi import Request
from config.enums import RedisInitKeyConfig
from config.get_redis import RedisUtil
from module_admin.entity.vo.cache_vo import CacheInfoModel, CacheMonitorModel
from module_admin.entity.vo.common_vo import CrudResponseModel


class CacheService:
    """
    缓存监控模块服务层
    """

    @classmethod
    async def get_cache_monitor_statistical_info_services(cls, request: Request):
        """
        获取缓存监控信息service

        :param request: Request对象
        :return: 缓存监控信息
        """
        info = await request.app.state.redis.info()
        db_size = await request.app.state.redis.dbsize()
        command_stats_dict = await request.app.state.redis.info('commandstats')
        command_stats = [
            dict(name=key.split('_')[1], value=str(value.get('calls'))) for key, value in command_stats_dict.items()
        ]
        result = CacheMonitorModel(commandStats=command_stats, dbSize=db_size, info=info)

        return result

    @classmethod
    async def get_cache_monitor_cache_name_services(cls):
        """
        获取缓存名称列表信息service

        :return: 缓存名称列表信息
        """
        name_list = []
        for key_config in RedisInitKeyConfig:
            name_list.append(
                CacheInfoModel(
                    cacheKey='',
                    cacheName=key_config.key,
                    cacheValue='',
                    remark=key_config.remark,
                )
            )

        return name_list

    @classmethod
    async def get_cache_monitor_cache_key_services(cls, request: Request, cache_name: str):
        """
        获取缓存键名列表信息service

        :param request: Request对象
        :param cache_name: 缓存名称
        :return: 缓存键名列表信息
        """
        cache_keys = await request.app.state.redis.keys(f'{cache_name}*')
        cache_key_list = []

        for key in cache_keys:
            if key == cache_name:
                # 直接键名（如 task_queue_new）
                cache_key_list.append('')  # 空字符串表示没有后缀
            elif key.startswith(f'{cache_name}:'):
                # 前缀:后缀格式（如 sys_dict:user_type）
                suffix = key.split(':', 1)[1]
                cache_key_list.append(suffix)

        return cache_key_list

    @classmethod
    async def get_cache_monitor_cache_value_services(cls, request: Request, cache_name: str, cache_key: str):
        """
        获取缓存内容信息service

        :param request: Request对象
        :param cache_name: 缓存名称
        :param cache_key: 缓存键名
        :return: 缓存内容信息
        """
        import json

        # 处理直接键名和前缀:后缀格式
        if cache_key == '' or cache_key == '_DIRECT_KEY_':
            # 空字符串或特殊标识表示直接键名（如 task_queue_new）
            full_key = cache_name
            # 统一处理，将特殊标识转换为空字符串用于返回
            cache_key = '' if cache_key == '_DIRECT_KEY_' else cache_key
        else:
            # 前缀:后缀格式（如 sys_dict:user_type）
            full_key = f'{cache_name}:{cache_key}'
        redis = request.app.state.redis

        # 检查键是否存在
        if not await redis.exists(full_key):
            return CacheInfoModel(cacheKey=cache_key, cacheName=cache_name, cacheValue='', remark='键不存在')

        # 获取数据类型
        key_type = await redis.type(full_key)
        cache_value = ''
        remark = f'数据类型: {key_type}'

        try:
            if key_type == 'string':
                cache_value = await redis.get(full_key)
            elif key_type == 'list':
                # 获取列表所有元素
                list_data = await redis.lrange(full_key, 0, -1)
                cache_value = json.dumps(list_data, ensure_ascii=False, indent=2)
            elif key_type == 'hash':
                # 获取哈希所有字段
                hash_data = await redis.hgetall(full_key)
                cache_value = json.dumps(hash_data, ensure_ascii=False, indent=2)
            elif key_type == 'set':
                # 获取集合所有成员
                set_data = list(await redis.smembers(full_key))
                cache_value = json.dumps(set_data, ensure_ascii=False, indent=2)
            elif key_type == 'zset':
                # 获取有序集合所有成员及分数
                zset_data = await redis.zrange(full_key, 0, -1, withscores=True)
                # 转换为字典格式便于显示
                zset_dict = {member: score for member, score in zset_data}
                cache_value = json.dumps(zset_dict, ensure_ascii=False, indent=2)
            else:
                cache_value = f'不支持的数据类型: {key_type}'
                remark = f'数据类型: {key_type} (不支持)'
        except Exception as e:
            cache_value = f'获取数据失败: {str(e)}'
            remark = f'数据类型: {key_type}, 错误: {str(e)}'

        return CacheInfoModel(cacheKey=cache_key, cacheName=cache_name, cacheValue=cache_value, remark=remark)

    @classmethod
    async def clear_cache_monitor_cache_name_services(cls, request: Request, cache_name: str):
        """
        清除缓存名称对应所有键值service

        :param request: Request对象
        :param cache_name: 缓存名称
        :return: 操作缓存响应信息
        """
        cache_keys = await request.app.state.redis.keys(f'{cache_name}*')
        if cache_keys:
            await request.app.state.redis.delete(*cache_keys)

        return CrudResponseModel(is_success=True, message=f'{cache_name}对应键值清除成功')

    @classmethod
    async def clear_cache_monitor_cache_key_services(cls, request: Request, cache_key: str):
        """
        清除缓存名称对应所有键值service

        :param request: Request对象
        :param cache_key: 缓存键名
        :return: 操作缓存响应信息
        """
        cache_keys = await request.app.state.redis.keys(f'*{cache_key}')
        if cache_keys:
            await request.app.state.redis.delete(*cache_keys)

        return CrudResponseModel(is_success=True, message=f'{cache_key}清除成功')

    @classmethod
    async def clear_cache_monitor_all_services(cls, request: Request):
        """
        清除所有缓存service

        :param request: Request对象
        :return: 操作缓存响应信息
        """
        cache_keys = await request.app.state.redis.keys()
        if cache_keys:
            await request.app.state.redis.delete(*cache_keys)

        await RedisUtil.init_sys_dict(request.app.state.redis)
        await RedisUtil.init_sys_config(request.app.state.redis)

        return CrudResponseModel(is_success=True, message='所有缓存清除成功')
