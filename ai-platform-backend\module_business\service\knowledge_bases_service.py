from sqlalchemy.ext.asyncio import AsyncSession
from typing import List
from exceptions.exception import ServiceException
from module_admin.entity.vo.common_vo import CrudResponseModel
from module_business.dao.knowledge_bases_dao import KnowledgeBasesDao
from module_business.entity.vo.knowledge_bases_vo import DeleteKnowledgeBasesModel, KnowledgeBasesModel, KnowledgeBasesPageQueryModel
from utils.common_util import CamelCaseUtil
from utils.excel_util import ExcelUtil


class KnowledgeBasesService:
    """
    数据库管理模块服务层
    """

    @classmethod
    async def get_knowledge_bases_list_services(
        cls, query_db: AsyncSession, query_object: KnowledgeBasesPageQueryModel, is_page: bool = False
    ):
        """获取数据库管理列表信息service"""
        return await KnowledgeBasesDao.get_knowledge_bases_list(query_db, query_object, is_page)

    @classmethod
    async def add_knowledge_bases_services(cls, query_db: AsyncSession, page_object: KnowledgeBasesModel):
        """新增数据库管理信息service"""
        try:
            await KnowledgeBasesDao.add_knowledge_bases_dao(query_db, page_object)
            await query_db.commit()
            return CrudResponseModel(is_success=True, message='新增成功')
        except Exception as e:
            await query_db.rollback()
            raise e

    @classmethod
    async def edit_knowledge_bases_services(cls, query_db: AsyncSession, page_object: KnowledgeBasesModel):
        """编辑数据库管理信息service"""
        knowledge_bases_info = await cls.knowledge_bases_detail_services(query_db, page_object.kb_id)
        if not knowledge_bases_info.kb_id:
            raise ServiceException(message='数据库管理不存在')
        
        try:
            edit_knowledge_bases = page_object.model_dump(exclude_unset=True, exclude={'type_ids', 'type_names'})
            await KnowledgeBasesDao.edit_knowledge_bases_dao(query_db, edit_knowledge_bases)
            await query_db.commit()
            return CrudResponseModel(is_success=True, message='更新成功')
        except Exception as e:
            await query_db.rollback()
            raise e

    @classmethod
    async def delete_knowledge_bases_services(cls, query_db: AsyncSession, page_object: DeleteKnowledgeBasesModel):
        """删除数据库管理信息service"""
        if not page_object.kb_ids:
            raise ServiceException(message='传入数据库id为空')
        
        try:
            kb_id_list = page_object.kb_ids.split(',')
            for kb_id in kb_id_list:
                await KnowledgeBasesDao.delete_knowledge_bases_dao(query_db, KnowledgeBasesModel(kbId=kb_id))
            await query_db.commit()
            return CrudResponseModel(is_success=True, message='删除成功')
        except Exception as e:
            await query_db.rollback()
            raise e

    @classmethod
    async def knowledge_bases_detail_services(cls, query_db: AsyncSession, kb_id: int):
        """获取数据库管理详细信息service"""
        knowledge_bases = await KnowledgeBasesDao.get_knowledge_bases_detail_by_id(query_db, kb_id=kb_id)
        if knowledge_bases:
            return KnowledgeBasesModel(**CamelCaseUtil.transform_result(knowledge_bases))
        return KnowledgeBasesModel(**dict())

    @classmethod
    async def knowledge_bases_detail_with_types_services(cls, query_db: AsyncSession, kb_id: int):
        """获取数据库管理详细信息（包含类型信息）service"""
        knowledge_bases = await KnowledgeBasesDao.get_knowledge_bases_detail_with_types_by_id(query_db, kb_id=kb_id)
        if knowledge_bases:
            return KnowledgeBasesModel(**knowledge_bases)
        return KnowledgeBasesModel(**dict())

    @classmethod
    async def get_knowledge_bases_with_types_services(cls, query_db: AsyncSession, page_num: int = 1, page_size: int = 10, kb_name: str = None):
        """获取所有数据库及其类型信息service"""
        return await KnowledgeBasesDao.get_knowledge_bases_with_types(query_db, page_num, page_size, kb_name)

    @staticmethod
    async def export_knowledge_bases_list_services(knowledge_bases_list: List):
        """导出数据库管理信息service"""
        mapping_dict = {
            'kbId': '数据库id',
            'projectId': 'NULL表示通用数据库',
            'kbName': '数据库名称',
            'description': '数据库描述',
            'ownerId': '数据库所有者',
            'vectorStoreId': '向量库标识',
            'isPublic': '是否公开',
            'createdAt': '创建时间',
            'updatedAt': '更新时间',
            'isDeleted': '是否删除',
            'fileTypeStats': 'JSON示例: model=5, report=3, log=12',
        }
        return ExcelUtil.export_list2excel(knowledge_bases_list, mapping_dict)
    
    @classmethod
    async def get_knowledge_bases_by_project_id_services(cls, query_db: AsyncSession, project_id: int):
        """根据项目ID获取关联的数据库列表service"""
        return await KnowledgeBasesDao.get_knowledge_bases_by_project_id(query_db, project_id)
    
    @classmethod
    async def delete_knowledge_bases_by_project_id_services(cls, query_db: AsyncSession, project_id: int):
        """根据项目ID逻辑删除关联的数据库service"""
        await KnowledgeBasesDao.delete_knowledge_bases_by_project_id(query_db, project_id)

