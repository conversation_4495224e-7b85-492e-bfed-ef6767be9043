from minio import Minio
from minio.error import S3Error
import os
from typing import Optional, BinaryIO
from config.env import MinioConfig
from utils.log_util import logger
from sqlalchemy.ext.asyncio import AsyncSession

class MinioUtil:
    """MinIO工具类，用于处理文件上传和下载"""
    
    _instance = None
    _client = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(MinioUtil, cls).__new__(cls)
        return cls._instance
    
    def __init__(self):
        if self._client is None:
            try:
                self._client = Minio(
                    endpoint=MinioConfig.minio_endpoint,
                    access_key=MinioConfig.minio_access_key,
                    secret_key=MinioConfig.minio_secret_key,
                    secure=MinioConfig.minio_secure
                )
            except Exception as e:
                logger.error(f"MinIO client initialization failed: {str(e)}")
                raise e
    
    @classmethod
    def get_instance(cls):
        """获取MinIO工具类实例"""
        if cls._instance is None:
            cls._instance = cls()
        return cls._instance
    
    def upload_file(self, file: BinaryIO, kb_id: int, file_type: str, file_name: str, task_id: int = None, sub_folder: str = None) -> str:
        """
        上传文件到MinIO

        :param file: 文件对象
        :param kb_id: 数据库ID
        :param file_type: 文件类型 (model/report/dataset/log/document)
        :param file_name: 文件名
        :param task_id: 任务ID (可选，如果提供则使用新的路径结构)
        :param sub_folder: 子文件夹名称 (可选，在task_id和file_type之间添加额外的文件夹层级)
        :return: 文件在MinIO中的路径
        """
        try:
            bucket_name = MinioConfig.minio_bucket_name
            # 根据参数构建路径结构
            if task_id:
                if sub_folder:
                    # 完整路径：kb_{kb_id}/task_{task_id}/{sub_folder}/{file_type}/{file_name}
                    object_name = f"kb_{kb_id}/task_{task_id}/{sub_folder}/{file_type}/{file_name}"
                else:
                    # 标准新路径：kb_{kb_id}/task_{task_id}/{file_type}/{file_name}
                    object_name = f"kb_{kb_id}/task_{task_id}/{file_type}/{file_name}"
            else:
                # 兼容旧格式：kb_{kb_id}/{file_type}/{file_name}
                object_name = f"kb_{kb_id}/{file_type}/{file_name}"
            
            # 确保bucket存在
            if not self._client.bucket_exists(bucket_name):
                self._client.make_bucket(bucket_name)
            
            # 获取文件大小
            try:
                # 尝试使用fileno()获取文件大小（适用于真实文件对象）
                file_size = os.fstat(file.fileno()).st_size
            except (AttributeError, OSError):
                # 对于BytesIO等内存文件对象，使用len()获取大小
                current_pos = file.tell()
                file.seek(0, 2)  # 移动到文件末尾
                file_size = file.tell()
                file.seek(current_pos)  # 恢复到原位置
            
            # 上传文件
            self._client.put_object(
                bucket_name=bucket_name,
                object_name=object_name,
                data=file,
                length=file_size,
                content_type=self._get_content_type(file_name)
            )
            
            return object_name
            
        except S3Error as e:
            logger.error(f"Failed to upload file to MinIO: {str(e)}")
            raise e
    
    def download_file(self, kb_id: int, file_type: str, file_name: str) -> bytes:
        """
        从MinIO下载文件
        
        :param kb_id: 数据库ID
        :param file_type: 文件类型 (model/report/dataset/log/document)
        :param file_name: 文件名
        :return: 文件内容（字节）
        """
        try:
            bucket_name = MinioConfig.minio_bucket_name
            object_name = f"kb_{kb_id}/{file_type}/{file_name}"
            
            # 获取文件
            response = self._client.get_object(bucket_name, object_name)
            return response.read()
            
        except S3Error as e:
            logger.error(f"Failed to download file from MinIO: {str(e)}")
            raise e
    
    async def download_file_by_id(self, file_id: int, query_db: AsyncSession) -> bytes:
        """
        根据文件ID从MinIO下载文件

        :param file_id: 文件ID
        :param query_db: 数据库会话
        :return: 文件内容（字节）
        """
        try:
            # 从数据库获取文件信息
            from module_business.service.files_service import FilesService
            file_info = await FilesService.files_detail_services(query_db, file_id)
            if not file_info:
                raise Exception(f"File with ID {file_id} not found")

            logger.info(f"下载文件 ID {file_id}: 存储路径={file_info.storage_path}, 原始名称={file_info.original_name}")

            # 直接使用存储路径下载文件，这是最准确的方式
            return await self.download_file_by_path(file_info.storage_path)

        except Exception as e:
            logger.error(f"Failed to download file by ID {file_id}: {str(e)}")
            raise e

    async def delete_file_by_id(self, file_id: int, query_db: AsyncSession) -> bool:
        """
        根据文件ID从MinIO删除文件
        
        :param file_id: 文件ID
        :param query_db: 数据库会话
        :return: 删除是否成功
        """
        try:
            # 从数据库获取文件信息
            from module_business.service.files_service import FilesService
            file_info = await FilesService.files_detail_services(query_db, file_id)
            if not file_info:
                raise Exception(f"File with ID {file_id} not found")
            
            kb_id = file_info.kb_id
            file_type = file_info.file_type
            file_name = file_info.original_name

            # 删除文件
            return self.delete_file(
                kb_id=int(kb_id),
                file_type=file_type,
                file_name=file_name
            )
            
        except Exception as e:
            logger.error(f"Failed to delete file by ID: {str(e)}")
            raise e

    def delete_file(self, kb_id: int, file_type: str, file_name: str) -> bool:
        """
        从MinIO删除文件
        
        :param kb_id: 数据库ID
        :param file_type: 文件类型 (model/report/dataset/log/document)
        :param file_name: 文件名
        :return: 删除是否成功
        """
        try:
            bucket_name = MinioConfig.minio_bucket_name
            object_name = f"kb_{kb_id}/{file_type}/{file_name}"
            
            # 删除文件
            self._client.remove_object(bucket_name, object_name)
            return True
            
        except S3Error as e:
            logger.error(f"Failed to delete file from MinIO: {str(e)}")
            raise e
    
    def _get_content_type(self, file_name: str) -> str:
        """
        根据文件名获取对应的Content-Type
        
        :param file_name: 文件名
        :return: Content-Type
        """
        ext = os.path.splitext(file_name)[1].lower()
        content_types = {
            '.txt': 'text/plain',
            '.pdf': 'application/pdf',
            '.doc': 'application/msword',
            '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            '.xls': 'application/vnd.ms-excel',
            '.xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            '.csv': 'text/csv',
            '.json': 'application/json',
            '.zip': 'application/zip',
            '.rar': 'application/x-rar-compressed',
            '.gz': 'application/gzip',
            '.bz2': 'application/x-bzip2',
            '.tar': 'application/x-tar',
            '.7z': 'application/x-7z-compressed',
            '.xz': 'application/x-xz',
            '.png': 'image/png',
            '.jpg': 'image/jpeg',
            '.jpeg': 'image/jpeg',
            '.gif': 'image/gif',
            '.mp4': 'video/mp4',
            '.mp3': 'audio/mpeg',
            '.wav': 'audio/wav'
        }
        return content_types.get(ext, 'application/octet-stream')

    async def download_file_by_path(self, file_path: str) -> bytes:
        """
        根据文件路径从MinIO下载文件

        :param file_path: 文件路径 (格式: kb_{kb_id}/{file_type}/{file_name})
        :return: 文件内容（字节）
        """
        try:
            bucket_name = MinioConfig.minio_bucket_name

            # 清理文件路径，确保符合MinIO对象名规范
            clean_path = self._sanitize_object_name(file_path)
            logger.info(f"原始路径: {file_path}, 清理后路径: {clean_path}")

            # 获取文件
            response = self._client.get_object(bucket_name, clean_path)
            return response.read()

        except S3Error as e:
            logger.error(f"Failed to download file by path from MinIO: {str(e)}")
            raise e

    def _sanitize_object_name(self, object_name: str) -> str:
        """
        清理对象名，确保符合MinIO规范

        :param object_name: 原始对象名
        :return: 清理后的对象名
        """
        import re

        # 移除URL前缀（如果存在）
        if object_name.startswith('http://') or object_name.startswith('https://'):
            from urllib.parse import urlparse, parse_qs
            parsed_url = urlparse(object_name)
            if 'path' in parse_qs(parsed_url.query):
                object_name = parse_qs(parsed_url.query)['path'][0]

        # 替换不支持的字符
        # MinIO不支持的字符: < > : " | ? * 以及控制字符
        clean_name = re.sub(r'[<>:"|?*\x00-\x1f\x7f]', '_', object_name)

        # 替换空格为下划线（避免路径问题）
        clean_name = clean_name.replace(' ', '_')

        # 替换括号（避免文件系统问题）
        clean_name = clean_name.replace('(', '_').replace(')', '_')

        # 确保路径不以/开头（MinIO对象名不应该以/开头）
        clean_name = clean_name.lstrip('/')

        # 确保路径不为空
        if not clean_name:
            raise ValueError("Object name cannot be empty after sanitization")

        return clean_name