import request from '@/utils/request'

// 添加项目类型关联
export function addProjectTypeRel(data) {
  return request({
    url: '/business/project_types_rel',
    method: 'post',
    data: data
  })
}

// 删除项目类型关联
export function deleteProjectTypeRel(data) {
  return request({
    url: '/business/project_types_rel',
    method: 'delete',
    data: data
  })
}

// 删除特定的项目类型关联
export function deleteProjectTypeRelByProjectAndType(projectId, typeId) {
  return request({
    url: `/business/project_types_rel/${projectId}/${typeId}`,
    method: 'delete'
  })
}

// 获取项目的类型关联列表
export function getProjectTypeRels(projectId) {
  return request({
    url: `/business/project_types_rel/list/${projectId}`,
    method: 'get'
  })
}

// 根据类型获取项目列表
export function getProjectsByTypes(params) {
  return request({
    url: '/business/project_types_rel/projects/by_types',
    method: 'get',
    params: params
  })
} 