# -------- 应用配置 --------
# 应用运行环境
APP_ENV = 'sit'
# 应用名称
APP_NAME = 'RD-AI-Platform'
# 应用代理路径
APP_ROOT_PATH = '/dev-api'
# 应用主机
APP_HOST = '0.0.0.0'
# 应用端口
APP_PORT = 9099
# 应用版本
APP_VERSION= '1.6.2'
# 应用是否开启热重载
APP_RELOAD = true
# 应用是否开启IP归属区域查询
APP_IP_LOCATION_QUERY = true
# 应用是否允许账号同时登录
APP_SAME_TIME_LOGIN = true

# -------- Jwt配置 --------
# Jwt秘钥
JWT_SECRET_KEY = 'b01c66dc2c58dc6a0aabfe2144256be36226de378bf87f72c0c795dda67f4d55'
# Jwt算法
JWT_ALGORITHM = 'HS256'
# 令牌过期时间
JWT_EXPIRE_MINUTES = 1440
# redis中令牌过期时间
JWT_REDIS_EXPIRE_MINUTES = 30


# -------- 数据库配置 --------
# 数据库类型，可选的有'mysql'、'postgresql'，默认为'mysql'
DB_TYPE = 'mysql'
# 数据库主机
DB_HOST = '************'
# 数据库端口
DB_PORT = 3306
# 数据库用户名
DB_USERNAME = 'aiplatform'
# 数据库密码
DB_PASSWORD = 'Shrd@2030'
# 数据库名称
DB_DATABASE = 'ai_platform'
# 是否开启sqlalchemy日志
DB_ECHO = true
# 允许溢出连接池大小的最大连接数
DB_MAX_OVERFLOW = 10
# 连接池大小，0表示连接数无限制
DB_POOL_SIZE = 50
# 连接回收时间（单位：秒）
DB_POOL_RECYCLE = 3600
# 连接池中没有线程可用时，最多等待的时间（单位：秒）
DB_POOL_TIMEOUT = 30

# -------- Redis配置 --------
# Redis主机
REDIS_HOST = '************'
# Redis端口
REDIS_PORT = 6379
# Redis用户名
REDIS_USERNAME = ''
# Redis密码
REDIS_PASSWORD = ''
# Redis数据库
REDIS_DATABASE = 2

# -------- Minio配置 --------
# minio控制台端口是9001，api端口是9000
MINIO_ENDPOINT = '************:9000'
MINIO_ACCESS_KEY = 'minioadmin'
MINIO_SECRET_KEY = 'minioadmin'
MINIO_BUCKET_NAME = 'ai-platform-sit'
MINIO_SECURE = false


# -------- kafka配置 --------
KAFKA_BOOTSTRAP_SERVERS = ************:9092
KAFKA_TASK_LOGS_TOPIC = task_logs_sit
KAFKA_CONSUMER_GROUP_ID = ai_platform_log_consumer_sit
