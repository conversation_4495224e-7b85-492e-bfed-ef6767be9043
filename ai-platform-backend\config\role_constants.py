"""
角色权限常量定义
"""


class RoleConstants:
    """角色权限常量"""
    
    # 角色键值
    SUPER_ADMIN = 'super_admin'  # 超级管理员
    PROJECT_MANAGER = 'project_manager'  # 项目管理员
    PROJECT_MEMBER = 'project_member'  # 项目成员
    COMMON_USER = 'common_user'  # 普通用户
    
    # 角色名称
    ROLE_NAMES = {
        SUPER_ADMIN: '超级管理员',
        PROJECT_MANAGER: '项目管理员',
        PROJECT_MEMBER: '项目成员',
        COMMON_USER: '普通用户',
    }
    
    # 项目成员角色类型
    PROJECT_ROLE_MANAGER = 'project_manager'  # 项目管理员
    PROJECT_ROLE_MEMBER = 'project_member'  # 项目成员
    
    # 权限级别（数字越小权限越高）
    ROLE_LEVELS = {
        SUPER_ADMIN: 1,
        PROJECT_MANAGER: 2,
        PROJECT_MEMBER: 3,
        COMMON_USER: 4,
    }
    
    # 数据权限范围
    DATA_SCOPE_ALL = '1'  # 全部数据权限
    DATA_SCOPE_CUSTOM = '2'  # 自定数据权限
    DATA_SCOPE_DEPT = '3'  # 本部门数据权限
    DATA_SCOPE_DEPT_AND_CHILD = '4'  # 本部门及以下数据权限
    DATA_SCOPE_SELF = '5'  # 仅本人数据权限
    
    @classmethod
    def is_super_admin(cls, role_keys: list) -> bool:
        """判断是否为超级管理员"""
        return cls.SUPER_ADMIN in role_keys
    
    @classmethod
    def is_project_manager(cls, role_keys: list) -> bool:
        """判断是否为项目管理员"""
        return cls.PROJECT_MANAGER in role_keys
    
    @classmethod
    def is_project_member(cls, role_keys: list) -> bool:
        """判断是否为项目成员"""
        return cls.PROJECT_MEMBER in role_keys
    
    @classmethod
    def is_common_user(cls, role_keys: list) -> bool:
        """判断是否为普通用户"""
        return cls.COMMON_USER in role_keys
    
    @classmethod
    def get_highest_role_level(cls, role_keys: list) -> int:
        """获取用户的最高权限级别"""
        if not role_keys:
            return 999  # 无角色时返回最低权限
        
        levels = [cls.ROLE_LEVELS.get(role_key, 999) for role_key in role_keys]
        return min(levels)
    
    @classmethod
    def can_manage_projects(cls, role_keys: list) -> bool:
        """判断是否可以管理项目"""
        return cls.is_super_admin(role_keys) or cls.is_project_manager(role_keys)
    
    @classmethod
    def can_access_database(cls, role_keys: list) -> bool:
        """判断是否可以访问数据库管理"""
        return cls.is_super_admin(role_keys) or cls.is_project_manager(role_keys)
    
    @classmethod
    def can_manage_users(cls, role_keys: list) -> bool:
        """判断是否可以管理用户"""
        return cls.is_super_admin(role_keys)
    
    @classmethod
    def can_view_all_data(cls, role_keys: list) -> bool:
        """判断是否可以查看所有数据"""
        return cls.is_super_admin(role_keys)
