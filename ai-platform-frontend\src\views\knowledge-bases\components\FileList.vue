<template>
  <div class="file-list">
    <!-- 检查是否有文件 -->
    <div v-if="!showGrouped && files.length === 0" class="empty-state">
      <el-empty description="暂无文件" />
    </div>
    <div v-else-if="showGrouped && Object.keys(groupedFiles).length === 0" class="empty-state">
      <el-empty description="暂无文件" />
    </div>

    <!-- 普通文件列表显示 -->
    <div v-else-if="!showGrouped" class="files-container">
      <div
        v-for="file in files"
        :key="file.fileId || file.file_id"
        class="file-item"
      >
        <div class="file-info">
          <div class="file-icon">
            <el-icon :size="20">
              <component :is="getFileTypeIcon(file.fileType || file.file_type)" />
            </el-icon>
          </div>
          <div class="file-details">
            <div class="file-name">{{ file.originalName || file.original_name }}</div>
            <div class="file-meta">
              <span class="file-size">{{ formatFileSize(file.fileSize || file.file_size) }}</span>
              <span class="file-format">{{ file.fileFormat || file.file_format }}</span>
              <span class="file-date">{{ formatDate(file.createdAt || file.created_at) }}</span>
            </div>
          </div>
        </div>

        <div class="file-actions">
          <el-button
            size="small"
            type="primary"
            @click="previewFile(file)"
            :loading="previewLoading === (file.fileId || file.file_id)"
          >
            <el-icon><View /></el-icon>
            预览
          </el-button>
          <el-button
            size="small"
            type="primary"
            @click="$emit('download', file.fileId || file.file_id)"
            :loading="downloadingFile === (file.fileId || file.file_id)"
          >
            <el-icon><Download /></el-icon>
            下载
          </el-button>
          <el-button
            size="small"
            type="danger"
            @click="$emit('delete', file.fileId || file.file_id)"
            :loading="deletingFile === (file.fileId || file.file_id)"
          >
            <el-icon><Delete /></el-icon>
            删除
          </el-button>
        </div>
      </div>
    </div>

    <!-- 分组文件列表显示 -->
    <div v-else class="grouped-files-container">
      <div
        v-for="(taskFiles, taskKey) in groupedFiles"
        :key="taskKey"
        class="task-group"
      >
        <div class="task-group-header">
          <el-icon :size="16" class="task-icon">
            <Folder />
          </el-icon>
          <span class="task-title">{{ getTaskDisplayName(taskKey) }}</span>
          <span class="task-count">({{ taskFiles.length }}个文件)</span>
        </div>

        <div class="task-files">
          <div
            v-for="file in taskFiles"
            :key="file.fileId || file.file_id"
            class="file-item"
          >
            <div class="file-info">
              <div class="file-icon">
                <el-icon :size="20">
                  <component :is="getFileTypeIcon(file.fileType || file.file_type)" />
                </el-icon>
              </div>
              <div class="file-details">
                <div class="file-name">{{ file.originalName || file.original_name }}</div>
                <div class="file-meta">
                  <span class="file-size">{{ formatFileSize(file.fileSize || file.file_size) }}</span>
                  <span class="file-format">{{ file.fileFormat || file.file_format }}</span>
                  <span class="file-date">{{ formatDate(file.createdAt || file.created_at) }}</span>
                </div>
              </div>
            </div>

            <div class="file-actions">
              <el-button
                size="small"
                type="primary"
                @click="previewFile(file)"
                :loading="previewLoading === (file.fileId || file.file_id)"
              >
                <el-icon><View /></el-icon>
                预览
              </el-button>
              <el-button
                size="small"
                type="primary"
                @click="$emit('download', file.fileId || file.file_id)"
                :loading="downloadingFile === (file.fileId || file.file_id)"
              >
                <el-icon><Download /></el-icon>
                下载
              </el-button>
              <el-button
                size="small"
                type="danger"
                @click="$emit('delete', file.fileId || file.file_id)"
                :loading="deletingFile === (file.fileId || file.file_id)"
              >
                <el-icon><Delete /></el-icon>
                删除
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 文件预览对话框 -->
    <el-dialog
      v-model="previewDialogVisible"
      :title="`文件预览 - ${previewFileName}`"
      width="80%"
      :before-close="handlePreviewClose"
    >
      <div v-if="previewContent || is3DModelFile(previewFileName)" class="preview-content">
        <!-- 3D模型预览 -->
        <div v-if="is3DModelFile(previewFileName)" class="preview-3d-model">
          <div class="model-preview-toolbar">
            <el-button-group>
              <el-button size="small" @click="resetModelView">重置视角</el-button>
              <el-button size="small" @click="fitModelView">适应窗口</el-button>
              <el-button size="small" @click="toggleAutoRotate">
                {{ modelAutoRotate ? '停止旋转' : '自动旋转' }}
              </el-button>
            </el-button-group>
          </div>
          <div class="model-preview-viewer">
            <div 
              ref="modelPreviewContainer"
              id="modelPreviewMainContainer"
              class="model-preview-container"
            ></div>
          </div>
        </div>
        
        <!-- 图片预览 -->
        <div v-else-if="isImageFile(previewFileName)" class="preview-image">
          <el-image
            :src="previewContent"
            fit="contain"
            style="max-width: 100%; max-height: 500px;"
            :preview-src-list="[previewContent]"
            preview-teleported
          />
        </div>
        
        <!-- 文本预览 -->
        <div v-else-if="isTextFile(previewFileName)" class="preview-text">
          <pre>{{ previewContent }}</pre>
        </div>
        
        <!-- PDF预览 -->
        <div v-else-if="isPdfFile(previewFileName)" class="preview-pdf">
          <iframe
            :src="previewContent"
            width="100%"
            height="500px"
            frameborder="0"
          ></iframe>
        </div>
        
        <!-- Office文档预览 -->
        <div v-else-if="isOfficeFile(previewFileName)" class="preview-office">
          <iframe
            :src="previewContent"
            width="100%"
            height="500px"
            frameborder="0"
          ></iframe>
        </div>
      </div>
      
      <div v-else-if="previewLoading" class="preview-loading">
        <el-icon class="is-loading"><Loading /></el-icon>
        <span>正在加载文件内容...</span>
      </div>
      
      <div v-else class="preview-error">
        <el-icon><Warning /></el-icon>
        <span>{{ previewErrorMessage }}</span>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, markRaw, nextTick } from 'vue'
import { Document, Download, Delete, View, Loading, Warning, Folder, Files, DataAnalysis, Notebook, List, FolderOpened } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import ThreeModelViewer from '@/utils/threeModelViewer'

// 定义props
const props = defineProps({
  files: {
    type: Array,
    default: () => []
  },
  groupedFiles: {
    type: Object,
    default: () => ({})
  },
  showGrouped: {
    type: Boolean,
    default: false
  }
})

// 定义emits
const emit = defineEmits(['download', 'delete'])

// 响应式数据
const downloadingFile = ref(null)
const deletingFile = ref(null)
const previewLoading = ref(null)
const previewDialogVisible = ref(false)
const previewFileName = ref('')
const previewContent = ref('')
const previewErrorMessage = ref('')

// 3D模型相关
const modelPreviewContainer = ref(null)
const modelAutoRotate = ref(false)
const modelPreviewViewer = ref(null)

// 获取文件类型图标
const getFileTypeIcon = (fileType) => {
  const iconMap = {
    model: Files,
    report: Notebook,
    dataset: DataAnalysis,
    log: List,
    document: Document,
    archived: FolderOpened
  }
  return iconMap[fileType] || Document
}

// 获取任务显示名称
const getTaskDisplayName = (taskKey) => {
  if (taskKey === 'no_task') {
    return '未关联任务'
  }
  return taskKey.replace('task_', '任务 ')
}

// 判断文件类型
const is3DModelFile = (fileName) => {
  const modelExtensions = ['.gltf', '.glb']
  const extension = fileName.toLowerCase().substring(fileName.lastIndexOf('.'))
  return modelExtensions.includes(extension)
}

const isImageFile = (fileName) => {
  const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp']
  const extension = fileName.toLowerCase().substring(fileName.lastIndexOf('.'))
  return imageExtensions.includes(extension)
}

const isTextFile = (fileName) => {
  const textExtensions = ['.txt', '.log', '.md', '.json', '.xml', '.csv']
  const extension = fileName.toLowerCase().substring(fileName.lastIndexOf('.'))
  return textExtensions.includes(extension)
}

const isPdfFile = (fileName) => {
  return fileName.toLowerCase().endsWith('.pdf')
}

const isOfficeFile = (fileName) => {
  const officeExtensions = ['.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx']
  const extension = fileName.toLowerCase().substring(fileName.lastIndexOf('.'))
  return officeExtensions.includes(extension)
}

const isSupportedFile = (fileName) => {
  return is3DModelFile(fileName) || 
         isImageFile(fileName) || 
         isTextFile(fileName) || 
         isPdfFile(fileName) || 
         isOfficeFile(fileName)
}

// 获取文件URL
const getFileUrl = (filePath) => {
  if (!filePath) return ''
  return `/dev-api/business/files/public/download?path=${encodeURIComponent(filePath)}`
}

// 下载3D模型文件数据
const downloadModelFile = async (filePath) => {
  try {
    console.log('🌐 开始下载3D模型文件:', filePath)
    
    const url = `/dev-api/business/files/public/download?path=${encodeURIComponent(filePath)}`
    const response = await fetch(url)
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }
    
    const arrayBuffer = await response.arrayBuffer()
    const uint8Array = new Uint8Array(arrayBuffer)
    
    console.log('✅ 3D模型文件下载完成:', {
      url: url,
      size: uint8Array.length
    })
    
    return uint8Array
    
  } catch (error) {
    console.error('❌ 下载3D模型文件失败:', error)
    throw new Error(`下载模型文件失败: ${error.message}`)
  }
}

// 预览文件
const previewFile = async (file) => {
  const fileName = file.originalName || file.original_name
  const filePath = file.storagePath || file.storage_path
  
  console.log('🔍 开始预览文件:', fileName)
  
  if (!isSupportedFile(fileName)) {
    previewDialogVisible.value = true
    previewFileName.value = fileName
    previewContent.value = ''
    previewErrorMessage.value = '暂不支持该格式文件在线查看，请下载至本地电脑打开'
    return
  }
  
  previewDialogVisible.value = true
  previewFileName.value = fileName
  previewContent.value = ''
  previewErrorMessage.value = ''
  previewLoading.value = file.fileId || file.file_id
  
  // 为3D模型设置占位符内容，确保容器能够渲染
  if (is3DModelFile(fileName)) {
    previewContent.value = '3d_model_placeholder'
  }
  
  try {
    if (is3DModelFile(fileName)) {
      // 3D模型文件
      console.log('🔍 预览3D模型文件:', fileName)
      const modelFileData = await downloadModelFile(filePath)
      
      // 等待对话框渲染完成
      await new Promise(resolve => setTimeout(resolve, 500))
      
      // 确保对话框完全显示
      await nextTick()
      
      console.log('🔍 查找3D模型容器...')
      let container = modelPreviewContainer.value
      console.log('🔍 通过ref获取容器:', !!container)
      
      if (!container) {
        // 备选方案：通过ID获取容器
        container = document.getElementById('modelPreviewMainContainer')
        console.log('🔄 通过ID获取容器:', !!container)
      }
      
      // 如果仍然没有找到容器，等待一下再试
      if (!container) {
        console.log('⏳ 等待容器渲染...')
        await new Promise(resolve => setTimeout(resolve, 200))
        container = modelPreviewContainer.value || document.getElementById('modelPreviewMainContainer')
        console.log('🔄 重试获取容器:', !!container)
      }
      
      if (container) {
        console.log('🎯 3D模型容器详细信息:', {
          element: container,
          id: container.id,
          clientWidth: container.clientWidth,
          clientHeight: container.clientHeight,
          offsetWidth: container.offsetWidth,
          offsetHeight: container.offsetHeight,
          style: container.style.cssText,
          visible: container.offsetWidth > 0 && container.offsetHeight > 0,
          parentElement: container.parentElement?.tagName
        })
        
              console.log('🔧 开始创建Three.js查看器...')
      try {
        modelPreviewViewer.value = markRaw(new ThreeModelViewer(container))
        console.log('✅ ThreeModelViewer实例创建成功')
        
        console.log('🔧 开始加载3D模型...')
        await modelPreviewViewer.value.loadModel(modelFileData, fileName)
        console.log('✅ 3D模型预览加载成功')
        
        // 检查模型是否成功加载
        const modelInfo = modelPreviewViewer.value.getModelInfo()
        console.log('📊 模型信息:', modelInfo)
        
      } catch (error) {
        console.error('❌ 3D模型加载失败:', error)
        throw error
      }
        
      } else {
        console.log('❌ 无法获取3D模型容器元素')
        console.log('🔍 对话框状态:', {
          dialogVisible: previewDialogVisible.value,
          fileName: previewFileName.value,
          loading: previewLoading.value
        })
        
        // 查找所有可能的容器元素
        const allContainers = document.querySelectorAll('[id*="model"]')
        console.log('🔍 页面所有model相关元素:', Array.from(allContainers).map(c => ({
          id: c.id,
          className: c.className,
          tagName: c.tagName,
          visible: c.offsetWidth > 0 && c.offsetHeight > 0
        })))
        
        throw new Error('无法获取3D模型容器元素')
      }
      
    } else if (isImageFile(fileName)) {
      // 图片文件
      previewContent.value = getFileUrl(filePath)
      
    } else if (isTextFile(fileName)) {
      // 文本文件
      const response = await fetch(getFileUrl(filePath))
      if (response.ok) {
        const text = await response.text()
        previewContent.value = text
      } else {
        throw new Error('文件加载失败')
      }
      
    } else if (isPdfFile(fileName)) {
      // PDF文件
      previewContent.value = getFileUrl(filePath)
      
    } else if (isOfficeFile(fileName)) {
      // Office文件
      previewContent.value = getFileUrl(filePath)
    }
    
  } catch (error) {
    console.error('❌ 预览文件失败:', error)
    previewErrorMessage.value = `预览文件失败: ${error.message}`
  } finally {
    previewLoading.value = null
  }
}

// 关闭预览对话框
const handlePreviewClose = () => {
  if (modelPreviewViewer.value) {
    modelPreviewViewer.value.destroy()
    modelPreviewViewer.value = null
  }
  previewDialogVisible.value = false
  previewFileName.value = ''
  previewContent.value = ''
  previewErrorMessage.value = ''
  modelAutoRotate.value = false
}

// 3D模型控制函数
const resetModelView = async () => {
  if (modelPreviewViewer.value) {
    await modelPreviewViewer.value.resetView()
  }
}

const fitModelView = async () => {
  if (modelPreviewViewer.value) {
    await modelPreviewViewer.value.fitView()
  }
}

const toggleAutoRotate = async () => {
  if (modelPreviewViewer.value) {
    modelAutoRotate.value = await modelPreviewViewer.value.toggleAutoRotate()
  }
}

// 格式化文件大小
const formatFileSize = (size) => {
  if (!size) return '未知'
  
  if (size < 1024) {
    return `${size} KB`
  } else if (size < 1024 * 1024) {
    return `${(size / 1024).toFixed(1)} MB`
  } else {
    return `${(size / (1024 * 1024)).toFixed(1)} GB`
  }
}

// 格式化日期
const formatDate = (dateStr) => {
  if (!dateStr) return '未知'
  return new Date(dateStr).toLocaleDateString('zh-CN')
}
</script>

<style scoped>
.file-list {
  min-height: 200px;
}

.empty-state {
  padding: 40px 0;
}

.files-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
  max-height: 400px;
  overflow-y: auto;
  padding-right: 8px;
}

.files-container::-webkit-scrollbar {
  width: 6px;
}

.files-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.files-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.files-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 分组文件列表样式 */
.grouped-files-container {
  max-height: 400px;
  overflow-y: auto;
  padding-right: 8px;
}

.grouped-files-container::-webkit-scrollbar {
  width: 6px;
}

.grouped-files-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.grouped-files-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.grouped-files-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.task-group {
  margin-bottom: 20px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  overflow: hidden;
}

.task-group-header {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  background-color: #f8f9fa;
  border-bottom: 1px solid #e4e7ed;
  font-weight: 500;
  color: #303133;
}

.task-icon {
  margin-right: 8px;
  color: #409eff;
}

.task-title {
  flex: 1;
  font-size: 14px;
}

.task-count {
  font-size: 12px;
  color: #909399;
  font-weight: normal;
}

.task-files {
  padding: 8px;
}

.task-files .file-item {
  margin-bottom: 8px;
}

.task-files .file-item:last-child {
  margin-bottom: 0;
}

.file-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  background-color: #fafafa;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.file-item:hover {
  background-color: #f5f7fa;
  border-color: #c0c4cc;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.file-info {
  display: flex;
  align-items: center;
  flex: 1;
  min-width: 0;
}

.file-icon {
  margin-right: 12px;
  color: #409eff;
}

.file-details {
  flex: 1;
  min-width: 0;
}

.file-name {
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.file-meta {
  display: flex;
  gap: 12px;
  font-size: 12px;
  color: #909399;
}

.file-size,
.file-format,
.file-date {
  white-space: nowrap;
}

.file-actions {
  display: flex;
  gap: 8px;
  margin-left: 16px;
}

.file-actions .el-button {
  display: flex;
  align-items: center;
  gap: 4px;
}

/* 预览对话框样式 */
.preview-content {
  max-height: 80vh;
  overflow-y: auto;
}

.preview-3d-model {
  height: 70vh;
  min-height: 500px;
  display: flex;
  flex-direction: column;
}

.model-preview-toolbar {
  padding: 10px 0;
  border-bottom: 1px solid #e4e7ed;
  margin-bottom: 15px;
}

.model-preview-viewer {
  flex: 1;
  position: relative;
  background-color: #f0f0f0;
  border-radius: 4px;
  overflow: hidden;
}

.model-preview-container {
  width: 100% !important;
  height: 100% !important;
  min-height: 400px !important;
  display: block !important;
  background-color: #f0f0f0 !important;
  border-radius: 4px;
  position: relative;
  overflow: hidden;
}

.model-preview-container canvas {
  width: 100% !important;
  height: 100% !important;
  display: block !important;
  border-radius: 4px;
  background-color: #f0f0f0 !important;
}

.preview-image {
  text-align: center;
}

.preview-text {
  background-color: #f5f5f5;
  padding: 15px;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 14px;
  line-height: 1.5;
  white-space: pre-wrap;
  word-break: break-all;
  max-height: 70vh;
  overflow-y: auto;
  border: 1px solid #e4e7ed;
}

.preview-pdf,
.preview-office {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
}

.preview-loading,
.preview-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #909399;
}

.preview-loading .el-icon,
.preview-error .el-icon {
  font-size: 32px;
  margin-bottom: 10px;
}

.preview-error .el-icon {
  color: #f56c6c;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .file-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .file-actions {
    margin-left: 0;
    width: 100%;
    justify-content: flex-end;
  }
  
  .file-meta {
    flex-direction: column;
    gap: 4px;
  }
}
</style> 