-- ----------------------------
-- 新增角色定义
-- ----------------------------

-- 1. 超级管理员角色（如果不存在的话）
INSERT INTO sys_role (role_name, role_key, role_sort, data_scope, menu_check_strictly, dept_check_strictly, status, del_flag, create_by, create_time, update_by, update_time, remark)
SELECT '超级管理员', 'super_admin', 1, '1', 1, 1, '0', '0', 'admin', CURRENT_TIMESTAMP, '', NULL, '超级管理员角色，拥有所有权限'
WHERE NOT EXISTS (SELECT 1 FROM sys_role WHERE role_key = 'super_admin');

-- 2. 项目管理员角色
INSERT INTO sys_role (role_name, role_key, role_sort, data_scope, menu_check_strictly, dept_check_strictly, status, del_flag, create_by, create_time, update_by, update_time, remark)
SELECT '项目管理员', 'project_manager', 2, '5', 1, 1, '0', '0', 'admin', CURRENT_TIMESTAMP, '', NULL, '项目管理员角色，管理负责的项目和项目成员'
WHERE NOT EXISTS (SELECT 1 FROM sys_role WHERE role_key = 'project_manager');

-- 3. 项目成员角色
INSERT INTO sys_role (role_name, role_key, role_sort, data_scope, menu_check_strictly, dept_check_strictly, status, del_flag, create_by, create_time, update_by, update_time, remark)
SELECT '项目成员', 'project_member', 3, '5', 1, 1, '0', '0', 'admin', CURRENT_TIMESTAMP, '', NULL, '项目成员角色，可以查看所属项目的任务和数据'
WHERE NOT EXISTS (SELECT 1 FROM sys_role WHERE role_key = 'project_member');

-- 4. 普通用户角色
INSERT INTO sys_role (role_name, role_key, role_sort, data_scope, menu_check_strictly, dept_check_strictly, status, del_flag, create_by, create_time, update_by, update_time, remark)
SELECT '普通用户', 'common_user', 4, '5', 1, 1, '0', '0', 'admin', CURRENT_TIMESTAMP, '', NULL, '普通用户角色，只能访问首页和工具广场'
WHERE NOT EXISTS (SELECT 1 FROM sys_role WHERE role_key = 'common_user');

-- ----------------------------
-- 新增菜单权限定义
-- ----------------------------

-- 首页菜单（所有角色都可以访问）
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
SELECT '首页', 0, 1, '/dashboard', 'dashboard/index', 1, 0, 'C', '0', '0', 'dashboard:view', 'dashboard', 'admin', CURRENT_TIMESTAMP, '', NULL, '系统首页'
WHERE NOT EXISTS (SELECT 1 FROM sys_menu WHERE path = '/dashboard');

-- 工具广场菜单（所有角色都可以访问）
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
SELECT '工具广场', 0, 2, '/tools', 'tools/index', 1, 0, 'C', '0', '0', 'tools:view', 'tool', 'admin', CURRENT_TIMESTAMP, '', NULL, '工具广场页面'
WHERE NOT EXISTS (SELECT 1 FROM sys_menu WHERE path = '/tools');

-- 项目管理菜单（项目管理员和超级管理员可以访问）
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
SELECT '项目管理', 0, 3, '/projects', 'projects/index', 1, 0, 'C', '0', '0', 'projects:view', 'project', 'admin', CURRENT_TIMESTAMP, '', NULL, '项目管理页面'
WHERE NOT EXISTS (SELECT 1 FROM sys_menu WHERE path = '/projects');

-- 任务管理菜单（项目成员、项目管理员和超级管理员可以访问）
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
SELECT '任务管理', 0, 4, '/tasks', 'tasks/index', 1, 0, 'C', '0', '0', 'tasks:view', 'task', 'admin', CURRENT_TIMESTAMP, '', NULL, '任务管理页面'
WHERE NOT EXISTS (SELECT 1 FROM sys_menu WHERE path = '/tasks');

-- 数据库管理菜单（只有超级管理员和项目管理员可以访问）
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
SELECT '数据库管理', 0, 5, '/database', 'database/index', 1, 0, 'C', '0', '0', 'database:view', 'database', 'admin', CURRENT_TIMESTAMP, '', NULL, '数据库管理页面'
WHERE NOT EXISTS (SELECT 1 FROM sys_menu WHERE path = '/database');

-- 系统管理菜单（只有超级管理员可以访问）
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
SELECT '系统管理', 0, 6, '/system', 'system/index', 1, 0, 'M', '0', '0', 'system:view', 'system', 'admin', CURRENT_TIMESTAMP, '', NULL, '系统管理目录'
WHERE NOT EXISTS (SELECT 1 FROM sys_menu WHERE path = '/system');

-- ----------------------------
-- 角色菜单关联关系
-- ----------------------------

-- 获取角色ID和菜单ID
-- 超级管理员角色ID
SET @super_admin_role_id = (SELECT role_id FROM sys_role WHERE role_key = 'super_admin');
-- 项目管理员角色ID
SET @project_manager_role_id = (SELECT role_id FROM sys_role WHERE role_key = 'project_manager');
-- 项目成员角色ID
SET @project_member_role_id = (SELECT role_id FROM sys_role WHERE role_key = 'project_member');
-- 普通用户角色ID
SET @common_user_role_id = (SELECT role_id FROM sys_role WHERE role_key = 'common_user');

-- 菜单ID
SET @dashboard_menu_id = (SELECT menu_id FROM sys_menu WHERE path = '/dashboard');
SET @tools_menu_id = (SELECT menu_id FROM sys_menu WHERE path = '/tools');
SET @projects_menu_id = (SELECT menu_id FROM sys_menu WHERE path = '/projects');
SET @tasks_menu_id = (SELECT menu_id FROM sys_menu WHERE path = '/tasks');
SET @database_menu_id = (SELECT menu_id FROM sys_menu WHERE path = '/database');
SET @system_menu_id = (SELECT menu_id FROM sys_menu WHERE path = '/system');

-- 超级管理员：所有菜单权限
INSERT IGNORE INTO sys_role_menu (role_id, menu_id) VALUES 
(@super_admin_role_id, @dashboard_menu_id),
(@super_admin_role_id, @tools_menu_id),
(@super_admin_role_id, @projects_menu_id),
(@super_admin_role_id, @tasks_menu_id),
(@super_admin_role_id, @database_menu_id),
(@super_admin_role_id, @system_menu_id);

-- 项目管理员：首页、工具广场、项目管理、任务管理、数据库管理
INSERT IGNORE INTO sys_role_menu (role_id, menu_id) VALUES 
(@project_manager_role_id, @dashboard_menu_id),
(@project_manager_role_id, @tools_menu_id),
(@project_manager_role_id, @projects_menu_id),
(@project_manager_role_id, @tasks_menu_id),
(@project_manager_role_id, @database_menu_id);

-- 项目成员：首页、工具广场、任务管理
INSERT IGNORE INTO sys_role_menu (role_id, menu_id) VALUES 
(@project_member_role_id, @dashboard_menu_id),
(@project_member_role_id, @tools_menu_id),
(@project_member_role_id, @tasks_menu_id);

-- 普通用户：首页、工具广场
INSERT IGNORE INTO sys_role_menu (role_id, menu_id) VALUES 
(@common_user_role_id, @dashboard_menu_id),
(@common_user_role_id, @tools_menu_id);
