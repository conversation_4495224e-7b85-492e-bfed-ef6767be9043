from fastapi import APIRouter, Depends, Request, Body
from sqlalchemy.ext.asyncio import AsyncSession
from config.get_db import get_db
from module_scheduler.service.task_scheduler_service import TaskSchedulerService
from module_business.controller.task_log_controller import push_task_log
from utils.log_util import logger
from utils.response_util import ResponseUtil
from typing import Dict, Any
from datetime import datetime


taskCallbackController = APIRouter(prefix='/business/tasks/callback')


@taskCallbackController.post('/{task_id}')
async def receive_task_callback(
    request: Request,
    task_id: int,
    callback_data: Dict[str, Any] = Body(...),
    query_db: AsyncSession = Depends(get_db),
):
    """
    接收外部服务的任务完成回调
    
    Args:
        task_id: 任务ID
        callback_data: 回调数据，格式如：
        {
            "task_id": 123,
            "status": "completed|failed|running|pending",
            "progress": 85,
            "message": "任务执行进度信息",
            "result": {
                "output_files": {
                    "model_file": "/path/to/model.step",
                    "report_file": "/path/to/report.pdf"
                },
                "design_result": {
                    "parameters": {...},
                    "metrics": {...}
                }
            },
            "error_message": "错误信息（如果失败）",
            "timestamp": "2024-01-01T12:00:00Z"
        }
        
    Returns:
        处理结果
    """
    try:
        logger.info(f"收到任务 {task_id} 的回调请求: {callback_data}")
        
        # 验证回调数据
        if not callback_data:
            logger.error(f"任务 {task_id} 回调数据为空")
            return ResponseUtil.failure(msg="回调数据不能为空")
        
        # 获取Redis客户端
        redis_client = request.app.state.redis

        # 创建可靠回调处理器实例
        from module_scheduler.service.reliable_callback_handler import ReliableCallbackHandler
        callback_handler = ReliableCallbackHandler(redis_client, query_db)

        # 验证回调有效性
        if not await callback_handler.validate_callback(task_id):
            logger.warning(f"任务 {task_id} 回调验证失败")
            return ResponseUtil.failure(msg="回调验证失败")

        # 处理不同类型的回调
        status = callback_data.get('status', 'unknown')

        if status in ['completed', 'failed']:
            success = await callback_handler.handle_task_completion_callback(task_id, callback_data)
        elif status in ['running', 'processing']:
            success = await callback_handler.handle_progress_callback(task_id, callback_data)
        else:
            # 其他状态回调
            success = await callback_handler.handle_status_callback(task_id, callback_data)
        
        if success:
            logger.info(f"任务 {task_id} 回调处理成功")
            
            # 推送日志到前端
            try:
                await push_task_log(task_id, f"任务状态更新: {status}", "INFO")
            except Exception as log_error:
                logger.warning(f"推送任务日志失败: {log_error}")
            
            return ResponseUtil.success(
                data={
                    "task_id": task_id,
                    "status": status,
                    "received_at": datetime.now().isoformat(),
                    "processed": True
                },
                msg="回调处理成功"
            )
        else:
            logger.error(f"任务 {task_id} 回调处理失败")
            return ResponseUtil.failure(msg="回调处理失败")
            
    except Exception as e:
        logger.error(f"处理任务回调失败，任务 {task_id}: {e}")
        return ResponseUtil.failure(msg=f"回调处理失败: {str(e)}")


async def handle_task_completed(scheduler_service: TaskSchedulerService, task_id: int, callback_data: Dict[str, Any]) -> bool:
    """
    处理任务完成回调
    
    Args:
        scheduler_service: 调度器服务实例
        task_id: 任务ID
        callback_data: 回调数据
        
    Returns:
        处理是否成功
    """
    try:
        logger.info(f"任务 {task_id} 已完成")
        
        result = callback_data.get('result', {})
        output_files = result.get('output_files', {})
        design_result = result.get('design_result', {})
        
        # 处理回调
        success = await scheduler_service.handle_task_callback(task_id, callback_data)
        
        if success:
            # 推送完成通知
            await push_task_log(task_id, "任务执行完成", "INFO")
            
            # 如果有设计结果，记录详细信息
            if design_result:
                await push_task_log(task_id, f"设计结果: {design_result}", "INFO")
            
            # 如果有输出文件，记录文件信息
            if output_files:
                file_count = len(output_files)
                await push_task_log(task_id, f"生成了 {file_count} 个输出文件", "INFO")
        
        return success
        
    except Exception as e:
        logger.error(f"处理任务完成回调失败，任务 {task_id}: {e}")
        return False


async def handle_task_failed(scheduler_service: TaskSchedulerService, task_id: int, callback_data: Dict[str, Any]) -> bool:
    """
    处理任务失败回调
    
    Args:
        scheduler_service: 调度器服务实例
        task_id: 任务ID
        callback_data: 回调数据
        
    Returns:
        处理是否成功
    """
    try:
        error_message = callback_data.get('error_message', '未知错误')
        logger.error(f"任务 {task_id} 失败: {error_message}")
        
        # 处理回调
        success = await scheduler_service.handle_task_callback(task_id, callback_data)
        
        if success:
            # 推送失败通知
            await push_task_log(task_id, f"任务执行失败: {error_message}", "ERROR")
        
        return success
        
    except Exception as e:
        logger.error(f"处理任务失败回调失败，任务 {task_id}: {e}")
        return False


async def handle_progress_update(scheduler_service: TaskSchedulerService, task_id: int, callback_data: Dict[str, Any]) -> bool:
    """
    处理进度更新回调

    Args:
        scheduler_service: 调度器服务实例
        task_id: 任务ID
        callback_data: 回调数据

    Returns:
        处理是否成功
    """
    try:
        progress = callback_data.get('progress', 0)
        message = callback_data.get('message', '')
        stage = callback_data.get('stage', '')

        logger.info(f"任务 {task_id} 进度更新: {progress}% - {message}")

        # 构建进度数据
        progress_data = {
            "progress": progress,
            "message": message,
            "stage": stage
        }

        # 处理进度回调（已在scheduler_service中推送日志，无需重复推送）
        success = await scheduler_service.handle_progress_callback(task_id, progress_data)

        return success
        
    except Exception as e:
        logger.error(f"处理进度更新回调失败，任务 {task_id}: {e}")
        return False


@taskCallbackController.post('/batch')
async def receive_batch_task_callback(
    request: Request,
    batch_data: Dict[str, Any] = Body(...),
    query_db: AsyncSession = Depends(get_db),
):
    """
    接收批量任务回调
    
    Args:
        batch_data: 批量回调数据，格式如：
        {
            "tasks": [
                {
                    "task_id": 123,
                    "status": "completed",
                    "progress": 100,
                    "message": "任务完成",
                    "result": {...},
                    "error_message": ""
                },
                {
                    "task_id": 124,
                    "status": "failed",
                    "error_message": "执行失败"
                }
            ]
        }
        
    Returns:
        处理结果
    """
    try:
        logger.info(f"收到批量任务回调请求: {batch_data}")
        
        # 获取Redis客户端
        redis_client = request.app.state.redis
        
        # 创建调度器服务实例
        scheduler_service = TaskSchedulerService(redis_client, query_db)
        
        tasks = batch_data.get('tasks', [])
        success_count = 0
        failed_count = 0
        results = []
        
        for task_callback in tasks:
            task_id = task_callback.get('task_id')
            if task_id:
                try:
                    # 处理不同类型的回调
                    status = task_callback.get('status', 'unknown')
                    
                    if status == 'completed':
                        success = await handle_task_completed(scheduler_service, task_id, task_callback)
                    elif status == 'failed':
                        success = await handle_task_failed(scheduler_service, task_id, task_callback)
                    elif status in ['running', 'pending']:
                        success = await handle_progress_update(scheduler_service, task_id, task_callback)
                    else:
                        success = await scheduler_service.handle_task_callback(task_id, task_callback)
                    
                    if success:
                        success_count += 1
                        results.append({
                            "task_id": task_id,
                            "status": "success",
                            "message": "处理成功"
                        })
                    else:
                        failed_count += 1
                        results.append({
                            "task_id": task_id,
                            "status": "failed",
                            "message": "处理失败"
                        })
                        
                except Exception as e:
                    logger.error(f"处理任务 {task_id} 回调失败: {e}")
                    failed_count += 1
                    results.append({
                        "task_id": task_id,
                        "status": "error",
                        "message": f"处理异常: {str(e)}"
                    })
            else:
                logger.warning("回调数据缺少task_id")
                failed_count += 1
                results.append({
                    "task_id": None,
                    "status": "error",
                    "message": "缺少task_id"
                })
        
        logger.info(f"批量回调处理完成: 成功 {success_count} 个，失败 {failed_count} 个")
        
        return ResponseUtil.success(data={
            "total": len(tasks),
            "success_count": success_count,
            "failed_count": failed_count,
            "results": results,
            "processed_at": datetime.now().isoformat()
        }, msg="批量回调处理完成")
        
    except Exception as e:
        logger.error(f"处理批量任务回调失败: {e}")
        return ResponseUtil.failure(msg=f"批量回调处理失败: {str(e)}") 


@taskCallbackController.post('/progress/{task_id}')
async def handle_progress_callback(
    request: Request,
    task_id: int,
    progress_data: dict = Body(...),
    query_db: AsyncSession = Depends(get_db),
):
    """
    处理任务进度回调
    
    Args:
        task_id: 任务ID
        progress_data: 进度数据，格式如：
        {
            "progress": 50,  # 进度值 (0-100)
            "message": "正在处理第3步",  # 进度消息
            "stage": "processing",  # 当前阶段
            "details": {  # 详细信息（可选）
                "current_step": 3,
                "total_steps": 5,
                "estimated_time": "2分钟"
            }
        }
    """
    try:
        from module_scheduler.service.scheduler_manager import scheduler_manager
        
        logger.info(f"收到任务 {task_id} 的进度回调: {progress_data}")
        
        # 处理进度回调
        success = await scheduler_manager.scheduler_service.handle_progress_callback(task_id, progress_data)

        if success:
            # 不在这里推送进度日志，避免重复（已在 scheduler_service 中推送）
            progress = progress_data.get('progress', 0)
            stage = progress_data.get('stage', '')

            return ResponseUtil.success(data={
                "task_id": task_id,
                "progress": progress,
                "stage": stage,
                "processed_at": datetime.now().isoformat()
            }, msg="进度更新成功")
        else:
            return ResponseUtil.failure(msg="进度更新失败")
            
    except Exception as e:
        logger.error(f"处理进度回调失败: {e}")
        return ResponseUtil.failure(msg=f"处理进度回调失败: {str(e)}")


@taskCallbackController.post('/status/{task_id}')
async def handle_status_callback(
    request: Request,
    task_id: int,
    status_data: dict = Body(...),
    query_db: AsyncSession = Depends(get_db),
):
    """
    处理任务状态回调
    
    Args:
        task_id: 任务ID
        status_data: 状态数据，格式如：
        {
            "status": "running|completed|failed|paused|cancelled",
            "message": "状态描述",
            "timestamp": "2024-01-01T12:00:00Z",
            "metadata": {
                "reason": "暂停原因",
                "user_id": 123
            }
        }
    """
    try:
        logger.info(f"收到任务 {task_id} 的状态回调: {status_data}")
        
        # 获取Redis客户端
        redis_client = request.app.state.redis
        
        # 创建调度器服务实例
        scheduler_service = TaskSchedulerService(redis_client, query_db)
        
        # 处理状态回调
        success = await scheduler_service.handle_task_callback(task_id, status_data)
        
        if success:
            status = status_data.get('status', 'unknown')
            message = status_data.get('message', '')
            
            # 推送状态日志
            await push_task_log(task_id, f"状态更新: {status} - {message}", "INFO")
            
            return ResponseUtil.success(data={
                "task_id": task_id,
                "status": status,
                "processed_at": datetime.now().isoformat()
            }, msg="状态更新成功")
        else:
            return ResponseUtil.failure(msg="状态更新失败")
            
    except Exception as e:
        logger.error(f"处理状态回调失败: {e}")
        return ResponseUtil.failure(msg=f"处理状态回调失败: {str(e)}")


@taskCallbackController.get('/health')
async def callback_health_check():
    """
    回调接口健康检查
    
    Returns:
        健康状态
    """
    return ResponseUtil.success(data={
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "service": "task_callback_service"
    }, msg="回调服务运行正常") 