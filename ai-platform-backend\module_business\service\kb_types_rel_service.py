from sqlalchemy.ext.asyncio import AsyncSession
from typing import List
from config.constant import CommonConstant
from exceptions.exception import ServiceException
from module_admin.entity.vo.common_vo import CrudResponseModel
from module_business.dao.kb_types_rel_dao import KbTypesRelDao
from module_business.entity.vo.kb_types_rel_vo import DeleteKbTypesRelModel, KbTypesRelModel, KbTypesRelPageQueryModel
from module_business.entity.vo.knowledge_bases_vo import KnowledgeBasesModel
from utils.common_util import CamelCaseUtil
from utils.excel_util import ExcelUtil


class KbTypesRelService:
    """
    数据库-类型关联关系模块服务层
    """

    @classmethod
    async def get_kb_types_rel_list_services(
        cls, query_db: AsyncSession, query_object: KbTypesRelPageQueryModel, is_page: bool = False
    ):
        """
        获取数据库-类型关联关系列表信息service

        :param query_db: orm对象
        :param query_object: 查询参数对象
        :param is_page: 是否开启分页
        :return: 数据库-类型关联关系列表信息对象
        """
        kb_types_rel_list_result = await KbTypesRelDao.get_kb_types_rel_list(query_db, query_object, is_page)

        return kb_types_rel_list_result


    @classmethod
    async def add_kb_types_rel_services(cls, query_db: AsyncSession, page_object: KbTypesRelModel):
        """
        新增数据库-类型关联关系信息service

        :param query_db: orm对象
        :param page_object: 新增数据库-类型关联关系对象
        :return: 新增数据库-类型关联关系校验结果
        """
        # 检查关联关系是否已存在
        existing_rel = await KbTypesRelDao.get_kb_types_rel_by_kb_id_and_type_id(
            query_db, page_object.kb_id, page_object.type_id
        )
        if existing_rel:
            return CrudResponseModel(is_success=True, message="关联关系已存在")

        try:
            await KbTypesRelDao.add_kb_types_rel_dao(query_db, page_object)
            await query_db.commit()
            return CrudResponseModel(is_success=True, message='新增成功')
        except Exception as e:
            await query_db.rollback()
            raise e

    @classmethod
    async def edit_kb_types_rel_services(cls, query_db: AsyncSession, page_object: KbTypesRelModel):
        """
        编辑数据库-类型关联关系信息service

        :param query_db: orm对象
        :param page_object: 编辑数据库-类型关联关系对象
        :return: 编辑数据库-类型关联关系校验结果
        """
        edit_kb_types_rel = page_object.model_dump(exclude_unset=True, exclude={})
        kb_types_rel_info = await cls.kb_types_rel_detail_services(query_db, page_object.kb_id)
        if kb_types_rel_info.kb_id:
            try:
                await KbTypesRelDao.edit_kb_types_rel_dao(query_db, edit_kb_types_rel)
                await query_db.commit()
                return CrudResponseModel(is_success=True, message='更新成功')
            except Exception as e:
                await query_db.rollback()
                raise e
        else:
            raise ServiceException(message='数据库-类型关联关系不存在')

    @classmethod
    async def delete_kb_types_rel_services(cls, query_db: AsyncSession, page_object: DeleteKbTypesRelModel):
        """
        删除数据库-类型关联关系信息service

        :param query_db: orm对象
        :param page_object: 删除数据库-类型关联关系对象
        :return: 删除数据库-类型关联关系校验结果
        """
        if page_object.kb_ids:
            kb_id_list = page_object.kb_ids.split(',')
            try:
                for kb_id in kb_id_list:
                    await KbTypesRelDao.delete_kb_types_rel_dao(query_db, KbTypesRelModel(kbId=kb_id))
                await query_db.commit()
                return CrudResponseModel(is_success=True, message='删除成功')
            except Exception as e:
                await query_db.rollback()
                raise e
        else:
            raise ServiceException(message='传入数据库id为空')

    @classmethod
    async def delete_kb_type_rel_by_kb_and_type_services(cls, query_db: AsyncSession, kb_id: int, type_id: int):
        """
        根据数据库ID和类型ID删除特定的数据库-类型关联关系service

        :param query_db: orm对象
        :param kb_id: 数据库ID
        :param type_id: 类型ID
        :return: 删除结果
        """
        try:
            await KbTypesRelDao.delete_kb_type_rel_by_kb_and_type(query_db, kb_id, type_id)
            await query_db.commit()
            return CrudResponseModel(is_success=True, message='删除成功')
        except Exception as e:
            await query_db.rollback()
            raise e

    @classmethod
    async def kb_types_rel_detail_services(cls, query_db: AsyncSession, kb_id: int):
        """
        获取数据库-类型关联关系详细信息service

        :param query_db: orm对象
        :param kb_id: 数据库id
        :return: 数据库id对应的信息
        """
        kb_types_rel = await KbTypesRelDao.get_kb_types_rel_detail_by_id(query_db, kb_id=kb_id)
        if kb_types_rel:
            result = KbTypesRelModel(**CamelCaseUtil.transform_result(kb_types_rel))
        else:
            result = KbTypesRelModel(**dict())

        return result

    @classmethod
    async def get_kb_types_rel_list_by_kb_id_services(cls, query_db: AsyncSession, kb_id: int):
        """
        根据数据库ID获取所有关联的类型信息service

        :param query_db: orm对象
        :param kb_id: 数据库ID
        :return: 数据库关联的所有类型信息列表
        """
        kb_types_rel_list = await KbTypesRelDao.get_kb_types_rel_list_by_kb_id(query_db, kb_id)
        result = []
        for kb_types_rel in kb_types_rel_list:
            result.append(KbTypesRelModel(**CamelCaseUtil.transform_result(kb_types_rel)))
        return result

    @staticmethod
    async def export_kb_types_rel_list_services(kb_types_rel_list: List):
        """
        导出数据库-类型关联关系信息service

        :param kb_types_rel_list: 数据库-类型关联关系信息列表
        :return: 数据库-类型关联关系信息对应excel的二进制数据
        """
        # 创建一个映射字典，将英文键映射到中文键
        mapping_dict = {
            'kbId': '数据库id',
            'typeId': '类型id',
            'assignedAt': '创建时间',
            'assignedBy': '创建者',
        }
        binary_data = ExcelUtil.export_list2excel(kb_types_rel_list, mapping_dict)

        return binary_data

    @classmethod
    async def get_knowledge_bases_by_types_services(cls, query_db: AsyncSession, type_ids: List[int], page_num: int = 1, page_size: int = 10, kb_name: str = None):
        """
        根据类型ID列表获取数据库列表service

        :param query_db: orm对象
        :param type_ids: 类型ID列表
        :param page_num: 页码
        :param page_size: 每页大小
        :param kb_name: 数据库名称搜索
        :return: 数据库列表
        """
        result = await KbTypesRelDao.get_knowledge_bases_by_types(query_db, type_ids, page_num, page_size, kb_name)
        return result
