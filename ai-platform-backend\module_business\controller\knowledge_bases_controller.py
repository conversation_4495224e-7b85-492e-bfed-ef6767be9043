from fastapi import APIRouter, Depends, Form, Request, Query
from pydantic_validation_decorator import Validate<PERSON>ields
from sqlalchemy.ext.asyncio import AsyncSession
from config.enums import BusinessType
from config.get_db import get_db
from module_admin.annotation.log_annotation import Log
from module_admin.aspect.interface_auth import CheckUserInterfaceAuth
from module_admin.entity.vo.user_vo import CurrentUserModel
from module_admin.service.login_service import LoginService
from module_business.service.knowledge_bases_service import KnowledgeBasesService
from module_business.entity.vo.knowledge_bases_vo import DeleteKnowledgeBasesModel, KnowledgeBasesModel, KnowledgeBasesPageQueryModel
from utils.common_util import bytes2file_response
from utils.log_util import logger
from utils.page_util import PageResponseModel
from utils.response_util import ResponseUtil
from datetime import datetime
from fastapi.responses import JSONResponse
from fastapi.encoders import jsonable_encoder


knowledge_basesController = APIRouter(prefix='/business/knowledge_bases', dependencies=[Depends(LoginService.get_current_user)])


@knowledge_basesController.get(
    '/list', response_model=PageResponseModel  # dependencies=[Depends(CheckUserInterfaceAuth('business:knowledge_bases:list'))]
)
async def get_business_knowledge_bases_list(
    request: Request,
knowledge_bases_page_query: KnowledgeBasesPageQueryModel = Depends(),
    query_db: AsyncSession = Depends(get_db),
):
    # 获取分页数据
    knowledge_bases_page_query_result = await KnowledgeBasesService.get_knowledge_bases_list_services(query_db, knowledge_bases_page_query, is_page=True)
    logger.info('获取成功')

    return ResponseUtil.success(model_content=knowledge_bases_page_query_result)


@knowledge_basesController.post('')  # dependencies=[Depends(CheckUserInterfaceAuth('business:knowledge_bases:add'))]
@ValidateFields(validate_model='add_knowledge_bases')
@Log(title='数据库管理', business_type=BusinessType.INSERT)
async def add_business_knowledge_bases(
    request: Request,
    add_knowledge_bases: KnowledgeBasesModel,
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    add_knowledge_bases.created_at = datetime.now()
    add_knowledge_bases.owner_id = current_user.user.user_id
    add_knowledge_bases_result = await KnowledgeBasesService.add_knowledge_bases_services(query_db, add_knowledge_bases)
    logger.info(add_knowledge_bases_result.message)

    return ResponseUtil.success(msg=add_knowledge_bases_result.message)


@knowledge_basesController.put('')  # dependencies=[Depends(CheckUserInterfaceAuth('business:knowledge_bases:edit'))]
@ValidateFields(validate_model='edit_knowledge_bases')
@Log(title='数据库管理', business_type=BusinessType.UPDATE)
async def edit_business_knowledge_bases(
    request: Request,
    edit_knowledge_bases: KnowledgeBasesModel,
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    # edit_knowledge_bases.update_by = current_user.user.user_name
    edit_knowledge_bases.updated_at = datetime.now()
    edit_knowledge_bases_result = await KnowledgeBasesService.edit_knowledge_bases_services(query_db, edit_knowledge_bases)
    logger.info(edit_knowledge_bases_result.message)

    return ResponseUtil.success(msg=edit_knowledge_bases_result.message)

@knowledge_basesController.get(
    '/{kb_id}', response_model=KnowledgeBasesModel  # dependencies=[Depends(CheckUserInterfaceAuth('business:knowledge_bases:query'))]
)
async def query_detail_business_knowledge_bases(request: Request, kb_id: int, query_db: AsyncSession = Depends(get_db)):
    knowledge_bases_detail_result = await KnowledgeBasesService.knowledge_bases_detail_services(query_db, kb_id)
    logger.info(f'获取kb_id为{kb_id}的信息成功')

    return ResponseUtil.success(data=knowledge_bases_detail_result)

@knowledge_basesController.get(
    '/{kb_id}/with_types', response_model=KnowledgeBasesModel  # dependencies=[Depends(CheckUserInterfaceAuth('business:knowledge_bases:query'))]
)
async def query_detail_business_knowledge_bases_with_types(request: Request, kb_id: int, query_db: AsyncSession = Depends(get_db)):
    knowledge_bases_detail_result = await KnowledgeBasesService.knowledge_bases_detail_with_types_services(query_db, kb_id)
    logger.info(f'获取kb_id为{kb_id}的信息（包含类型）成功')

    return ResponseUtil.success(data=knowledge_bases_detail_result)

@knowledge_basesController.get("/list_with_types", summary='获取带类型标签的数据库列表')
async def get_knowledge_bases_with_types_list(
    request: Request,
    page_num: int = Query(1, description="页码"),
    page_size: int = Query(10, description="每页大小"),
    kb_name: str = Query(None, description="数据库名称"),
    query_db: AsyncSession = Depends(get_db)
):
    """获取带类型标签的数据库列表，rows 每项带 typeNames"""
    try:
        result = await KnowledgeBasesService.get_knowledge_bases_with_types_services(
            query_db, page_num, page_size, kb_name
        )
        return JSONResponse(content=jsonable_encoder({"code": 200, "msg": "操作成功", **result}))
    except Exception as e:
        return JSONResponse(content=jsonable_encoder({"code": 500, "msg": str(e)}))
