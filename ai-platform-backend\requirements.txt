# 核心框架
fastapi[all]==0.115.8
SQLAlchemy[asyncio]==2.0.38

# 数据库驱动 (选择其中一个)
asyncmy==0.2.10          # MySQL驱动
# asyncpg==0.30.0        # PostgreSQL驱动 (取消注释使用)
# psycopg2==2.9.10       # PostgreSQL驱动 (取消注释使用)

# 缓存和消息队列
redis>=5.2.1
# kafka-python==2.0.2  # 已禁用Kafka功能

# 认证和安全
passlib[bcrypt]==1.7.4
PyJWT[crypto]==2.10.1

# 任务调度
APScheduler==3.11.0

# 数据处理
pandas==2.2.3
openpyxl==3.1.5
Pillow==11.1.0

# 工具库
requests==2.32.3
aiohttp==3.9.3
psutil==7.0.0
loguru==0.7.3
DateTime==5.5
pydantic-validation-decorator==0.1.4
sqlglot[rs]==26.6.0
user-agents==2.2.0

# 文件存储
minio==7.2.15
