from sqlalchemy.ext.asyncio import AsyncSession
from typing import List
from config.constant import CommonConstant
from exceptions.exception import ServiceException
from module_admin.entity.vo.common_vo import CrudResponseModel
from module_business.entity.vo.projects_vo import ProjectsModel
from module_business.dao.project_types_rel_dao import ProjectTypesRelDao
from module_business.entity.vo.project_types_rel_vo import DeleteProjectTypesRelModel, ProjectTypesRelModel, ProjectTypesRelPageQueryModel
from utils.common_util import CamelCaseUtil
from utils.excel_util import ExcelUtil


class ProjectTypesRelService:
    """
    项目-类型关联关系模块服务层
    """

    @classmethod
    async def get_project_types_rel_list_services(
        cls, query_db: AsyncSession, query_object: ProjectTypesRelPageQueryModel, is_page: bool = False
    ):
        """
        获取项目-类型关联关系列表信息service

        :param query_db: orm对象
        :param query_object: 查询参数对象
        :param is_page: 是否开启分页
        :return: 项目-类型关联关系列表信息对象
        """
        project_types_rel_list_result = await ProjectTypesRelDao.get_project_types_rel_list(query_db, query_object, is_page)

        return project_types_rel_list_result


    @classmethod
    async def add_project_types_rel_services(cls, query_db: AsyncSession, page_object: ProjectTypesRelModel):
        """
        新增项目-类型关联关系信息service

        :param query_db: orm对象
        :param page_object: 新增项目-类型关联关系对象
        :return: 新增项目-类型关联关系校验结果
        """
        # 检查关联关系是否已存在
        existing_rel = await ProjectTypesRelDao.get_project_types_rel_by_project_id_and_type_id(
            query_db, page_object.project_id, page_object.type_id
        )
        if existing_rel:
            return CrudResponseModel(is_success=True, message="关联关系已存在")

        try:
            await ProjectTypesRelDao.add_project_types_rel_dao(query_db, page_object)
            await query_db.commit()
            return CrudResponseModel(is_success=True, message="新增成功")
        except Exception as e:
            await query_db.rollback()
            raise e

    @classmethod
    async def edit_project_types_rel_services(cls, query_db: AsyncSession, page_object: ProjectTypesRelModel):
        """
        编辑项目-类型关联关系信息service

        :param query_db: orm对象
        :param page_object: 编辑项目-类型关联关系对象
        :return: 编辑项目-类型关联关系校验结果
        """
        edit_project_types_rel = page_object.model_dump(exclude_unset=True, exclude={})
        project_types_rel_info = await cls.project_types_rel_detail_services(query_db, page_object.project_id)
        if project_types_rel_info.project_id:
            try:
                await ProjectTypesRelDao.edit_project_types_rel_dao(query_db, edit_project_types_rel)
                await query_db.commit()
                return CrudResponseModel(is_success=True, message='更新成功')
            except Exception as e:
                await query_db.rollback()
                raise e
        else:
            raise ServiceException(message='项目-类型关联关系不存在')

    @classmethod
    async def delete_project_types_rel_services(cls, query_db: AsyncSession, page_object: DeleteProjectTypesRelModel):
        """
        删除项目-类型关联关系信息service

        :param query_db: orm对象
        :param page_object: 删除项目-类型关联关系对象
        :return: 删除项目-类型关联关系校验结果
        """
        if page_object.project_ids:
            project_id_list = page_object.project_ids.split(',')
            try:
                for project_id in project_id_list:
                    await ProjectTypesRelDao.delete_project_types_rel_dao(query_db, ProjectTypesRelModel(projectId=project_id))
                await query_db.commit()
                return CrudResponseModel(is_success=True, message='删除成功')
            except Exception as e:
                await query_db.rollback()
                raise e
        else:
            raise ServiceException(message='传入为空')

    @classmethod
    async def delete_project_type_rel_by_project_and_type_services(cls, query_db: AsyncSession, project_id: int, type_id: int):
        """
        根据项目ID和类型ID删除特定的项目-类型关联关系service

        :param query_db: orm对象
        :param project_id: 项目ID
        :param type_id: 类型ID
        :return: 删除结果
        """
        try:
            await ProjectTypesRelDao.delete_project_type_rel_by_project_and_type(query_db, project_id, type_id)
            await query_db.commit()
            return CrudResponseModel(is_success=True, message='删除成功')
        except Exception as e:
            await query_db.rollback()
            raise e

    @classmethod
    async def project_types_rel_detail_services(cls, query_db: AsyncSession, project_id: int):
        """
        获取项目-类型关联关系详细信息service

        :param query_db: orm对象
        :param project_id: 
        :return: 对应的信息
        """
        project_types_rel = await ProjectTypesRelDao.get_project_types_rel_detail_by_id(query_db, project_id=project_id)
        if project_types_rel:
            result = ProjectTypesRelModel(**CamelCaseUtil.transform_result(project_types_rel))
        else:
            result = ProjectTypesRelModel(**dict())

        return result

    @classmethod
    async def get_project_types_rel_list_by_project_id_services(cls, query_db: AsyncSession, project_id: int):
        """
        根据项目ID获取所有关联的类型信息service

        :param query_db: orm对象
        :param project_id: 项目ID
        :return: 项目关联的所有类型信息列表
        """
        project_types_rel_list = await ProjectTypesRelDao.get_project_types_rel_list_by_project_id(query_db, project_id)
        result = []
        for project_types_rel in project_types_rel_list:
            result.append(ProjectTypesRelModel(**CamelCaseUtil.transform_result(project_types_rel)))
        return result

    @staticmethod
    async def export_project_types_rel_list_services(project_types_rel_list: List):
        """
        导出项目-类型关联关系信息service

        :param project_types_rel_list: 项目-类型关联关系信息列表
        :return: 项目-类型关联关系信息对应excel的二进制数据
        """
        # 创建一个映射字典，将英文键映射到中文键
        mapping_dict = {
            'projectId': '',
            'typeId': '',
            'assignedAt': '',
            'assignedBy': '',
        }
        binary_data = ExcelUtil.export_list2excel(project_types_rel_list, mapping_dict)

        return binary_data

    @classmethod
    async def get_projects_by_types_services(cls, query_db: AsyncSession, type_ids: List[int], page_num: int = 1, page_size: int = 12, project_name: str = ''):
        """
        根据类型ID列表获取项目列表service，支持分页和项目名称模糊搜索
        :param query_db: orm对象
        :param type_ids: 类型ID列表
        :param page_num: 当前页码
        :param page_size: 每页数量
        :param project_name: 项目名称模糊搜索
        :return: 项目分页列表
        """
        projects_page = await ProjectTypesRelDao.get_projects_by_types(query_db, type_ids, page_num, page_size, project_name, is_page=True)
        return projects_page
