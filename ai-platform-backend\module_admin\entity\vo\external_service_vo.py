from datetime import datetime
from pydantic import BaseModel, ConfigDict, Field
from pydantic.alias_generators import to_camel
from typing import Optional, List


class ExternalServiceHealthModel(BaseModel):
    """
    外部服务健康检查模型
    """
    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True)

    tool_id: Optional[int] = Field(default=None, alias="toolId", description='工具ID')
    tool_name: Optional[str] = Field(default=None, alias="toolName", description='工具名称')
    executable_api: Optional[str] = Field(default=None, alias="executableApi", description='可执行API地址')
    health_url: Optional[str] = Field(default=None, alias="healthUrl", description='健康检查URL')
    status: Optional[str] = Field(default=None, description='服务状态')
    response_time: Optional[float] = Field(default=None, alias="responseTime", description='响应时间(ms)')
    last_check_time: Optional[datetime] = Field(default=None, alias="lastCheckTime", description='最后检查时间')
    error_message: Optional[str] = Field(default=None, alias="errorMessage", description='错误信息')
    is_active: Optional[int] = Field(default=1, alias="isActive", description='是否激活')
    is_health: Optional[int] = Field(default=1, alias="isHealth", description='服务是否健康')


class ExternalServiceMonitorModel(BaseModel):
    """
    外部服务监控模型
    """
    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True)

    total_services: Optional[int] = Field(default=0, alias="totalServices", description='总服务数')
    healthy_services: Optional[int] = Field(default=0, alias="healthyServices", description='健康服务数')
    unhealthy_services: Optional[int] = Field(default=0, alias="unhealthyServices", description='不健康服务数')
    services: Optional[List[ExternalServiceHealthModel]] = Field(default=[], description='服务列表')
    last_update_time: Optional[datetime] = Field(default=None, alias="lastUpdateTime", description='最后更新时间') 