"""
日志管理器
提供日志文件管理功能，包括查询、清理、压缩等
"""

import os
import gzip
import shutil
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Tuple
from pathlib import Path

from utils.log_util import logger


class LogManager:
    """日志管理器"""
    
    def __init__(self, log_dir: str = None):
        """
        初始化日志管理器
        
        :param log_dir: 日志目录路径，默认为当前工作目录下的logs文件夹
        """
        self.log_dir = Path(log_dir) if log_dir else Path(os.getcwd()) / 'logs'
        self._ensure_log_directory()
    
    def _ensure_log_directory(self):
        """确保日志目录存在"""
        self.log_dir.mkdir(parents=True, exist_ok=True)
    
    def get_log_files(self, log_type: str = 'all', date_range: Optional[Tuple[str, str]] = None) -> List[Dict]:
        """
        获取日志文件列表
        
        :param log_type: 日志类型 ('all', 'error', 'info', 'debug')
        :param date_range: 日期范围 (start_date, end_date)
        :return: 日志文件信息列表
        """
        files = []
        
        try:
            # 获取所有日志文件
            for file_path in self.log_dir.glob('*.log*'):
                if file_path.is_file():
                    file_info = self._get_file_info(file_path, log_type, date_range)
                    if file_info:
                        files.append(file_info)
            
            # 按日期倒序排序
            files.sort(key=lambda x: x['date'], reverse=True)
            
        except Exception as e:
            logger.error(f"获取日志文件列表失败: {e}")
        
        return files
    
    def _get_file_info(self, file_path: Path, log_type: str, date_range: Optional[Tuple[str, str]]) -> Optional[Dict]:
        """
        获取单个文件信息
        
        :param file_path: 文件路径
        :param log_type: 日志类型过滤
        :param date_range: 日期范围过滤
        :return: 文件信息字典或None
        """
        try:
            # 解析文件名获取日期和类型
            file_name = file_path.name
            
            # 跳过非日志文件
            if not (file_name.endswith('.log') or file_name.endswith('.log.gz') or file_name.endswith('.zip')):
                return None
            
            # 提取日期（格式：YYYY-MM-DD）
            date_str = None
            file_type = 'unknown'
            
            if '_all.log' in file_name:
                date_str = file_name.split('_all.log')[0]
                file_type = 'all'
            elif '_error.log' in file_name:
                date_str = file_name.split('_error.log')[0]
                file_type = 'error'
            
            if not date_str:
                return None
            
            # 验证日期格式
            try:
                file_date = datetime.strptime(date_str, '%Y-%m-%d').date()
            except ValueError:
                return None
            
            # 应用类型过滤
            if log_type != 'all' and file_type != log_type:
                return None
            
            # 应用日期范围过滤
            if date_range:
                start_date, end_date = date_range
                if start_date and file_date < datetime.strptime(start_date, '%Y-%m-%d').date():
                    return None
                if end_date and file_date > datetime.strptime(end_date, '%Y-%m-%d').date():
                    return None
            
            # 获取文件统计信息
            stat = file_path.stat()
            
            return {
                'name': file_name,
                'path': str(file_path),
                'type': file_type,
                'date': date_str,
                'size': stat.st_size,
                'size_mb': round(stat.st_size / 1024 / 1024, 2),
                'modified_time': datetime.fromtimestamp(stat.st_mtime).strftime('%Y-%m-%d %H:%M:%S'),
                'is_compressed': file_name.endswith(('.gz', '.zip'))
            }
            
        except Exception as e:
            logger.error(f"获取文件信息失败 {file_path}: {e}")
            return None
    
    def read_log_file(self, file_path: str, lines: int = 100, from_end: bool = True) -> List[str]:
        """
        读取日志文件内容
        
        :param file_path: 文件路径
        :param lines: 读取行数
        :param from_end: 是否从文件末尾开始读取
        :return: 日志行列表
        """
        try:
            file_path = Path(file_path)
            
            if not file_path.exists():
                raise FileNotFoundError(f"文件不存在: {file_path}")
            
            # 处理压缩文件
            if file_path.suffix == '.gz':
                with gzip.open(file_path, 'rt', encoding='utf-8') as f:
                    content = f.readlines()
            else:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.readlines()
            
            # 根据参数返回指定行数
            if from_end:
                return content[-lines:] if len(content) > lines else content
            else:
                return content[:lines] if len(content) > lines else content
                
        except Exception as e:
            logger.error(f"读取日志文件失败 {file_path}: {e}")
            raise
    
    def clean_old_logs(self, days_to_keep: int = 30, log_type: Optional[str] = None) -> int:
        """
        清理旧日志文件
        
        :param days_to_keep: 保留天数
        :param log_type: 日志类型，None表示所有类型
        :return: 删除的文件数量
        """
        deleted_count = 0
        cutoff_date = datetime.now() - timedelta(days=days_to_keep)
        
        try:
            for file_path in self.log_dir.glob('*.log*'):
                if file_path.is_file():
                    file_info = self._get_file_info(file_path, log_type or 'all', None)
                    if file_info:
                        file_date = datetime.strptime(file_info['date'], '%Y-%m-%d')
                        if file_date < cutoff_date:
                            file_path.unlink()
                            deleted_count += 1
                            logger.info(f"删除旧日志文件: {file_path}")
            
        except Exception as e:
            logger.error(f"清理旧日志失败: {e}")
            raise
        
        return deleted_count
    
    def compress_logs(self, days_old: int = 7) -> int:
        """
        压缩旧日志文件
        
        :param days_old: 压缩多少天前的日志
        :return: 压缩的文件数量
        """
        compressed_count = 0
        cutoff_date = datetime.now() - timedelta(days=days_old)
        
        try:
            for file_path in self.log_dir.glob('*.log'):
                if file_path.is_file():
                    file_info = self._get_file_info(file_path, 'all', None)
                    if file_info:
                        file_date = datetime.strptime(file_info['date'], '%Y-%m-%d')
                        if file_date < cutoff_date:
                            # 压缩文件
                            compressed_path = file_path.with_suffix('.log.gz')
                            with open(file_path, 'rb') as f_in:
                                with gzip.open(compressed_path, 'wb') as f_out:
                                    shutil.copyfileobj(f_in, f_out)
                            
                            # 删除原文件
                            file_path.unlink()
                            compressed_count += 1
                            logger.info(f"压缩日志文件: {file_path} -> {compressed_path}")
            
        except Exception as e:
            logger.error(f"压缩日志失败: {e}")
            raise
        
        return compressed_count
    
    def get_log_stats(self) -> Dict:
        """
        获取日志统计信息
        
        :return: 统计信息字典
        """
        try:
            files = self.get_log_files()
            
            total_files = len(files)
            total_size = sum(f['size'] for f in files)
            total_size_mb = round(total_size / 1024 / 1024, 2)
            
            # 按类型统计
            type_stats = {}
            for file_info in files:
                file_type = file_info['type']
                if file_type not in type_stats:
                    type_stats[file_type] = {'count': 0, 'size_mb': 0}
                type_stats[file_type]['count'] += 1
                type_stats[file_type]['size_mb'] += file_info['size_mb']
            
            # 压缩文件统计
            compressed_files = [f for f in files if f['is_compressed']]
            
            return {
                'total_files': total_files,
                'total_size_mb': total_size_mb,
                'compressed_files': len(compressed_files),
                'type_stats': type_stats,
                'oldest_log': files[-1]['date'] if files else None,
                'newest_log': files[0]['date'] if files else None,
            }
            
        except Exception as e:
            logger.error(f"获取日志统计失败: {e}")
            return {}


# 全局日志管理器实例
_log_manager = None


def get_log_manager() -> LogManager:
    """
    获取日志管理器实例（单例模式）
    
    :return: LogManager实例
    """
    global _log_manager
    if _log_manager is None:
        _log_manager = LogManager()
    return _log_manager
