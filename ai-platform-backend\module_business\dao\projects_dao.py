from datetime import datetime
from sqlalchemy import delete, select, update
from sqlalchemy.ext.asyncio import AsyncSession
from module_business.entity.do.projects_do import RdProjects
from module_business.entity.vo.projects_vo import ProjectsModel, ProjectsPageQueryModel
from utils.page_util import PageUtil

class ProjectsDao:
    """
    项目信息管理模块数据库操作层
    """

    @classmethod
    async def get_projects_detail_by_id(cls, db: AsyncSession, project_id: int):
        """
        根据获取项目信息管理详细信息

        :param db: orm对象
        :param project_id: 
        :return: 项目信息管理信息对象
        """
        projects_info = (
            (
                await db.execute(
                    select(RdProjects)
                    .where(
                        RdProjects.project_id == project_id
                    )
                )
            )
            .scalars()
            .first()
        )

        return projects_info

    @classmethod
    async def get_projects_detail_by_info(cls, db: AsyncSession, projects: ProjectsModel):
        """
        根据项目信息管理参数获取项目信息管理信息

        :param db: orm对象
        :param projects: 项目信息管理参数对象
        :return: 项目信息管理信息对象
        """
        projects_info = (
            (
                await db.execute(
                    select(RdProjects).where(
                        RdProjects.project_id == projects.project_id if projects.project_id else True,
                        RdProjects.project_name == projects.project_name if projects.project_name else True,
                        RdProjects.owner_id == projects.owner_id if projects.owner_id else True,
                        RdProjects.is_deleted == 0,
                    )
                )
            )
            .scalars()
            .first()
        )

        return projects_info

    @classmethod
    async def get_projects_list(cls, db: AsyncSession, query_object: ProjectsPageQueryModel, data_scope_sql: str = None, is_page: bool = False):
        """
        根据查询参数获取项目信息管理列表信息

        :param db: orm对象
        :param query_object: 查询参数对象
        :param data_scope_sql: 数据权限过滤条件
        :param is_page: 是否开启分页
        :return: 项目信息管理列表信息对象
        """
        query = (
            select(RdProjects)
            .where(
                RdProjects.project_name.like(f'%{query_object.project_name}%') if query_object.project_name else True,
                RdProjects.description == query_object.description if query_object.description else True,
                RdProjects.owner_id == query_object.owner_id if query_object.owner_id else True,
                RdProjects.is_deleted == False,
                RdProjects.created_at == query_object.created_at if query_object.created_at else True,
                RdProjects.updated_at == query_object.updated_at if query_object.updated_at else True,
            )
            .distinct()
        )

        # 添加数据权限过滤
        if data_scope_sql and data_scope_sql != "all":
            if data_scope_sql == "none":
                # 普通用户无法查看任何项目
                query = query.where(RdProjects.project_id == -1)  # 永远不会匹配的条件
            elif data_scope_sql.startswith("project_ids:"):
                # 用户参与的项目
                project_ids_str = data_scope_sql.split(":", 1)[1]
                if project_ids_str:
                    project_ids = [int(pid) for pid in project_ids_str.split(",")]
                    query = query.where(RdProjects.project_id.in_(project_ids))
                else:
                    query = query.where(RdProjects.project_id == -1)
            elif data_scope_sql.startswith("owner_only:"):
                # 只能查看自己拥有的项目
                owner_id = int(data_scope_sql.split(":", 1)[1])
                query = query.where(RdProjects.owner_id == owner_id)

        # 添加动态排序
        if query_object.order_by_column:
            # 将驼峰命名转换为下划线命名
            order_column = ''.join(['_' + c.lower() if c.isupper() else c for c in query_object.order_by_column]).lstrip('_')
            # 获取排序字段
            order_field = getattr(RdProjects, order_column, None)
            if order_field is not None:
                if query_object.is_asc:
                    query = query.order_by(order_field.asc())
                else:
                    query = query.order_by(order_field.desc())
        else:
            # 默认按更新时间倒序
            query = query.order_by(RdProjects.updated_at.desc())

        projects_list = await PageUtil.paginate(db, query, query_object.page_num, query_object.page_size, is_page)

        return projects_list

    @classmethod
    async def add_projects_dao(cls, db: AsyncSession, projects: ProjectsModel):
        """
        新增项目信息管理数据库操作

        :param db: orm对象
        :param projects: 项目信息管理对象
        :return:
        """
        db_projects = RdProjects(**projects.model_dump(exclude={}))
        db.add(db_projects)
        await db.flush()

        return db_projects

    @classmethod
    async def edit_projects_dao(cls, db: AsyncSession, projects: dict):
        """
        编辑项目信息管理数据库操作

        :param db: orm对象
        :param projects: 需要更新的项目信息管理字典
        :return:
        """
        await db.execute(update(RdProjects), [projects])

    @classmethod
    async def delete_projects_dao(cls, db: AsyncSession, projects: ProjectsModel):
        """
        删除项目信息管理数据库操作

        :param db: orm对象
        :param projects: 项目信息管理对象
        :return:
        """
        await db.execute(delete(RdProjects).where(RdProjects.project_id.in_([projects.project_id])))

    @classmethod
    async def get_projects_by_owner(cls, db: AsyncSession, owner_id: int):
        """
        根据所有者ID获取项目列表

        :param db: orm对象
        :param owner_id: 所有者ID
        :return: 项目列表
        """
        query = (
            select(RdProjects)
            .where(
                RdProjects.owner_id == owner_id,
                RdProjects.is_deleted == 0,
            )
        )
        result = (await db.execute(query)).scalars().all()
        return result

