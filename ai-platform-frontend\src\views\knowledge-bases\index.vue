<template>
  <div class="app-container">
    <!-- 类型多选筛选 -->
    <div class="filter-container">
      <div class="filter-types-search">
        <el-checkbox-group v-model="selectedTypes" class="mr-2" @change="handleTypeChange">
          <el-checkbox-button :value="''">全部</el-checkbox-button>
          <el-checkbox-button v-for="type in types" :key="type.typeId" :value="type.typeId">{{ type.displayName }}</el-checkbox-button>
        </el-checkbox-group>
        <el-button type="primary" class="ml-2" @click="showCreateDialog = true">创建新数据库</el-button>
        <el-input
          v-model="searchQuery"
          placeholder="搜索数据库名称"
          class="search-input ml-2"
          clearable
          @input="handleSearch"
          style="width: 220px;"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
      </div>
    </div>

    <!-- 数据库列表 -->
    <el-row :gutter="20" class="knowledge-bases-list">
      <el-col v-if="knowledgeBases.length === 0" :span="24">
        <el-empty description="暂无数据库数据" />
      </el-col>
      <el-col v-for="kb in knowledgeBases" :key="kb.kbId || kb.kb_id" :xs="24" :sm="12" :md="8" :lg="6">
        <el-card class="knowledge-base-card" shadow="hover" @dblclick="goToDetail(kb)">
          <template #header>
            <div class="kb-header">
              <div class="kb-title-section">
                <span class="kb-name">
                  {{ kb.kbName || kb.kb_name || '未命名数据库' }}
                  <span class="kb-id">(ID: {{ kb.kbId || kb.kb_id }})</span>
                </span>
                <div class="kb-actions">
                  <el-button size="small" type="primary" @click="editKnowledgeBase(kb)">编辑</el-button>
                  <el-button size="small" type="danger" @click="deleteKnowledgeBase(kb)">删除</el-button>
                </div>
              </div>
              <div class="kb-types">
                <el-tag
                  v-for="typeName in kb.typeNames || kb.type_names || []"
                  :key="typeName"
                  size="small"
                  type="info"
                  class="type-tag"
                >
                  {{ typeName }}
                </el-tag>
                <!-- 如果project_id为null，显示通用标签 -->
                <el-tag
                  v-if="kb.project_id === null"
                  size="small"
                  type="success"
                  class="type-tag"
                >
                  通用
                </el-tag>
                <span class="owner-info">负责人：{{ kb.ownerName || kb.owner_name || '未知用户' }}</span>
              </div>
            </div>
          </template>
          
          <div class="kb-content">
            <div class="kb-files-stats">
              <div class="stats-title">文件统计：</div>
              <div class="stats-grid">
                <div class="stat-item">
                  <div class="stat-label">模型</div>
                  <div class="stat-value">{{ kb.fileStats?.model || 0 }}</div>
                </div>
                <div class="stat-item">
                  <div class="stat-label">报告</div>
                  <div class="stat-value">{{ kb.fileStats?.report || 0 }}</div>
                </div>
                <div class="stat-item">
                  <div class="stat-label">数据表</div>
                  <div class="stat-value">{{ kb.fileStats?.dataset || 0 }}</div>
                </div>
                <div class="stat-item">
                  <div class="stat-label">日志</div>
                  <div class="stat-value">{{ kb.fileStats?.log || 0 }}</div>
                </div>
                <div class="stat-item">
                  <div class="stat-label">文档</div>
                  <div class="stat-value">{{ kb.fileStats?.document || 0 }}</div>
                </div>
                <div class="stat-item">
                  <div class="stat-label">归档</div>
                  <div class="stat-value">{{ kb.fileStats?.archived || 0 }}</div>
                </div>
              </div>
            </div>
            
            <div class="kb-description">{{ kb.description || '暂无描述' }}</div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[12, 24, 36, 48]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 新建/编辑数据库弹窗 -->
    <el-dialog v-model="showCreateDialog" :title="editKnowledgeBaseData ? '编辑数据库' : '新建数据库'">
      <el-form :model="knowledgeBaseForm" label-width="100px">
        <el-form-item label="数据库名称" style="white-space: nowrap;">
          <el-input v-model="knowledgeBaseForm.kbName" />
        </el-form-item>
        <el-form-item label="数据库描述" style="white-space: nowrap;">
          <el-input v-model="knowledgeBaseForm.description" type="textarea" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showCreateDialog = false">取消</el-button>
        <el-button type="primary" @click="submitKnowledgeBase">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { Search } from '@element-plus/icons-vue'
import { getTypesList } from '@/api/types'
import { addKnowledgeBase, updateKnowledgeBase, getKnowledgeBasesByTypes } from '@/api/knowledge-bases'
import { getFileStatsByKb } from '@/api/knowledge-bases'
import { ElMessage, ElMessageBox } from 'element-plus'

const router = useRouter()

const types = ref([])
const selectedTypes = ref([])
const searchQuery = ref('')
const knowledgeBases = ref([])
const currentPage = ref(1)
const pageSize = ref(12)
const total = ref(0)
const showCreateDialog = ref(false)
const editKnowledgeBaseData = ref(null)
const knowledgeBaseForm = ref({ kbName: '', description: '' })

// 获取类型列表
const fetchTypes = async () => {
  try {
    const res = await getTypesList({ pageNum: 1, pageSize: 100, isActive: 1 })
    if (res.code === 200) {
      types.value = res.rows
    }
  } catch (error) {
    console.error('获取类型列表失败:', error)
    ElMessage.error('获取类型列表失败')
  }
}

// 获取数据库列表
const fetchKnowledgeBases = async () => {
  try {
    const typeIds = selectedTypes.value.length > 0 ? selectedTypes.value.join(',') : ''
    const res = await getKnowledgeBasesByTypes({
      typeIds: typeIds || '',
      pageNum: currentPage.value,
      pageSize: pageSize.value,
      kbName: searchQuery.value || ''
    })

    if (res.code === 200) {
      knowledgeBases.value = res.data.rows || []
      total.value = res.data.total || 0

      // 获取每个数据库的文件统计
      for (const kb of knowledgeBases.value) {
        try {
          const kbId = kb.kbId || kb.kb_id
          if (kbId) {
            const statsRes = await getFileStatsByKb(kbId)
            kb.fileStats = (statsRes.code === 200) ? statsRes.data : {}
          } else {
            kb.fileStats = {}
          }
        } catch (error) {
          console.error(`获取数据库${kb.kbId || kb.kb_id} 的文件统计失败:`, error)
          kb.fileStats = {}
        }
      }
    }
  } catch (error) {
    console.error('获取数据库列表失败:', error)
    ElMessage.error('获取数据库列表失败')
  }
}

// 事件处理
const handleTypeChange = () => {
  // 如果选择了"全部"，清空其他选择；否则移除"全部"选项
  if (selectedTypes.value.includes('')) {
    selectedTypes.value = ['']
  } else {
    selectedTypes.value = selectedTypes.value.filter(type => type !== '')
  }
  
  currentPage.value = 1
  fetchKnowledgeBases()
}

const handleSearch = () => {
  currentPage.value = 1
  fetchKnowledgeBases()
}

const handleSizeChange = (val) => {
  pageSize.value = val
  currentPage.value = 1
  fetchKnowledgeBases()
}

const handleCurrentChange = (val) => {
  currentPage.value = val
  fetchKnowledgeBases()
}

// 新建/编辑数据库
const submitKnowledgeBase = async () => {
  if (!knowledgeBaseForm.value.kbName) {
    ElMessage.warning('数据库名称不能为空')
    return
  }
  
  try {
    if (editKnowledgeBaseData.value) {
      await updateKnowledgeBase({ ...editKnowledgeBaseData.value, ...knowledgeBaseForm.value })
      ElMessage.success('编辑成功')
    } else {
      await addKnowledgeBase(knowledgeBaseForm.value)
      ElMessage.success('创建成功')
    }
    showCreateDialog.value = false
    editKnowledgeBaseData.value = null
    knowledgeBaseForm.value = { kbName: '', description: '' }
    fetchKnowledgeBases()
  } catch (error) {
    console.error('提交数据库失败:', error)
    ElMessage.error('操作失败')
  }
}

const editKnowledgeBase = (kb) => {
  editKnowledgeBaseData.value = kb
  knowledgeBaseForm.value = { 
    kbName: kb.kbName || kb.kb_name || '', 
    description: kb.description || '' 
  }
  showCreateDialog.value = true
}

const deleteKnowledgeBase = (kb) => {
  ElMessageBox.confirm('确定要删除该数据库吗？', '提示', { type: 'warning' })
    .then(async () => {
      try {
        const kbId = kb.kbId || kb.kb_id
        // 确保传递完整的数据库信息，包括kb_name
        await updateKnowledgeBase({ 
          kbId: kbId,
          kbName: kb.kbName || kb.kb_name || '',
          description: kb.description || '',
          isDeleted: 1 
        })
        ElMessage.success('删除成功')
        fetchKnowledgeBases()
      } catch (error) {
        console.error('删除数据库失败:', error)
        ElMessage.error('删除失败')
      }
    })
    .catch(() => {
      // 用户取消删除
    })
}

const goToDetail = (kb) => {
  const kbId = kb.kbId || kb.kb_id
  if (kbId) {
    router.push(`/knowledge-bases/detail/${kbId}`)
  } else {
    ElMessage.error('数据库ID不存在')
  }
}

onMounted(() => {
  fetchTypes()
  fetchKnowledgeBases()
})
</script>

<style scoped>
.app-container {
  padding: 30px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.filter-container {
  display: flex;
  align-items: center;
  margin-bottom: 30px;
  flex-wrap: wrap;
  gap: 16px;
  padding: 24px;
  background: transparent;
  border-radius: 0;
  border: none;
  box-shadow: none;
}

.ml-2 {
  margin-left: 12px;
}

.mr-2 {
  margin-right: 12px;
}

.search-input {
  width: 220px;
  :deep(.el-input__wrapper) {
    border-radius: 6px;
    background: rgba(255, 255, 255, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow: none;
    transition: border-color 0.2s;
    &:hover, &.is-focus {
      border-color: #67aafc;
      box-shadow: none;
    }
  }
}

.knowledge-bases-list {
  margin-bottom: 30px;
  min-height: 400px;
  
  :deep(.el-col) {
    display: flex;
  }
}

.knowledge-base-card {
  margin-bottom: 24px;
  min-height: 300px;
  height: auto;
  width: 100%;
  border: none;
  border-radius: 16px;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  overflow: hidden;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  
  &:hover {
    transform: translateY(-8px);
    box-shadow: 0 16px 48px rgba(102, 126, 234, 0.2);
  }
  
  :deep(.el-card__header) {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    padding: 20px;
    flex-shrink: 0;
  }
  
  :deep(.el-card__body) {
    padding: 20px;
    flex: 1;
    display: flex;
    flex-direction: column;
  }
}

.kb-header {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.kb-title-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.kb-name {
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
  flex: 1;
  white-space: nowrap;
  min-width: 0;
  overflow: hidden;
  text-overflow: ellipsis;
}

.kb-id {
  margin-left: 8px;
  color: #999;
  font-size: 12px;
  font-weight: normal;
}

.kb-actions {
  display: flex;
  gap: 8px;
  
  :deep(.el-button) {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
    
    &:hover {
      transform: translateY(-2px);
    }
  }
}

.kb-types {
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
  min-height: 32px;
  align-items: center;
  
  :deep(.el-tag) {
    border-radius: 6px;
    font-weight: 500;
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow: none;
    background: rgba(255, 255, 255, 0.8);
  }
}

.type-tag {
  margin-right: 4px;
}

.kb-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
  flex: 1;
}

.kb-description {
  color: #606266;
  line-height: 1.6;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  min-height: 40px;
  margin-top: 12px;
  padding-top: 16px;
  border-top: 1px solid rgba(102, 126, 234, 0.1);
  font-size: 14px;
  flex-shrink: 0;
}

.kb-files-stats {
  display: flex;
  flex-direction: column;
  gap: 12px;
  flex: 1;
  min-height: 120px;
}

.stats-title {
  font-weight: 600;
  color: #2c3e50;
  font-size: 14px;
  position: relative;
  padding-left: 12px;
  
  &::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 3px;
    height: 16px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 2px;
  }
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 8px;
  width: 100%;
  flex: 1;
  min-height: 100px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 10px 6px;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.15) 0%, rgba(118, 75, 162, 0.15) 100%);
  }
}

.stat-label {
  font-size: 10px;
  color: #667eea;
  margin-bottom: 3px;
  font-weight: 600;
  text-align: center;
}

.stat-value {
  font-size: 16px;
  font-weight: 700;
  color: #2c3e50;
}

.no-types {
  color: #9ca3af;
  font-size: 12px;
  font-style: italic;
  padding: 4px 8px;
  background: rgba(255, 255, 255, 0.5);
  border-radius: 4px;
}

.owner-info {
  color: #6b7280;
  font-size: 12px;
  font-weight: 500;
  margin-left: auto;
  padding: 4px 8px;
  background: rgba(59, 130, 246, 0.1);
  border-radius: 4px;
  border: 1px solid rgba(59, 130, 246, 0.2);
}

.pagination-container {
  position: fixed;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  justify-content: center;
  padding: 15px 25px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  z-index: 1000;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .app-container {
    padding: 20px;
  }
  
  .filter-container {
    flex-direction: column;
    align-items: stretch;
    padding: 20px;
  }
  
  .search-input {
    width: 100%;
  }
  
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 6px;
  }
  
  .knowledge-base-card {
    min-height: 250px;
  }
  
  .knowledge-bases-list {
    min-height: 300px;
  }
  
  .kb-files-stats {
    min-height: 100px;
  }
  
  .stats-grid {
    min-height: 60px;
  }
}

.filter-types-search {
  display: flex;
  align-items: center;
  gap: 24px;
  width: 100%;
  margin-bottom: 8px;
}

:deep(.el-checkbox-group) .el-checkbox-button__inner {
  font-size: 16px;
  padding: 8px 24px;
  border-radius: 24px !important;
  margin-right: 12px;
  margin-bottom: 4px;
  height: 40px;
  min-width: 60px;
  background: #f5f7fa;
  border: 1.5px solid #e3eaf1;
  transition: all 0.2s;
}

:deep(.el-checkbox-button.is-checked) .el-checkbox-button__inner {
  background-color: #409eff !important;
  border-color: #409eff !important;
  color: #ffffff !important;
}
</style> 