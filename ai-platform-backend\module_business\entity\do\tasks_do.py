from sqlalchemy import <PERSON>IMESTAMP, String, Column, BigInteger, Enum, Text, JSON, SmallInteger, Integer
from config.database import Base


class RdTasks(Base):
    """
    任务信息管理表
    """

    __tablename__ = 'rd_tasks'

    task_id = Column(BigInteger, primary_key=True, autoincrement=True, nullable=False, comment='')
    project_id = Column(BigInteger, nullable=False, comment='')
    task_name = Column(String(255), nullable=False, comment='')
    type_id = Column(Integer, nullable=False, comment='引用system_types')
    description = Column(Text, nullable=True, comment='')
    assigned_to = Column(BigInteger, nullable=True, comment='')
    status = Column(Enum("pending","queued","running","completed","failed","cancelled"), nullable=True, comment='')
    progress = Column(Integer, nullable=True, comment='')
    tool_id = Column(Integer, nullable=True, comment='')
    parameters = Column(JSON, nullable=True, comment='')
    parameters_md5 = Column(String(32), nullable=True, comment='参数MD5值，用于历史任务匹配')
    created_at = Column(TIMESTAMP, nullable=True, comment='')
    updated_at = Column(TIMESTAMP, nullable=True, comment='')
    started_at = Column(TIMESTAMP, nullable=True, comment='')
    completed_at = Column(TIMESTAMP, nullable=True, comment='')
    is_deleted = Column(SmallInteger, nullable=True, comment='')
    average_execution_time = Column(Integer, nullable=True, comment='工具平均执行时间（动态更新）')



