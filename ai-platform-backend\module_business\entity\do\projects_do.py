from sqlalchemy import TIM<PERSON>TAMP, String, Column, BigInteger, Text, SmallInteger
from config.database import Base


class RdProjects(Base):
    """
    项目信息管理表
    """

    __tablename__ = 'rd_projects'

    project_id = Column(BigInteger, primary_key=True, autoincrement=True, nullable=False, comment='')
    project_name = Column(String(255), nullable=False, comment='')
    description = Column(Text, nullable=True, comment='')
    owner_id = Column(BigInteger, nullable=False, comment='')
    is_deleted = Column(SmallInteger, nullable=True, comment='')
    created_at = Column(TIMESTAMP, nullable=True, comment='')
    updated_at = Column(TIMESTAMP, nullable=True, comment='')



