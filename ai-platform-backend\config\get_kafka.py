"""
Kafka连接配置
参考get_redis.py的实现方式，自动加载.env.dev
注意：此文件中的Kafka功能已被注释掉
"""

import os
import logging
from typing import Optional
# from kafka import KafkaProducer, KafkaConsumer, TopicPartition
# from kafka.errors import KafkaError
import json
from utils.log_util import logger
from dotenv import load_dotenv

# 在DEV环境中设置Kafka库的日志级别
# if os.getenv('APP_ENV', 'dev').lower() == 'dev':
#     # 启用kafka库的详细日志
#     logging.getLogger('kafka').setLevel(logging.DEBUG)
#     logging.getLogger('kafka.producer').setLevel(logging.DEBUG)
#     logging.getLogger('kafka.consumer').setLevel(logging.DEBUG)
#     logging.getLogger('kafka.client').setLevel(logging.DEBUG)
#     logging.getLogger('kafka.conn').setLevel(logging.DEBUG)
#     logging.getLogger('kafka.protocol').setLevel(logging.DEBUG)
#     # 添加更多Kafka相关日志器
#     logging.getLogger('kafka.coordinator').setLevel(logging.DEBUG)
#     logging.getLogger('kafka.consumer.fetcher').setLevel(logging.DEBUG)
#     logging.getLogger('kafka.consumer.group').setLevel(logging.DEBUG)
#     logging.getLogger('kafka.cluster').setLevel(logging.DEBUG)
#     logging.getLogger('kafka.metrics').setLevel(logging.DEBUG)
#     logger.info("DEV环境：已启用Kafka库的详细日志")

# 自动加载.env.dev或指定环境
def get_env_file():
    env = os.environ.get('APP_ENV', 'dev')
    env_file = f'.env.{env}'
    return env_file

load_dotenv(get_env_file())

class KafkaConfig:
    """Kafka配置类（已禁用）"""
    
    # Kafka服务器地址
    KAFKA_BOOTSTRAP_SERVERS = os.getenv('KAFKA_BOOTSTRAP_SERVERS', 'localhost:9092')
    
    # 主题配置
    TASK_LOGS_TOPIC = os.getenv('KAFKA_TASK_LOGS_TOPIC', 'task_logs_dev')
    
    # 消费者组配置
    CONSUMER_GROUP_ID = os.getenv('KAFKA_CONSUMER_GROUP_ID', 'ai_platform_log_consumer_dev')
    
    # 生产者配置（已禁用）
    PRODUCER_CONFIG = {
        'bootstrap_servers': KAFKA_BOOTSTRAP_SERVERS,
        'value_serializer': lambda v: json.dumps(v, ensure_ascii=False).encode('utf-8'),
        'key_serializer': lambda k: k.encode('utf-8') if k else None,
        'acks': 'all',
        'retries': 3,
        'batch_size': 16384,
        'linger_ms': 10,
        'buffer_memory': 33554432,
    }
    
    # 在DEV环境中启用详细日志（已禁用）
    # if os.getenv('APP_ENV', 'dev').lower() == 'dev':
    #     # 启用更详细的配置用于调试
    #     PRODUCER_CONFIG.update({
    #         'request_timeout_ms': 60000,  # 增加请求超时时间
    #         # 移除不支持的配置项
    #         # 'delivery_timeout_ms': 120000,  # 这个配置项不被支持
    #         # 'enable_idempotence': True,  # 这个配置项不被支持
    #     })
    #     logger.info("DEV环境：Kafka生产者配置已优化用于调试")
    
    # 消费者配置（已禁用）
    CONSUMER_CONFIG = {
        'bootstrap_servers': KAFKA_BOOTSTRAP_SERVERS,
        'group_id': CONSUMER_GROUP_ID,
        'value_deserializer': lambda v: json.loads(v.decode('utf-8')),
        'key_deserializer': lambda k: k.decode('utf-8') if k else None,
        'auto_offset_reset': 'earliest',
        'enable_auto_commit': True,
        'auto_commit_interval_ms': 1000,
        'session_timeout_ms': 30000,
        'heartbeat_interval_ms': 3000,
        'max_poll_records': 100,
        'fetch_min_bytes': 1,
        'fetch_max_wait_ms': 500,
    }
    
    # 在DEV环境中启用详细日志（已禁用）
    # if os.getenv('APP_ENV', 'dev').lower() == 'dev':
    #     # 启用更详细的配置用于调试
    #     # 注意：request_timeout_ms 必须大于 session_timeout_ms
    #     CONSUMER_CONFIG.update({
    #         'session_timeout_ms': 45000,  # 先设置会话超时时间
    #         'request_timeout_ms': 60000,  # 然后设置请求超时时间（必须更大）
    #         'heartbeat_interval_ms': 3000,  # 心跳间隔
    #         # 移除可能不支持的配置项
    #         # 'max_poll_interval_ms': 300000,  # 最大轮询间隔
    #     })
    #     logger.info("DEV环境：Kafka消费者配置已优化用于调试")


class KafkaManager:
    """Kafka连接管理器（已禁用）"""
    
    _producer: Optional[object] = None  # KafkaProducer
    _consumer: Optional[object] = None  # KafkaConsumer
    
    @classmethod
    def get_producer(cls) -> object:  # -> KafkaProducer:
        """
        获取Kafka生产者实例（已禁用）
        
        :return: KafkaProducer实例
        """
        logger.warning("Kafka功能已被禁用")
        return None
        # if cls._producer is None:
        #     try:
        #         logger.info(f"正在连接Kafka生产者，服务器: {KafkaConfig.KAFKA_BOOTSTRAP_SERVERS}")
        #         cls._producer = KafkaProducer(**KafkaConfig.PRODUCER_CONFIG)
        #         logger.info("Kafka生产者连接成功")
        #         
        #         # 在DEV环境中输出详细配置
        #         if os.getenv('APP_ENV', 'dev').lower() == 'dev':
        #             logger.info(f"Kafka生产者配置: {KafkaConfig.PRODUCER_CONFIG}")
        #             
        #     except KafkaError as e:
        #         logger.error(f"Kafka生产者连接失败: {e}")
        #         logger.error(f"连接配置: {KafkaConfig.PRODUCER_CONFIG}")
        #         raise
        # return cls._producer
    
    @classmethod
    def get_consumer(cls, topics: list = None) -> object:  # -> KafkaConsumer:
        """
        获取Kafka消费者实例（已禁用）
        
        :param topics: 要订阅的主题列表
        :return: KafkaConsumer实例
        """
        logger.warning("Kafka功能已被禁用")
        return None
        # if cls._consumer is None:
        #     try:
        #         config = KafkaConfig.CONSUMER_CONFIG.copy()
        #         logger.info(f"正在连接Kafka消费者，服务器: {KafkaConfig.KAFKA_BOOTSTRAP_SERVERS}")
        #         logger.info(f"消费者组ID: {KafkaConfig.CONSUMER_GROUP_ID}")
        #         
        #         if topics:
        #             cls._consumer = KafkaConsumer(*topics, **config)
        #             logger.info(f"订阅自定义主题: {topics}")
        #         else:
        #             cls._consumer = KafkaConsumer(KafkaConfig.TASK_LOGS_TOPIC, **config)
        #             logger.info(f"订阅默认主题: {KafkaConfig.TASK_LOGS_TOPIC}")
        #         
        #         logger.info(f"Kafka消费者连接成功，订阅主题: {cls._consumer.subscription()}")
        #         
        #         # 在DEV环境中输出详细配置
        #         if os.getenv('APP_ENV', 'dev').lower() == 'dev':
        #             logger.info(f"Kafka消费者配置: {config}")
        #             
        #     except KafkaError as e:
        #         logger.error(f"Kafka消费者连接失败: {e}")
        #         logger.error(f"连接配置: {config}")
        #         raise
        # return cls._consumer
    
    @classmethod
    def close_producer(cls):
        """关闭生产者连接（已禁用）"""
        logger.warning("Kafka功能已被禁用")
        # if cls._producer:
        #     try:
        #         cls._producer.close()
        #         cls._producer = None
        #         logger.info("Kafka生产者连接已关闭")
        #     except Exception as e:
        #         logger.error(f"关闭Kafka生产者连接失败: {e}")
    
    @classmethod
    def close_consumer(cls):
        """关闭消费者连接（已禁用）"""
        logger.warning("Kafka功能已被禁用")
        # if cls._consumer:
        #     try:
        #         cls._consumer.close()
        #         cls._consumer = None
        #         logger.info("Kafka消费者连接已关闭")
        #     except Exception as e:
        #         logger.error(f"关闭Kafka消费者连接失败: {e}")
    
    @classmethod
    def close_all(cls):
        """关闭所有连接（已禁用）"""
        logger.warning("Kafka功能已被禁用")
        # cls.close_producer()
        # cls.close_consumer()


def get_kafka_producer() -> object:  # -> KafkaProducer:
    """
    获取Kafka生产者实例的便捷函数（已禁用）
    
    :return: KafkaProducer实例
    """
    logger.warning("Kafka功能已被禁用")
    return None
    # return KafkaManager.get_producer()


def get_kafka_consumer(topics: list = None) -> object:  # -> KafkaConsumer:
    """
    获取Kafka消费者实例的便捷函数（已禁用）
    
    :param topics: 要订阅的主题列表
    :return: KafkaConsumer实例
    """
    logger.warning("Kafka功能已被禁用")
    return None
    # return KafkaManager.get_consumer(topics)


def send_task_log(task_id: int, service_type: str, message: str, level: str = "INFO", **kwargs):
    """
    发送任务日志到Kafka（已禁用）
    
    :param task_id: 任务ID
    :param service_type: 服务类型 (modeling/simulation/selection)
    :param message: 日志消息
    :param level: 日志级别
    :param kwargs: 其他参数
    """
    logger.warning(f"Kafka功能已被禁用，跳过发送任务日志: task_id={task_id}, message={message}")
    # try:
    #     producer = get_kafka_producer()
    #     
    #     log_data = {
    #         "task_id": task_id,
    #         "service_type": service_type,
    #         "timestamp": kwargs.get("timestamp"),
    #         "level": level,
    #         "message": message,
    #         **kwargs
    #     }
    #     
    #     # 在DEV环境中输出发送详情
    #     if os.getenv('APP_ENV', 'dev').lower() == 'dev':
    #         logger.info(f"🔄 准备发送Kafka消息，任务ID: {task_id}, 主题: {KafkaConfig.TASK_LOGS_TOPIC}")
    #         logger.info(f"📤 消息内容: {log_data}")
    #     
    #     # 发送到任务日志主题
    #     future = producer.send(KafkaConfig.TASK_LOGS_TOPIC, value=log_data)
    #     
    #     # 等待发送完成
    #     record_metadata = future.get(timeout=10)
    #     
    #     # 在DEV环境中输出发送结果
    #     if os.getenv('APP_ENV', 'dev').lower() == 'dev':
    #         logger.info(f"✅ Kafka消息发送成功，任务ID: {task_id}")
    #         logger.info(f"📊 发送详情: 主题={record_metadata.topic}, 分区={record_metadata.partition}, offset={record_metadata.offset}")
    #     
    # except Exception as e:
    #     logger.error(f"❌ 发送任务日志失败: {e}")
    #     logger.error(f"📤 失败的消息内容: {log_data if 'log_data' in locals() else 'N/A'}")


def consume_task_logs(callback_func):
    """
    消费任务日志（已禁用）
    
    :param callback_func: 处理日志的回调函数
    """
    logger.warning("Kafka功能已被禁用，跳过消费任务日志")
    # import time
    # try:
    #     consumer = get_kafka_consumer()
    #     
    #     logger.info(f"🚀 开始消费任务日志，主题: {KafkaConfig.TASK_LOGS_TOPIC}")
    #     logger.info(f"👥 消费者组ID: {KafkaConfig.CONSUMER_GROUP_ID}")
    #     logger.info(f"🌐 Kafka服务器: {KafkaConfig.KAFKA_BOOTSTRAP_SERVERS}")
    #     
    #     # === 新增分区信息重试机制 ===
    #     partitions = None
    #     for retry in range(10):
    #         partitions = consumer.partitions_for_topic(KafkaConfig.TASK_LOGS_TOPIC)
    #         if partitions:
    #             break
    #         logger.warning(f"⚠️ 未找到主题 {KafkaConfig.TASK_LOGS_TOPIC} 的分区信息，重试({retry+1}/10)...")
    #         time.sleep(1)
    #     if not partitions:
    #         logger.error(f"❌ 最终未找到主题 {KafkaConfig.TASK_LOGS_TOPIC} 的分区信息，退出消费线程")
    #         return
    #     # =========================
    # 
    #     logger.info(f"📊 主题 {KafkaConfig.TASK_LOGS_TOPIC} 的分区信息:")
    #     for partition in partitions:
    #         try:
    #             tp = TopicPartition(KafkaConfig.TASK_LOGS_TOPIC, partition)
    #             beginning = consumer.beginning_offsets([tp])
    #             end = consumer.end_offsets([tp])
    #             logger.info(f"📋 分区 {partition}: 开始offset={beginning[tp]}, 结束offset={end[tp]}")
    #         except Exception as e:
    #             logger.warning(f"⚠️ 分区 {partition}: 无法获取offset信息: {e}")
    # 
    #     message_count = 0
    #     logger.info(f"🔄 开始监听消息...")
    # 
    #     for message in consumer:
    #         try:
    #             message_count += 1
    #             log_data = message.value
    #             # 新增：强制打印每条消费到的消息
    #             logger.info(f"【Kafka消费】收到消息: {log_data}")
    #             # 在DEV环境中输出接收详情
    #             if os.getenv('APP_ENV', 'dev').lower() == 'dev':
    #                 logger.info(f"📥 收到Kafka消息 #{message_count}")
    #                 logger.info(f"📋 消息详情: 主题={message.topic}, 分区={message.partition}, offset={message.offset}")
    #                 logger.info(f"📄 消息内容: {log_data}")
    #                 logger.info(f"⏰ 消息时间戳: {message.timestamp}")
    #             logger.info(f"🔑 消息键: {message.key}")
    #             # 调用回调函数处理消息
    #             callback_func(log_data)
    #             # 每10条消息输出一次统计
    #             if message_count % 10 == 0:
    #                 logger.info(f"📊 已处理 {message_count} 条消息")
    #         except Exception as e:
    #             logger.error(f"❌ 处理任务日志失败: {e}")
    #             logger.error(f"📄 失败的消息内容: {message.value if hasattr(message, 'value') else 'N/A'}")
    #             import traceback
    #             logger.error(f"❌ 异常堆栈: {traceback.format_exc()}")
    # except Exception as e:
    #     logger.error(f"💥 消费任务日志失败: {e}")
    #     logger.error(f"💥 错误详情: {str(e)}")
    #     import traceback
    #     logger.error(f"💥 异常堆栈: {traceback.format_exc()}")
    # finally:
    #     logger.info("🛑 停止消费任务日志")
    #     KafkaManager.close_consumer() 