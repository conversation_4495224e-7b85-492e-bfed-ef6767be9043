from sqlalchemy import <PERSON>SO<PERSON>, Column, TIMESTAMP, BigInteger, String, Enum, SmallInteger
from config.database import Base


class RdKnowledgeFiles(Base):
    """
    数据库文件存储表
    """

    __tablename__ = 'rd_knowledge_files'

    file_id = Column(BigInteger, primary_key=True, autoincrement=True, nullable=False, comment='文件id')
    kb_id = Column(BigInteger, nullable=False, comment='关联数据库id')
    project_id = Column(BigInteger, nullable=True, comment='冗余存储便于查询')
    file_type = Column(Enum("model","report","dataset","log","document","archived"), nullable=False, comment='文件类型')
    original_name = Column(String(255), nullable=False, comment='源文件名称')
    storage_path = Column(String(512), nullable=False, comment='OSS存储路径')
    file_size = Column(BigInteger, nullable=True, comment='文件带下')
    file_format = Column(String(50), nullable=True, comment='文件扩展名')
    source_task_id = Column(BigInteger, nullable=True, comment='关联产生此文件的任务')
    file_metadata = Column(JSON, nullable=True, comment='包含模型参数/仿真条件等')
    created_at = Column(TIMESTAMP, nullable=True, comment='创建时间')
    is_deleted = Column(SmallInteger, nullable=True, comment='是否删除')



