import json
import time
from datetime import datetime
from typing import Optional, Dict, Any
from redis import asyncio as aioredis
from utils.log_util import logger


class TaskQueueService:
    """
    可靠任务队列服务

    架构设计：
    1. 双队列模式：主队列(task_queue_new) + 处理队列(task_processing_queue_new)
       - 主队列：存储等待处理的任务
       - 处理队列：存储正在处理的任务，确保任务不丢失

    2. 可靠性保证：
       - 使用lmove原子操作确保任务不丢失，实现FIFO（先进先出）
       - 支持任务重试和故障恢复
       - 处理队列长度检查确保顺序执行
    """

    # Redis键名
    MAIN_QUEUE_KEY = "task_queue_new"                    # 主队列：等待处理的任务
    PROCESSING_QUEUE_KEY = "task_processing_queue_new"   # 处理队列：正在处理的任务（可靠性保证）
    TASK_INFO_KEY = "task_info:{}"                   # 任务信息: 静态数据（参数、结果、时间戳）
    TASK_STATUS_KEY = "task_status:{}"               # 任务状态: 动态数据（状态、进度、消息）

    def __init__(self, redis_client: aioredis.Redis):
        self.redis = redis_client
    
    async def submit_task(self, task_id: int, task_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        提交任务到主队列

        Args:
            task_id: 任务ID
            task_data: 任务数据

        Returns:
            包含队列位置信息的字典
        """
        try:
            # 过滤掉None值和非基本类型，避免Redis存储错误
            filtered_task_data = {}
            for k, v in task_data.items():
                if v is not None:
                    # 确保值是Redis支持的类型
                    if isinstance(v, (str, int, float, bool)):
                        filtered_task_data[k] = str(v)
                    elif isinstance(v, dict):
                        # 将dict转换为JSON字符串
                        filtered_task_data[k] = json.dumps(v)
                    else:
                        # 其他类型转换为字符串
                        filtered_task_data[k] = str(v)

            # 创建任务信息，包含静态元数据（不包含状态）
            task_info = {
                "id": str(task_id),
                "data": json.dumps(filtered_task_data, ensure_ascii=False),
                "created_at": str(time.time()),
                "submitted_at": datetime.now().isoformat()
            }

            logger.info(f"任务 {task_id} 准备提交到主队列: {filtered_task_data}")

            # 将任务详情存储到Redis Hash中
            task_info_key = self.TASK_INFO_KEY.format(task_id)
            await self.redis.hset(task_info_key, mapping=task_info)

            # 初始化任务状态
            task_status_key = self.TASK_STATUS_KEY.format(task_id)
            await self.redis.hset(task_status_key, mapping={
                "status": "queued",
                "progress": "0",
                "message": "任务已提交到队列",
                "updated_at": str(time.time())
            })

            # 将任务ID添加到主队列尾部
            await self.redis.rpush(self.MAIN_QUEUE_KEY, task_id)

            # 获取队列长度（即任务位置）
            queue_length = await self.redis.llen(self.MAIN_QUEUE_KEY)

            logger.info(f"任务 {task_id} 已提交到主队列，当前位置: {queue_length}")

            return {
                "task_id": task_id,
                "queue_position": queue_length,
                "status": "queued",
                "submitted_at": datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"提交任务到主队列失败: {e}")
            raise
    
    async def get_next_task(self) -> Optional[Dict[str, Any]]:
        """
        从主队列获取下一个任务并移动到处理队列
        当且仅当处理队列为空时，才能从主队列获取任务

        Returns:
            任务数据字典，如果队列为空或处理队列不为空则返回None
        """
        try:
            # 检查处理队列是否为空
            processing_queue_length = await self.redis.llen(self.PROCESSING_QUEUE_KEY)
            if processing_queue_length > 0:
                logger.info(f"⚠️ 处理队列不为空 (长度: {processing_queue_length})，不能获取新任务")
                return None

            # 使用 lmove 原子操作：从主队列左端弹出（FIFO），推入处理队列左端
            # Redis 6.2+ 支持 lmove，如果不支持则使用 lpop + lpush 组合
            try:
                task_id = await self.redis.lmove(self.MAIN_QUEUE_KEY, self.PROCESSING_QUEUE_KEY, "LEFT", "LEFT")
            except Exception:
                # 降级到非原子操作（Redis版本较低时）
                task_id = await self.redis.lpop(self.MAIN_QUEUE_KEY)
                if task_id:
                    await self.redis.lpush(self.PROCESSING_QUEUE_KEY, task_id)

            if not task_id:
                return None

            task_id = int(task_id)
            logger.info(f"📤 从主队列移动任务到处理队列: {task_id}")

            # 获取任务详细信息
            task_info_key = self.TASK_INFO_KEY.format(task_id)
            task_data = await self.redis.hgetall(task_info_key)

            if not task_data:
                logger.warning(f"⚠️ 任务 {task_id} 的信息不存在，从处理队列移除")
                await self.redis.lrem(self.PROCESSING_QUEUE_KEY, 1, task_id)
                return None

            # 解析任务数据
            try:
                if 'data' in task_data:
                    parsed_data = json.loads(task_data['data'])
                    # 解析parameters字段
                    if 'parameters' in parsed_data and parsed_data['parameters']:
                        if isinstance(parsed_data['parameters'], str):
                            parsed_data['parameters'] = json.loads(parsed_data['parameters'])
                    task_data.update(parsed_data)
            except json.JSONDecodeError as e:
                logger.warning(f"⚠️ 任务 {task_id} 数据解析失败: {e}")

            # 只更新task_status（动态状态数据）
            await self._update_task_status(task_id, "processing", "任务开始处理")

            logger.info(f"📋 获取任务 {task_id} 并移动到处理队列")
            return task_data

        except Exception as e:
            logger.error(f"❌ 从队列获取任务失败: {e}")
            return None

    async def handle_task_callback(self, task_id: int, success: bool, result: Any = None) -> bool:
        """
        处理外部服务的回调（基于 queue_example.py 的回调机制）

        Args:
            task_id: 任务ID
            success: 是否成功
            result: 结果数据

        Returns:
            是否处理成功
        """
        try:
            # 检查任务是否存在于Redis中
            task_info_key = self.TASK_INFO_KEY.format(task_id)
            task_exists_in_redis = await self.redis.hexists(task_info_key, "id")

            if task_exists_in_redis:
                # 任务在Redis中，按正常队列流程处理
                if success:
                    await self._mark_task_completed(task_id, result)
                else:
                    await self._mark_task_failed(task_id, result)

                # 从处理队列中移除任务
                removed_count = await self.redis.lrem(self.PROCESSING_QUEUE_KEY, 1, task_id)
                if removed_count > 0:
                    logger.info(f"✅ 任务 {task_id} 已从处理队列移除")
                else:
                    logger.warning(f"⚠️ 任务 {task_id} 不在处理队列中")
            else:
                # 任务不在Redis中，可能是不需要队列的任务（如选型任务）
                logger.info(f"📝 任务 {task_id} 不在Redis队列中，可能是直接执行的任务（如选型任务）")
                # 对于不需要队列的任务，我们仍然返回成功，让上层处理数据库状态更新

            return True

        except Exception as e:
            logger.error(f"❌ 处理任务回调失败，任务 {task_id}: {e}")
            return False

    async def _mark_task_completed(self, task_id: int, result: Any = None):
        """标记任务为完成"""
        try:
            # 只在task_info中更新结果和完成时间（静态数据）
            task_info_updates = {
                "completed_at": str(time.time()),
                "result": json.dumps(result, ensure_ascii=False) if result else None
            }

            # 更新任务信息（不包含状态）
            task_info_key = self.TASK_INFO_KEY.format(task_id)
            await self.redis.hset(task_info_key, mapping=task_info_updates)

            # 在task_status中更新状态（动态数据）
            await self._update_task_status(task_id, "completed", "任务执行完成")

            logger.info(f"✅ 任务 {task_id} 标记为完成")

        except Exception as e:
            logger.error(f"❌ 标记任务完成失败，任务 {task_id}: {e}")

    async def _mark_task_failed(self, task_id: int, error: Any = None):
        """标记任务为失败"""
        try:
            # 只在task_info中更新错误信息和失败时间（静态数据）
            task_info_updates = {
                "failed_at": str(time.time()),
                "error": str(error) if error else None
            }

            # 更新任务信息（不包含状态）
            task_info_key = self.TASK_INFO_KEY.format(task_id)
            await self.redis.hset(task_info_key, mapping=task_info_updates)

            # 在task_status中更新状态（动态数据）
            error_message = str(error) if error else "任务执行失败"
            await self._update_task_status(task_id, "failed", error_message)

            logger.info(f"❌ 任务 {task_id} 标记为失败: {error_message}")

        except Exception as e:
            logger.error(f"❌ 标记任务失败状态失败，任务 {task_id}: {e}")

    async def _update_task_status(self, task_id: int, status: str, message: str = "",
                                 progress: int = None, stage: str = None):
        """
        更新任务状态（专门用于动态状态数据）

        Args:
            task_id: 任务ID
            status: 任务状态
            message: 状态消息
            progress: 进度百分比（可选）
            stage: 当前阶段（可选）
        """
        try:
            task_status_key = self.TASK_STATUS_KEY.format(task_id)
            status_data = {
                "status": status,
                "message": message,
                "updated_at": str(time.time())
            }

            # 只有提供了进度时才更新
            if progress is not None:
                status_data["progress"] = str(progress)

            # 只有提供了阶段时才更新
            if stage is not None:
                status_data["stage"] = stage

            await self.redis.hset(task_status_key, mapping=status_data)

        except Exception as e:
            logger.error(f"❌ 更新任务状态失败，任务 {task_id}: {e}")

    async def _cleanup_task_data(self, task_id: int):
        """清理任务相关的Redis数据"""
        try:
            # 删除任务信息
            task_info_key = self.TASK_INFO_KEY.format(task_id)
            await self.redis.delete(task_info_key)

            # 删除任务状态
            task_status_key = self.TASK_STATUS_KEY.format(task_id)
            await self.redis.delete(task_status_key)

            logger.info(f"🧹 已清理任务 {task_id} 的Redis数据")

        except Exception as e:
            logger.error(f"❌ 清理任务数据失败，任务 {task_id}: {e}")

    async def get_queue_position(self, task_id: int) -> Optional[int]:
        """
        获取任务在主队列中的位置

        Args:
            task_id: 任务ID

        Returns:
            队列位置（从1开始），如果任务不在队列中则返回None
        """
        try:
            # 获取任务在主队列中的位置
            position = await self.redis.lpos(self.MAIN_QUEUE_KEY, task_id)

            if position is not None:
                return position + 1  # 转换为从1开始的位置
            else:
                return None

        except Exception as e:
            logger.error(f"❌ 获取任务队列位置失败: {e}")
            return None

    async def get_queue_length(self) -> int:
        """
        获取主队列长度

        Returns:
            主队列中的任务数量
        """
        try:
            return await self.redis.llen(self.MAIN_QUEUE_KEY)
        except Exception as e:
            logger.error(f"❌ 获取主队列长度失败: {e}")
            return 0

    async def get_processing_queue_length(self) -> int:
        """
        获取处理队列长度

        Returns:
            处理队列中的任务数量
        """
        try:
            return await self.redis.llen(self.PROCESSING_QUEUE_KEY)
        except Exception as e:
            logger.error(f"❌ 获取处理队列长度失败: {e}")
            return 0
    
    async def remove_task_from_queue(self, task_id: int) -> bool:
        """
        从队列中移除任务（支持双队列）

        Args:
            task_id: 任务ID

        Returns:
            是否成功移除
        """
        try:
            removed_count = 0

            # 从主队列中移除任务ID
            main_removed = await self.redis.lrem(self.MAIN_QUEUE_KEY, 1, task_id)
            removed_count += main_removed

            # 从处理队列中移除任务ID
            processing_removed = await self.redis.lrem(self.PROCESSING_QUEUE_KEY, 1, task_id)
            removed_count += processing_removed

            # 清理任务相关数据
            await self._cleanup_task_data(task_id)

            if removed_count > 0:
                logger.info(f"✅ 从队列移除任务 {task_id} (主队列: {main_removed}, 处理队列: {processing_removed})")
            else:
                logger.warning(f"⚠️ 任务 {task_id} 不在任何队列中")

            return removed_count > 0

        except Exception as e:
            logger.error(f"❌ 从队列移除任务失败: {e}")
            return False
    
    async def clear_queue(self) -> bool:
        """
        清空所有队列（主队列和处理队列）

        Returns:
            是否成功清空
        """
        try:
            # 获取主队列中的所有任务ID
            main_task_ids = await self.redis.lrange(self.MAIN_QUEUE_KEY, 0, -1)

            # 获取处理队列中的所有任务ID
            processing_task_ids = await self.redis.lrange(self.PROCESSING_QUEUE_KEY, 0, -1)

            # 合并所有任务ID
            all_task_ids = set(main_task_ids + processing_task_ids)

            # 清理所有任务数据
            for task_id in all_task_ids:
                await self._cleanup_task_data(int(task_id))

            # 清空主队列
            await self.redis.delete(self.MAIN_QUEUE_KEY)

            # 清空处理队列
            await self.redis.delete(self.PROCESSING_QUEUE_KEY)

            logger.info(f"🧹 清空所有队列，移除了 {len(all_task_ids)} 个任务 (主队列: {len(main_task_ids)}, 处理队列: {len(processing_task_ids)})")
            return True

        except Exception as e:
            logger.error(f"❌ 清空队列失败: {e}")
            return False
    
    async def get_queue_status(self) -> Dict[str, Any]:
        """
        获取队列状态信息（包括主队列和处理队列）

        Returns:
            队列状态字典
        """
        try:
            main_queue_length = await self.get_queue_length()
            processing_queue_length = await self.get_processing_queue_length()

            # 获取主队列前几个任务的信息
            main_task_ids = await self.redis.lrange(self.MAIN_QUEUE_KEY, 0, 4)  # 前5个任务
            main_queue_preview = []

            for task_id in main_task_ids:
                task_info_key = self.TASK_INFO_KEY.format(task_id)
                task_data = await self.redis.hgetall(task_info_key)
                if task_data:
                    # 解析任务数据
                    try:
                        parsed_data = json.loads(task_data.get('data', '{}'))
                        task_name = parsed_data.get("task_name", "")
                    except:
                        task_name = task_data.get("task_name", "")

                    main_queue_preview.append({
                        "task_id": int(task_id),
                        "task_name": task_name,
                        "submitted_at": task_data.get("submitted_at", "")
                    })

            # 获取处理队列前几个任务的信息
            processing_task_ids = await self.redis.lrange(self.PROCESSING_QUEUE_KEY, 0, 4)  # 前5个任务
            processing_queue_preview = []

            for task_id in processing_task_ids:
                task_info_key = self.TASK_INFO_KEY.format(task_id)
                task_data = await self.redis.hgetall(task_info_key)
                if task_data:
                    # 解析任务数据
                    try:
                        parsed_data = json.loads(task_data.get('data', '{}'))
                        task_name = parsed_data.get("task_name", "")
                    except:
                        task_name = task_data.get("task_name", "")

                    processing_queue_preview.append({
                        "task_id": int(task_id),
                        "task_name": task_name,
                        "submitted_at": task_data.get("submitted_at", ""),
                        "status": task_data.get("status", "processing")
                    })

            return {
                "main_queue_length": main_queue_length,
                "processing_queue_length": processing_queue_length,
                "total_queue_length": main_queue_length + processing_queue_length,
                "main_queue_preview": main_queue_preview,
                "processing_queue_preview": processing_queue_preview,
                "last_updated": datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"❌ 获取队列状态失败: {e}")
            return {
                "main_queue_length": 0,
                "processing_queue_length": 0,
                "total_queue_length": 0,
                "main_queue_preview": [],
                "processing_queue_preview": [],
                "last_updated": datetime.now().isoformat()
            }

    async def return_task_to_queue(self, task_id: int) -> bool:
        """
        将任务从处理队列返回到主队列（用于重试）

        Args:
            task_id: 任务ID

        Returns:
            是否成功返回
        """
        try:
            # 从处理队列中移除
            removed_count = await self.redis.lrem(self.PROCESSING_QUEUE_KEY, 1, task_id)

            if removed_count > 0:
                # 重新添加到主队列头部（优先处理）
                await self.redis.lpush(self.MAIN_QUEUE_KEY, task_id)

                # 更新任务状态为重试
                await self._update_task_status(task_id, "retrying", "任务重新排队等待处理")

                logger.info(f"🔄 任务 {task_id} 已从处理队列返回到主队列")
                return True
            else:
                logger.warning(f"⚠️ 任务 {task_id} 不在处理队列中，无法返回")
                return False

        except Exception as e:
            logger.error(f"❌ 返回任务到队列失败，任务 {task_id}: {e}")
            return False