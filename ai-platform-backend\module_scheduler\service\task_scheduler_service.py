import json
import asyncio
import requests
import sys
import traceback
from datetime import datetime
from typing import Optional, Dict, Any
from redis import asyncio as aioredis
from sqlalchemy.ext.asyncio import AsyncSession
from module_business.dao.tasks_dao import TasksDao
from module_business.dao.tools_dao import ToolsDao
from module_business.entity.vo.tasks_vo import TasksModel
from module_scheduler.service.task_queue_service import TaskQueueService
from module_business.controller.task_log_controller import push_task_log
from utils.log_util import logger
from sqlalchemy import select
from module_business.entity.do.knowledge_bases_do import RdKnowledgeBases


class TaskSchedulerService:
    """
    任务调度服务
    负责从队列取任务并调用外部服务
    """
    
    def __init__(self, redis_client: aioredis.Redis, db_session: AsyncSession):
        self.redis = redis_client
        self.db = db_session
        self.queue_service = TaskQueueService(redis_client)
        self.is_running = False
        self.scheduler_task = None

    async def _get_fresh_db_session(self):
        """
        获取新的数据库会话，确保数据一致性
        """
        try:
            from config.get_db import AsyncSessionLocal
            return AsyncSessionLocal()
        except Exception as e:
            logger.error(f"创建新数据库会话失败: {e}")
            return self.db
    
    async def start_scheduler(self):
        """
        启动任务调度器
        """
        if self.is_running:
            logger.warning("任务调度器已经在运行")
            return True
        
        try:
            self.is_running = True
            self.scheduler_task = asyncio.create_task(self._scheduler_loop())
            logger.info("任务调度器已启动")
            logger.info(f"调度器状态: is_running={self.is_running}, task={self.scheduler_task}")
            return True
        except Exception as e:
            logger.error(f"启动调度器失败: {e}")
            self.is_running = False
            return False
    
    async def stop_scheduler(self):
        """
        停止任务调度器
        """
        if not self.is_running:
            return
        
        self.is_running = False
        if self.scheduler_task:
            self.scheduler_task.cancel()
            try:
                await self.scheduler_task
            except asyncio.CancelledError:
                pass
        logger.info("任务调度器已停止")
    
    async def _scheduler_loop(self):
        """
        调度器主循环
        """
        logger.info("🚀 调度器主循环已启动")
        loop_count = 0
        while self.is_running:
            try:
                loop_count += 1
                
                # 每10次循环记录一次心跳，并进行健康检查
                if loop_count % 10 == 0:
                    main_queue_length = await self.queue_service.get_queue_length()
                    processing_queue_length = await self.queue_service.get_processing_queue_length()
                    logger.info(f"💓 调度器心跳 - 循环次数: {loop_count}, 主队列: {main_queue_length}, 处理队列: {processing_queue_length}")

                    # 每100次循环进行一次健康检查和清理
                    if loop_count % 100 == 0:
                        await self._health_check_and_cleanup()

                # 从队列获取下一个任务
                task_data = await self.queue_service.get_next_task()

                if not task_data:
                    # 队列为空，等待一段时间
                    await asyncio.sleep(5)
                    continue
                
                if task_data:
                    task_id = task_data.get('task_id')
                    logger.info(f"📋 从队列获取到任务: {task_id}")

                    # 立即更新任务状态为running，避免前端显示"调度中"
                    try:
                        # 使用独立的数据库会话来避免事务冲突
                        from config.get_db import AsyncSessionLocal
                        async with AsyncSessionLocal() as update_session:
                            update_data = {
                                "task_id": task_id,
                                "status": "running",
                                "started_at": datetime.now(),
                                "updated_at": datetime.now()
                            }
                            await TasksDao.edit_tasks_dao(update_session, update_data)
                            await update_session.commit()

                        logger.info(f"🚀 任务 {task_id} 状态已更新为running")

                        # 处理任务
                        await self._process_task(task_data)
                        # logger.info(f"✅ 任务 {task_id} 处理完成")

                    except Exception as e:
                        logger.error(f"❌ 处理任务 {task_id} 时发生异常: {e}")
                        logger.error(f"❌ 异常堆栈: {traceback.format_exc()}")
                        # 处理失败时更新状态
                        try:
                            await self._update_task_status(task_id, "failed", error_message=str(e))
                        except Exception as update_error:
                            logger.error(f"❌ 更新失败状态时出错: {update_error}")

                        # 任务失败时从处理队列移除
                        try:
                            await self.redis.lrem("task_processing_queue_new", 1, task_id)
                            logger.info(f"🧹 失败任务 {task_id} 已从处理队列移除")
                        except Exception as cleanup_error:
                            logger.warning(f"⚠️ 从处理队列移除失败任务失败: {cleanup_error}")

                    # 注意：成功的异步任务不在这里移除
                    # 异步任务应该保留在处理队列中直到收到完成回调
                else:
                    # 队列为空，等待一段时间
                    await asyncio.sleep(5)
                    
            except asyncio.CancelledError:
                logger.info("🛑 调度器主循环被取消")
                break
            except Exception as e:
                logger.error(f"💥 调度器循环出错: {e}")
                logger.error(f"💥 异常堆栈: {traceback.format_exc()}")
                await asyncio.sleep(10)  # 出错后等待更长时间
        
        logger.info("🛑 调度器主循环已停止")
    
    async def _process_task(self, task_data: Dict[str, Any]):
        """
        处理单个任务

        Args:
            task_data: 任务数据
        """
        task_id = int(task_data.get('task_id'))

        try:
            logger.info(f"开始处理任务 {task_id}")
            logger.info(f"任务数据: {task_data}")

            # 检查任务当前状态，避免重复处理已完成的任务
            try:
                logger.info(f"🔍 查询任务 {task_id} 的数据库信息...")

                # 使用独立的数据库会话进行查询，确保数据一致性
                async with await self._get_fresh_db_session() as fresh_db:
                    task_info = await TasksDao.get_tasks_detail_by_id(fresh_db, task_id)
                    if not task_info:
                        logger.error(f"❌ 任务 {task_id} 在数据库中不存在，但队列中有数据")
                        logger.error(f"📋 队列数据: {task_data}")

                        # 紧急调试：使用独立会话直接查询数据库
                        try:
                            from sqlalchemy import text
                            result = await fresh_db.execute(text("SELECT task_id, status FROM rd_tasks WHERE task_id = :task_id"), {"task_id": task_id})
                            row = result.fetchone()
                            if row:
                                logger.error(f"🔍 直接SQL查询结果: task_id={row[0]}, status={row[1]}")
                                # 如果直接查询找到了，说明是会话问题，继续处理
                                logger.info(f"✅ 直接SQL查询找到任务，继续处理")
                            else:
                                logger.error(f"🔍 直接SQL查询也没有找到任务 {task_id}")
                                # 任务真的不存在，跳过处理
                                logger.warning(f"⚠️ 任务 {task_id} 确实不存在，跳过处理")
                                return
                        except Exception as sql_error:
                            logger.error(f"🔍 直接SQL查询失败: {sql_error}")

                        # 尝试使用队列中的数据继续处理
                        logger.info(f"⚠️ 尝试使用队列中的数据继续处理任务 {task_id}")
                    else:
                        logger.info(f"✅ 成功获取任务 {task_id} 信息，状态: {task_info.status}")
                        current_status = task_info.status
                        if current_status in ['completed', 'failed']:
                            logger.info(f"⏭️ 任务 {task_id} 已处于终态 ({current_status})，跳过处理")
                            return
            except Exception as db_error:
                logger.error(f"❌ 查询任务 {task_id} 数据库信息失败: {db_error}")
                logger.error(f"❌ 数据库查询异常堆栈: {traceback.format_exc()}")
                logger.info(f"⚠️ 使用队列中的数据继续处理任务 {task_id}")
                # 继续使用队列中的数据处理

            # 检查数据库连接状态
            try:
                if not self.db.is_active:
                    logger.error(f"数据库连接已关闭，尝试重新连接")
                    # 尝试执行一个简单查询来测试连接
                    await self.db.execute("SELECT 1")
                    logger.info(f"数据库连接测试成功")
            except Exception as db_test_error:
                logger.error(f"数据库连接测试失败: {db_test_error}")
                await self._update_task_status(task_id, "failed", use_transaction=False, error_message="数据库连接失败")
                return
            
            # 推送日志到前端
            try:
                await push_task_log(task_id, "任务开始执行", "INFO")
            except Exception as log_error:
                logger.warning(f"推送任务日志失败: {log_error}")
            
            # 更新任务状态为运行中（不使用独立事务）
            await self._update_task_status(task_id, "running", started_at=datetime.now(), use_transaction=False)
            
            # 获取工具信息
            tool_id = task_data.get('tool_id')
            logger.info(f"任务 {task_id} 的工具ID: {tool_id} (类型: {type(tool_id)})")
            if not tool_id:
                logger.error(f"任务 {task_id} 缺少工具ID")
                await self._update_task_status(task_id, "failed", use_transaction=False)
                return

            # 确保tool_id是整数类型
            try:
                tool_id = int(tool_id)
                logger.info(f"工具ID转换为整数: {tool_id}")
            except (ValueError, TypeError) as e:
                logger.error(f"工具ID转换失败: {e}")
                await self._update_task_status(task_id, "failed", use_transaction=False)
                return

            try:
                tool_info = await ToolsDao.get_tools_detail_by_id(self.db, tool_id)
                if not tool_info:
                    logger.error(f"工具 {tool_id} 信息为空")
            except Exception as tool_error:
                logger.error(f"获取工具 {tool_id} 信息失败: {tool_error}")
                logger.error(f"工具查询异常堆栈: {traceback.format_exc()}")
                await self._update_task_status(task_id, "failed", use_transaction=False)
                return
            
            if not tool_info:
                logger.error(f"任务 {task_id} 的工具 {tool_id} 不存在")
                await self._update_task_status(task_id, "failed", use_transaction=False)
                return
            
            # 调用外部服务
            executable_api = tool_info.executable_api
            logger.info(f"工具 {tool_id} 的可执行API: {executable_api}")
            if not executable_api:
                logger.error(f"任务 {task_id} 的工具 {tool_id} 缺少可执行API")
                await self._update_task_status(task_id, "failed", use_transaction=False)
                return
            
            # 获取知识库ID
            kb_id = None
            project_id = task_data.get('project_id')
            if project_id:
                # 确保project_id是整数类型
                try:
                    project_id = int(project_id)
                except (ValueError, TypeError):
                    logger.error(f"任务 {task_id} 的project_id '{project_id}' 无法转换为整数")
                    project_id = None
                    
            if project_id:
                try:
                    # 刷新会话以确保能看到最新的知识库数据
                    await self.db.commit()
                    
                    query = (
                        select(RdKnowledgeBases)
                        .where(
                            RdKnowledgeBases.project_id == project_id,
                            RdKnowledgeBases.is_deleted == 0
                        )
                        .order_by(RdKnowledgeBases.kb_id)
                        .limit(1)
                    )
                    
                    result_query = await self.db.execute(query)
                    kb_obj = result_query.scalars().first()
                    if kb_obj:
                        kb_id = kb_obj.kb_id
                        logger.info(f"任务 {task_id} 获取到知识库ID: {kb_id}")
                    else:
                        logger.warning(f"任务 {task_id} 未找到关联的知识库")
                except Exception as e:
                    logger.error(f"获取知识库ID失败: {e}")
            
            # 调用外部服务（在后台线程中执行同步调用）
            try:
                # 在后台线程中执行外部服务调用，避免阻塞事件循环
                import threading

                def call_external_service_sync(request_data):
                    """在后台线程中执行同步的外部服务调用"""
                    import requests
                    import json
                    
                    try:
                        # 构建请求数据
                        headers = {
                            'Content-Type': 'application/json',
                            'User-Agent': 'AI-Platform-Scheduler/1.0'
                        }
                        
                        # 添加进度回调URL
                        from config.constant import get_task_callback_url
                        callback_url = get_task_callback_url(task_id)
                        request_data['callback_url'] = callback_url
                        request_data['progress_callback'] = True  # 启用进度回调
                        
                        logger.info(f"调用外部服务: {executable_api}")
                        logger.info(f"请求数据: {json.dumps(request_data, ensure_ascii=False)}")
                        
                        # 发送请求
                        response = requests.post(
                            executable_api,
                            json=request_data,
                            headers=headers,
                            timeout=30  # 30秒超时
                        )
                        
                        logger.info(f"外部服务响应状态码: {response.status_code}")
                        logger.info(f"外部服务响应内容: {response.text}")
                        
                        if response.status_code == 200:
                            try:
                                result = response.json()
                                return {
                                    'success': True,
                                    'data': result,
                                    'status_code': response.status_code
                                }
                            except json.JSONDecodeError:
                                return {
                                    'success': True,
                                    'data': {'message': response.text},
                                    'status_code': response.status_code
                                }
                        else:
                            return {
                                'success': False,
                                'error': f'HTTP {response.status_code}: {response.text}',
                                'status_code': response.status_code
                            }
                            
                    except requests.exceptions.Timeout:
                        return {
                            'success': False,
                            'error': '请求超时',
                            'status_code': 408
                        }
                    except requests.exceptions.RequestException as e:
                        return {
                            'success': False,
                            'error': f'请求失败: {str(e)}',
                            'status_code': 500
                        }
                    except Exception as e:
                        return {
                            'success': False,
                            'error': f'未知错误: {str(e)}',
                            'status_code': 500
                        }
                
                # 准备请求数据
                from config.constant import get_task_callback_url
                request_data = {
                    "task_id": task_id,
                    "task_name": task_data.get('task_name'),
                    "parameters": task_data.get('parameters', {}),
                    "callback_url": get_task_callback_url(task_id),
                    "submitted_at": datetime.now().isoformat()
                }
                
                # 添加项目ID
                if task_data.get('project_id'):
                    request_data["project_id"] = task_data.get('project_id')
                
                # 添加源任务ID（当前任务ID作为源任务ID）
                request_data["source_task_id"] = task_id
                
                # 添加知识库ID（如果存在）
                if kb_id:
                    request_data["kb_id"] = kb_id
                
                # 添加创建者信息
                created_by = None
                if task_data.get('submitted_by'):
                    # 如果队列中有submitted_by字段，直接使用
                    logger.info(f"任务 {task_id} 使用 submitted_by 字段获取用户信息: {task_data.get('submitted_by')}")
                    created_by = await self._get_user_name(task_data.get('submitted_by'))
                elif task_data.get('assigned_to'):
                    # 否则使用assigned_to字段
                    logger.info(f"任务 {task_id} 使用 assigned_to 字段获取用户信息: {task_data.get('assigned_to')}")
                    created_by = await self._get_user_name(task_data.get('assigned_to'))
                else:
                    logger.warning(f"任务 {task_id} 没有找到用户信息字段")
                
                if created_by:
                    request_data["created_by"] = created_by
                    logger.info(f"任务 {task_id} 设置创建者: {created_by}")
                else:
                    logger.warning(f"任务 {task_id} 无法获取创建者信息")
                
                # 处理文件路径参数
                parameters = request_data.get('parameters', {})
                if parameters:
                    # 检查并处理文件路径参数
                    for key, value in parameters.items():
                        if isinstance(value, str) and ('modelFilePath' in key or 'path' in key.lower()):
                            if value:
                                # 如果已经是完整URL，直接使用
                                if value.startswith('http'):
                                    logger.info(f"📁 文件路径已是完整URL: {key} = {value}")
                                else:
                                    # 构建完整的下载URL
                                    from config.constant import get_file_download_url
                                    full_url = get_file_download_url(value)
                                    parameters[key] = full_url
                                    logger.info(f"📁 文件路径转换为完整URL: {key} = {full_url}")
                
                # 更新转换后的参数
                request_data["parameters"] = parameters
                
                # 在主线程中先输出关键信息
                logger.info(f"🚀 准备调用外部服务，任务 {task_id}")
                logger.info(f"📡 外部服务API: {executable_api}")
                logger.info(f"📤 请求参数: {json.dumps(request_data, ensure_ascii=False, indent=2)}")
                
                # 在后台线程中执行
                thread = threading.Thread(target=call_external_service_sync, args=(request_data,), daemon=True)
                thread.start()
                
                logger.info(f"🚀 任务 {task_id} 已提交到外部服务，等待回调")
                
            except Exception as e:
                logger.error(f"调用外部服务异常，任务 {task_id}: {e}")
                # 更新任务状态
                try:
                    await self._update_task_status(task_id, "failed")
                    await push_task_log(task_id, f"调用外部服务异常: {str(e)}", "ERROR")
                except Exception as log_error:
                    logger.warning(f"推送任务日志失败: {log_error}")
                
        except Exception as e:
            logger.error(f"处理任务 {task_id} 失败: {e}")
            await self._update_task_status(task_id, "failed")
            # 推送异常日志
            try:
                await push_task_log(task_id, f"任务处理异常: {str(e)}", "ERROR")
            except Exception as log_error:
                logger.warning(f"推送任务日志失败: {log_error}")
    

    
    async def _update_task_status(self, task_id: int, status: str, use_transaction: bool = True, **kwargs):
        """
        更新任务状态

        Args:
            task_id: 任务ID
            status: 新状态
            use_transaction: 是否使用独立事务（如果已在事务中则设为False）
            **kwargs: 其他更新字段
        """
        max_retries = 3 if use_transaction else 1  # 如果在现有事务中，不重试
        retry_delay = 1  # 秒

        for attempt in range(max_retries):
            try:
                update_data = {
                    "task_id": task_id,
                    "status": status,
                    "updated_at": datetime.now()
                }

                # 添加其他字段
                if "started_at" in kwargs:
                    update_data["started_at"] = kwargs["started_at"]
                if "completed_at" in kwargs:
                    update_data["completed_at"] = kwargs["completed_at"]
                if "progress" in kwargs:
                    update_data["progress"] = kwargs["progress"]

                if use_transaction:
                    # 使用独立的事务
                    async with self.db.begin():
                        await TasksDao.edit_tasks_dao(self.db, update_data)
                else:
                    # 在现有事务中执行
                    await TasksDao.edit_tasks_dao(self.db, update_data)

                logger.info(f"任务 {task_id} 状态更新为: {status}")
                return  # 成功则退出

            except Exception as e:
                logger.warning(f"更新任务状态失败，任务 {task_id}，尝试 {attempt + 1}/{max_retries}: {e}")

                if attempt < max_retries - 1:
                    # 等待后重试
                    await asyncio.sleep(retry_delay * (attempt + 1))
                else:
                    # 最后一次尝试失败
                    logger.error(f"更新任务状态最终失败，任务 {task_id}: {e}")
                    if use_transaction:
                        try:
                            await self.db.rollback()
                        except:
                            pass
    


    async def handle_task_callback(self, task_id: int, callback_data: Dict[str, Any]) -> bool:
        """
        处理任务回调
        
        Args:
            task_id: 任务ID
            callback_data: 回调数据
            
        Returns:
            是否处理成功
        """
        try:
            await self.db.begin()
            
            # 更新任务状态
            status = callback_data.get('status')
            progress = callback_data.get('progress', 0)
            message = callback_data.get('message', '')
            result = callback_data.get('result')
            error_message = callback_data.get('error_message')
            
            # 更新任务状态
            update_kwargs = {
                "progress": progress,
                "message": message,
                "error_message": error_message
            }
            
            # 如果任务完成，设置完成时间
            if status == 'completed':
                update_kwargs["completed_at"] = datetime.now()
            
            await self._update_task_status(task_id, status, use_transaction=False, **update_kwargs)
            
            # 如果任务完成且有结果，处理结果文件
            if status == 'completed' and result:
                logger.info(f"任务 {task_id} 完成，开始处理结果文件，result数据: {result}")

                # 从任务数据中获取知识库ID
                kb_id = None
                task_info = await TasksDao.get_tasks_detail_by_id(self.db, task_id)
                if task_info and task_info.project_id:
                    query = (
                        select(RdKnowledgeBases)
                        .where(
                            RdKnowledgeBases.project_id == task_info.project_id,
                            RdKnowledgeBases.is_deleted == 0
                        )
                        .order_by(RdKnowledgeBases.kb_id)
                        .limit(1)
                    )

                    result_query = await self.db.execute(query)
                    kb_obj = result_query.scalars().first()
                    if kb_obj:
                        kb_id = kb_obj.kb_id
                        logger.info(f"任务 {task_id} 找到知识库ID: {kb_id}")
                    else:
                        logger.error(f"任务 {task_id} 项目 {task_info.project_id} 没有找到知识库")
                else:
                    logger.error(f"任务 {task_id} 没有找到任务信息或项目ID")

                await self._handle_result_files(task_id, result, kb_id)
            elif status == 'completed':
                logger.warning(f"任务 {task_id} 完成但没有result数据")
            else:
                logger.info(f"任务 {task_id} 状态为 {status}，跳过文件处理")
            
            await self.db.commit()

            # 如果任务完成，使用队列服务的回调处理机制
            if status in ['completed', 'failed']:
                try:
                    success = status == 'completed'
                    result_data = result if success else error_message

                    # 使用新的队列回调处理机制
                    callback_success = await self.queue_service.handle_task_callback(
                        task_id, success, result_data
                    )

                    if callback_success:
                        logger.info(f"✅ 任务 {task_id} 队列回调处理成功")
                    else:
                        logger.warning(f"⚠️ 任务 {task_id} 队列回调处理失败")

                except Exception as e:
                    logger.warning(f"⚠️ 队列回调处理失败，任务 {task_id}: {e}")

            logger.info(f"✅ 任务回调处理成功，任务 {task_id}")
            return True
            
        except Exception as e:
            logger.error(f"处理任务回调失败，任务 {task_id}: {e}")
            await self.db.rollback()
            return False
    
    async def _handle_result_files(self, task_id: int, result: Dict[str, Any], kb_id: int = None):
        """
        处理任务结果文件
        
        Args:
            task_id: 任务ID
            result: 任务结果
            kb_id: 知识库ID
        """
        try:
            logger.info(f"任务 {task_id} 开始处理结果文件，result结构: {result}")

            # 检查是否有输出文件信息
            output_files = result.get('output_files', {})
            logger.info(f"任务 {task_id} 提取的output_files: {output_files}")

            if not output_files:
                logger.info(f"任务 {task_id} 没有输出文件")
                # 即使没有输出文件，也要检查是否有已上传到MinIO的文件
                await self._check_existing_files(task_id, kb_id)
                return
            
            # 检查知识库ID
            if not kb_id:
                logger.error(f"任务 {task_id} 没有提供知识库ID，跳过文件处理")
                return
            

            
            # 导入必要的模块
            import os
            from utils.minio_util import MinioUtil
            from module_business.entity.vo.files_vo import FilesModel
            from module_business.service.files_service import FilesService
            
            # 获取MinIO工具实例
            minio_util = MinioUtil.get_instance()
            
            # 处理每个输出文件
            for file_type, file_path in output_files.items():
                try:
                    # 判断文件路径类型
                    is_minio_path = '/' in file_path and not os.path.isabs(file_path) and not os.path.exists(file_path)
                    
                    if is_minio_path:
                        storage_path = file_path
                        file_name = os.path.basename(file_path)
                        try:
                            file_content = await minio_util.download_file_by_path(file_path)
                            file_size = len(file_content)
                        except Exception as e:
                            logger.error(f"任务 {task_id} 从MinIO下载文件失败: {file_path}, 错误: {e}")
                            continue
                    else:
                        if not os.path.exists(file_path):
                            logger.error(f"任务 {task_id} 本地文件不存在: {file_path}")
                            continue
                        file_name = os.path.basename(file_path)
                        import io
                        with open(file_path, 'rb') as f:
                            file_content = f.read()
                        file_size = len(file_content)
                        file_obj = io.BytesIO(file_content)
                        file_obj.seek(0)
                        storage_path = minio_util.upload_file(
                            file=file_obj,
                            kb_id=kb_id,
                            file_type=file_type,
                            file_name=file_name
                        )

                    # 获取文件扩展名
                    file_format = os.path.splitext(file_name)[1][1:].lower()

                    # 根据扩展名兜底归类模型文件
                    model_exts = ['step', 'stp', 'stl', 'igs', 'iges', 'obj']
                    db_file_type = self._map_file_type_to_db_enum(file_type)
                    if db_file_type == 'document' and file_format in model_exts:
                        db_file_type = 'model'

                    task_info = await TasksDao.get_tasks_detail_by_id(self.db, task_id)
                    from module_business.dao.files_dao import FilesDao
                    existing_file = await FilesDao.get_files_by_path_and_task(self.db, storage_path, task_id)
                    if existing_file:
                        logger.info(f"任务 {task_id} 文件已存在，跳过保存: {file_name}")
                        continue
                    file_model = FilesModel(
                        kbId=kb_id,
                        originalName=file_name,
                        storagePath=storage_path,
                        fileType=db_file_type,
                        fileSize=file_size,
                        fileFormat=file_format,
                        projectId=task_info.project_id if task_info else None,
                        sourceTaskId=task_id,
                        createdAt=datetime.now()
                    )
                    file_model.validate_fields()
                    await FilesService.add_files_services(self.db, file_model)
                    logger.info(f"任务 {task_id} 文件上传成功: {file_type} -> {storage_path}，类型归类: {db_file_type}")
                except Exception as e:
                    logger.error(f"任务 {task_id} 上传文件 {file_type} 失败: {e}")
                    continue
            
            # 不在这里提交事务，由外层的 handle_task_callback 统一提交
            logger.info(f"任务 {task_id} 所有文件处理完成")

        except Exception as e:
            logger.error(f"处理任务结果文件失败，任务 {task_id}: {e}")
            # 不在这里回滚，由外层处理
            raise  # 重新抛出异常，让外层处理

    async def _check_existing_files(self, task_id: int, kb_id: int = None):
        """
        检查任务是否有已上传的文件（通过外部工具直接上传到MinIO的文件）

        Args:
            task_id: 任务ID
            kb_id: 知识库ID
        """
        try:
            from module_business.dao.files_dao import FilesDao

            # 查询该任务已上传的文件
            existing_files = await FilesDao.get_files_by_task_id(self.db, task_id)

            if existing_files:
                logger.info(f"任务 {task_id} 发现 {len(existing_files)} 个已上传的文件")
                for file in existing_files:
                    logger.info(f"文件: {file.original_name}, 类型: {file.file_type}, 路径: {file.storage_path}")
            else:
                logger.info(f"任务 {task_id} 数据库中没有发现已上传的文件，尝试从MinIO检查")
                # 如果数据库中没有文件记录，尝试从MinIO中查找并添加
                await self._scan_minio_for_task_files(task_id, kb_id)

        except Exception as e:
            logger.error(f"检查任务 {task_id} 已存在文件失败: {e}")

    async def _scan_minio_for_task_files(self, task_id: int, kb_id: int):
        """
        扫描MinIO中该任务的文件并添加到数据库

        Args:
            task_id: 任务ID
            kb_id: 知识库ID
        """
        if not kb_id:
            logger.warning(f"任务 {task_id} 没有知识库ID，跳过MinIO扫描")
            return

        try:
            from utils.minio_util import MinioUtil
            from module_business.entity.vo.files_vo import FilesModel
            from module_business.service.files_service import FilesService
            import os
            from datetime import datetime

            minio_util = MinioUtil.get_instance()

            # 获取任务信息
            task_info = await TasksDao.get_tasks_detail_by_id(self.db, task_id)

            found_files = []

            # 尝试直接检查常见的文件名模式
            common_files = [
                f"kb_{kb_id}/report/o_ring_report_task_{task_id}.txt",
                f"kb_{kb_id}/log/o_ring_design_task_{task_id}.log"
            ]

            for file_path in common_files:
                try:
                    # 尝试下载文件以验证存在性
                    file_content = await minio_util.download_file_by_path(file_path)
                    if file_content:
                        file_name = os.path.basename(file_path)
                        file_size = len(file_content)
                        file_format = os.path.splitext(file_name)[1][1:].lower()

                        # 根据路径确定文件类型
                        if '/report/' in file_path:
                            file_type = 'report'
                        elif '/log/' in file_path:
                            file_type = 'log'
                        else:
                            file_type = 'document'

                        # 检查是否已存在
                        from module_business.dao.files_dao import FilesDao
                        existing_file = await FilesDao.get_files_by_path_and_task(self.db, file_path, task_id)
                        if existing_file:
                            logger.info(f"任务 {task_id} 文件已存在，跳过: {file_name}")
                            continue

                        # 创建文件记录
                        file_model = FilesModel(
                            kbId=kb_id,
                            originalName=file_name,
                            storagePath=file_path,
                            fileType=file_type,
                            fileSize=file_size,
                            fileFormat=file_format,
                            projectId=task_info.project_id if task_info else None,
                            sourceTaskId=task_id,
                            createdAt=datetime.now()
                        )

                        file_model.validate_fields()
                        await FilesService.add_files_services(self.db, file_model)
                        found_files.append(file_name)
                        logger.info(f"任务 {task_id} 从MinIO发现并添加文件: {file_name} -> {file_path}")

                except Exception as e:
                    # 文件不存在或其他错误，继续检查下一个
                    logger.debug(f"任务 {task_id} 检查文件 {file_path} 失败: {e}")
                    continue

            if found_files:
                logger.info(f"任务 {task_id} 从MinIO成功添加 {len(found_files)} 个文件: {found_files}")
            else:
                logger.info(f"任务 {task_id} 在MinIO中没有找到文件")

        except Exception as e:
            logger.error(f"任务 {task_id} 扫描MinIO文件失败: {e}")
    
    def _map_file_type_to_db_enum(self, file_type: str) -> str:
        """
        将外部服务的文件类型映射到数据库枚举值
        
        Args:
            file_type: 外部服务的文件类型
            
        Returns:
            数据库枚举值
        """
        # 文件类型映射表
        type_mapping = {
            'report_image': 'report',
            'report_data': 'report', 
            'report_file': 'report',
            'model_file': 'model',
            'model_data': 'model',
            'dataset_file': 'dataset',
            'dataset_data': 'dataset', 
            'log_file': 'log',
            'log_data': 'log',
            'document_file': 'document',
            'document_data': 'document'
        }
        
        # 如果能找到映射，使用映射值；否则尝试直接匹配；最后默认为document
        mapped_type = type_mapping.get(file_type, file_type)
        
        # 验证是否为有效的数据库枚举值
        valid_types = {"model", "report", "dataset", "log", "document", "archived"}
        if mapped_type not in valid_types:
            logger.warning(f"未知的文件类型 {file_type}，默认使用 document")
            return "document"
        
        return mapped_type
    
    async def _get_user_name(self, user_id: int) -> str:
        """
        根据用户ID获取用户名
        
        Args:
            user_id: 用户ID
            
        Returns:
            用户名
        """
        try:
            from module_admin.dao.user_dao import UserDao
            user_info = await UserDao.get_user_detail_by_id(self.db, user_id)
            if user_info and user_info.get('user_basic_info'):
                user_basic_info = user_info['user_basic_info']
                return user_basic_info.user_name or f"用户{user_id}"
            return f"用户{user_id}"
        except Exception as e:
            logger.error(f"获取用户名失败: {e}")
            return f"用户{user_id}"
    
    def _parse_progress_from_log(self, log_message: str) -> int:
        """
        从日志消息中解析进度值
        
        Args:
            log_message: 日志消息
            
        Returns:
            进度值 (0-100)
        """
        import re
        
        try:
            # 1. 直接包含百分比的情况
            percent_match = re.search(r'(?:完成|progress[:：]?|进度[:：]?)\s*(\d+)%', log_message)
            if percent_match:
                progress = int(percent_match.group(1))
                return min(max(progress, 0), 100)  # 确保在0-100范围内
            
            # 2. 步骤格式: "3/5" 或 "第3步，共5步"
            step_match = re.search(r'(\d+)[/／](\d+)', log_message)
            if step_match:
                current = int(step_match.group(1))
                total = int(step_match.group(2))
                if total > 0:
                    progress = int((current / total) * 100)
                    return min(max(progress, 0), 100)
            
            # 3. 阶段映射
            stage_progress = {
                '启动': 10, '开始': 10, '初始化': 15,
                '准备': 20, '上传': 30, '处理': 50,
                '计算': 60, '分析': 70, '生成': 80,
                '完成': 100, '结束': 100, '成功': 100
            }
            
            for stage, progress in stage_progress.items():
                if stage in log_message:
                    return progress
            
            # 4. 默认返回0
            return 0
            
        except Exception as e:
            logger.error(f"解析进度失败: {e}")
            return 0
    
    async def _push_progress_update(self, task_id: int, progress: int, message: str):
        """
        推送进度更新

        Args:
            task_id: 任务ID
            progress: 进度值 (0-100)
            message: 进度消息
        """
        try:
            from module_business.controller.task_log_controller import push_task_log

            # 只推送专门的进度更新消息，避免重复
            await push_task_log(
                task_id,
                message,
                "INFO",
                message_type="progress_update",
                progress=progress,
                timestamp=datetime.now().isoformat()
            )

            # 异步更新任务进度，避免阻塞主流程
            asyncio.create_task(self._update_task_progress(task_id, progress))

            logger.info(f"任务 {task_id} 进度更新: {progress}% - {message}")

        except Exception as e:
            logger.error(f"推送进度更新失败: {e}")
    
    async def _update_task_progress(self, task_id: int, progress: int):
        """
        更新任务进度

        Args:
            task_id: 任务ID
            progress: 进度值 (0-100)
        """
        try:
            from module_business.dao.tasks_dao import TasksDao
            from config.get_db import AsyncSessionLocal

            # 使用独立的数据库会话来更新进度，确保立即提交
            async with AsyncSessionLocal() as db_session:
                await TasksDao.update_task_progress(db_session, task_id, progress)
                await db_session.commit()

                # 立即刷新数据库连接，确保数据可见
                await db_session.flush()

            # 同时在Redis中缓存最新进度，供前端快速获取
            await self._cache_latest_progress(task_id, progress)

        except Exception as e:
            logger.error(f"更新任务进度失败: {e}")
            # 进度更新失败不应该影响主流程，只记录错误

    async def _cache_latest_progress(self, task_id: int, progress: int, message: str = None):
        """
        在Redis中缓存最新进度和消息

        Args:
            task_id: 任务ID
            progress: 进度值 (0-100)
            message: 进度消息
        """
        try:
            import json
            # 缓存进度和消息，设置1小时过期时间
            progress_data = {
                "progress": progress,
                "message": message or f"当前进度: {progress}%",
                "timestamp": datetime.now().isoformat()
            }
            progress_key = f"task_progress:{task_id}"
            await self.redis.setex(progress_key, 3600, json.dumps(progress_data))
        except Exception as e:
            logger.warning(f"缓存任务进度失败: {e}")

    async def get_cached_progress(self, task_id: int) -> dict:
        """
        从Redis获取缓存的最新进度和消息

        Args:
            task_id: 任务ID

        Returns:
            包含进度和消息的字典，如果没有缓存则返回None
        """
        try:
            import json
            progress_key = f"task_progress:{task_id}"
            cached_data = await self.redis.get(progress_key)
            if cached_data:
                try:
                    return json.loads(cached_data)
                except (json.JSONDecodeError, TypeError):
                    # 兼容旧格式（纯数字）
                    return {"progress": int(cached_data), "message": f"当前进度: {cached_data}%"}
        except Exception as e:
            logger.warning(f"获取缓存进度失败: {e}")
        return None
    
    async def handle_progress_callback(self, task_id: int, progress_data: dict) -> bool:
        """
        处理进度回调

        Args:
            task_id: 任务ID
            progress_data: 进度数据

        Returns:
            处理是否成功
        """
        try:
            # 解析进度数据
            progress = progress_data.get('progress', 0)
            message = progress_data.get('message', '')

            # 如果没有直接提供进度值，尝试从消息中解析
            if progress == 0 and message:
                progress = self._parse_progress_from_log(message)

            # 确保进度值在有效范围内
            progress = min(max(progress, 0), 100)

            # 立即缓存最新进度和消息到Redis
            await self._cache_latest_progress(task_id, progress, message)

            # 推送进度更新
            await self._push_progress_update(task_id, progress, message)

            return True

        except Exception as e:
            logger.error(f"处理进度回调失败: {e}")
            return False
    
    async def get_scheduler_status(self) -> Dict[str, Any]:
        """
        获取调度器状态
        
        Returns:
            调度器状态信息
        """
        queue_status = await self.queue_service.get_queue_status()
        
        return {
            "is_running": self.is_running,
            "queue_status": queue_status,
            "last_updated": datetime.now().isoformat()
        }

    async def _health_check_and_cleanup(self):
        """
        健康检查和清理，简化版本
        """
        try:
            logger.info("🔍 开始调度器健康检查和清理")

            # 检查处理队列中的任务状态
            processing_task_ids = await self.redis.lrange("task_processing_queue_new", 0, -1)

            if processing_task_ids:
                logger.info(f"🔍 检查处理队列中的 {len(processing_task_ids)} 个任务")

                for task_id in processing_task_ids:
                    try:
                        task_id = int(task_id)

                        # 检查任务在数据库中的状态
                        task_info = await TasksDao.get_tasks_detail_by_id(self.db, task_id)

                        if task_info:
                            if task_info.status in ['completed', 'failed', 'cancelled']:
                                # 任务已完成，从处理队列中移除
                                await self.redis.lrem("task_processing_queue_new", 1, task_id)
                                await self.queue_service._cleanup_task_data(task_id)
                                logger.info(f"🧹 清理已完成任务 {task_id} 从处理队列")
                        else:
                            # 任务不存在，从处理队列中移除并清理数据
                            await self.redis.lrem("task_processing_queue_new", 1, task_id)
                            await self.queue_service._cleanup_task_data(task_id)
                            logger.info(f"🧹 清理不存在任务 {task_id} 的数据")

                    except Exception as e:
                        logger.warning(f"⚠️ 检查处理队列任务 {task_id} 时出错: {e}")

            logger.info("✅ 调度器健康检查和清理完成")

        except Exception as e:
            logger.error(f"❌ 调度器健康检查失败: {e}")