# 权限管理系统实现说明

## 概述

本文档描述了基于角色的权限管理系统的完整实现，满足以下需求：

1. **超级管理员**：拥有所有的权限，包括可以给用户分配角色
2. **项目管理员**：管理负责的项目(CRUD)，管理项目成员，管理该项目下所有的任务，管理该项目下产生的所有数据，不能看到非自己负责的项目和数据库
3. **项目成员**：能够看到所属项目组内的所有任务，能够创建和执行新任务，只能对自己创建的任务进行修改，提交，删除，以及下载结果数据，对于其他人创建的任务只能有查看权限，无数据库管理菜单栏访问权限
4. **普通用户**：只能访问首页和工具广场页菜单栏

## 系统架构

### 1. 数据库设计

#### 1.1 项目成员关联表 (rd_project_members)
```sql
CREATE TABLE rd_project_members (
    project_id BIGINT NOT NULL COMMENT '项目ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    role_type VARCHAR(20) NOT NULL COMMENT '成员角色类型：project_manager(项目管理员), project_member(项目成员)',
    assigned_at TIMESTAMP NULL COMMENT '分配时间',
    assigned_by BIGINT NOT NULL COMMENT '分配人ID',
    is_deleted SMALLINT DEFAULT 0 COMMENT '删除标志（0代表存在 1代表删除）',
    PRIMARY KEY (project_id, user_id)
);
```

#### 1.2 角色定义
- `super_admin`: 超级管理员
- `project_manager`: 项目管理员
- `project_member`: 项目成员
- `common_user`: 普通用户

### 2. 核心组件

#### 2.1 角色常量定义 (`config/role_constants.py`)
定义了所有角色相关的常量和权限检查方法：
- 角色键值和名称映射
- 权限级别定义
- 角色权限检查方法

#### 2.2 权限验证工具 (`utils/permission_util.py`)
提供统一的权限验证接口：
- 项目权限检查
- 任务编辑权限检查
- 菜单访问权限检查
- 接口访问权限检查

#### 2.3 项目权限验证装饰器 (`module_business/aspect/project_auth.py`)
提供项目级别的权限验证装饰器：
- `CheckProjectPermission`: 检查项目权限
- `CheckProjectManagerPermission`: 检查项目管理员权限
- `CheckProjectMemberPermission`: 检查项目成员权限

#### 2.4 数据权限过滤器 (`module_business/aspect/data_scope.py`)
提供数据范围过滤功能：
- `ProjectDataScopeFilter`: 项目数据过滤
- `TaskDataScopeFilter`: 任务数据过滤
- `BusinessDataScopeFilter`: 业务数据过滤

### 3. 项目成员管理

#### 3.1 数据模型 (`module_business/entity/vo/project_members_vo.py`)
- `ProjectMembersModel`: 项目成员基础模型
- `AddProjectMemberModel`: 添加项目成员模型
- `BatchAddProjectMembersModel`: 批量添加项目成员模型
- `UpdateProjectMemberRoleModel`: 更新项目成员角色模型

#### 3.2 数据访问层 (`module_business/dao/project_members_dao.py`)
提供项目成员的数据库操作：
- 添加/删除/修改项目成员
- 查询用户所属项目
- 检查用户项目权限

#### 3.3 服务层 (`module_business/service/project_members_service.py`)
提供项目成员管理的业务逻辑：
- 成员添加验证
- 批量操作处理
- 权限检查

#### 3.4 控制器层 (`module_business/controller/project_members_controller.py`)
提供项目成员管理的API接口：
- `POST /business/projectMembers/add`: 添加项目成员
- `POST /business/projectMembers/batchAdd`: 批量添加项目成员
- `PUT /business/projectMembers/updateRole`: 更新成员角色
- `DELETE /business/projectMembers/{project_id}/{user_id}`: 移除项目成员

## 权限控制实现

### 1. 菜单权限控制

修改了 `module_admin/dao/menu_dao.py` 中的菜单获取逻辑，根据用户角色过滤可访问的菜单：

```python
def _get_allowed_menu_paths(cls, role_keys: list) -> list:
    allowed_paths = []
    
    # 所有角色都可以访问的菜单
    common_paths = ['/dashboard', '/tools']
    allowed_paths.extend(common_paths)
    
    # 项目成员及以上角色可以访问的菜单
    if (RoleConstants.is_project_member(role_keys) or 
        RoleConstants.is_project_manager(role_keys) or 
        RoleConstants.is_super_admin(role_keys)):
        allowed_paths.extend(['/tasks'])
    
    # 项目管理员及以上角色可以访问的菜单
    if (RoleConstants.is_project_manager(role_keys) or 
        RoleConstants.is_super_admin(role_keys)):
        allowed_paths.extend(['/projects', '/database'])
    
    # 超级管理员可以访问的菜单
    if RoleConstants.is_super_admin(role_keys):
        allowed_paths.extend(['/system'])
    
    return allowed_paths
```

### 2. 数据权限控制

#### 2.1 项目数据过滤
- 超级管理员：可以查看所有项目
- 项目管理员/项目成员：只能查看自己参与的项目和拥有的项目
- 普通用户：无法查看任何项目

#### 2.2 任务数据过滤
- 超级管理员：可以查看所有任务
- 项目管理员：可以查看和管理所负责项目的所有任务
- 项目成员：可以查看所属项目的所有任务，但只能编辑自己创建的任务
- 普通用户：无法查看任何任务

### 3. 接口权限控制

通过权限验证装饰器和工具类实现接口级别的权限控制：

```python
# 项目编辑权限检查
await _check_project_edit_permission(edit_projects.project_id, current_user, query_db)

# 任务编辑权限检查
await check_task_edit_permission(edit_tasks.task_id, current_user, query_db)
```

## 测试覆盖

### 1. 单元测试
- `tests/test_role_constants.py`: 角色常量测试
- `tests/test_permission_util.py`: 权限验证工具测试
- `tests/test_project_members.py`: 项目成员管理测试

### 2. 集成测试
- `tests/test_permission_integration.py`: 权限系统集成测试

### 3. 测试运行
使用 `run_permission_tests.py` 脚本运行所有权限相关测试：

```bash
python run_permission_tests.py
```

## 部署说明

### 1. 数据库迁移
执行以下SQL脚本：
- `sql/create_project_members_table.sql`: 创建项目成员表
- `sql/insert_new_roles.sql`: 插入新角色和菜单权限

### 2. 代码部署
确保以下新增文件已部署：
- 项目成员管理相关文件
- 权限验证相关文件
- 测试文件

### 3. 配置更新
- 更新路由配置，添加项目成员管理控制器
- 确保权限验证装饰器正确应用

## 安全考虑

1. **权限提升防护**：严格的角色层次检查，防止权限提升
2. **跨项目访问防护**：确保用户只能访问自己参与的项目
3. **数据泄露防护**：通过数据权限过滤确保数据安全
4. **接口安全**：所有敏感接口都有权限验证
5. **边界情况处理**：对不存在的资源和异常情况有适当的错误处理

## 维护指南

1. **添加新角色**：在 `RoleConstants` 中定义新角色，并更新相关权限检查方法
2. **修改权限**：更新 `PermissionUtil` 中的权限检查逻辑
3. **添加新菜单**：在菜单权限控制中添加新的路径映射
4. **数据权限扩展**：在数据权限过滤器中添加新的过滤逻辑

## 总结

本权限管理系统实现了完整的基于角色的访问控制(RBAC)，包括：
- 四种明确定义的用户角色
- 细粒度的权限控制（菜单、接口、数据）
- 项目级别的成员管理
- 完整的测试覆盖
- 安全的权限验证机制

系统设计遵循最小权限原则，确保用户只能访问其角色允许的资源，同时提供了灵活的扩展机制以适应未来的需求变化。
