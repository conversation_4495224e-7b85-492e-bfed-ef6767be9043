from datetime import datetime
from pydantic import BaseModel, ConfigDict, Field
from pydantic.alias_generators import to_camel
from pydantic_validation_decorator import NotBlank
from typing import Optional
from module_admin.annotation.pydantic_annotation import as_query




class ProjectTypesRelModel(BaseModel):
    """
    项目-类型关联关系表对应pydantic模型
    """
    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True)

    project_id: Optional[int] = Field(default=None, description='')
    type_id: Optional[int] = Field(default=None, description='')
    assigned_at: Optional[datetime] = Field(default=None, description='')
    assigned_by: Optional[int] = Field(default=None, description='')

    def validate_fields(self):
        pass




class ProjectTypesRelQueryModel(ProjectTypesRelModel):
    """
    项目-类型关联关系不分页查询模型
    """
    pass


@as_query
class ProjectTypesRelPageQueryModel(ProjectTypesRelQueryModel):
    """
    项目-类型关联关系分页查询模型
    """

    page_num: int = Field(default=1, description='当前页码')
    page_size: int = Field(default=10, description='每页记录数')


class DeleteProjectTypesRelModel(BaseModel):
    """
    删除项目-类型关联关系模型
    """

    model_config = ConfigDict(alias_generator=to_camel)

    project_ids: str = Field(description='需要删除的')
