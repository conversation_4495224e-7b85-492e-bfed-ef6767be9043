<template>
  <div class="batch-upload-container">
    <div class="upload-header">
      <h3>批量文件上传</h3>
      <el-button 
        type="primary" 
        @click="selectFiles"
        :disabled="uploading"
      >
        <el-icon><Plus /></el-icon>
        选择文件
      </el-button>
    </div>

    <!-- 文件选择器 -->
    <input
      ref="fileInput"
      type="file"
      multiple
      style="display: none"
      @change="handleFileSelect"
      :accept="acceptTypes"
    />

    <!-- 文件列表 -->
    <div class="file-list" v-if="fileList.length > 0">
      <div class="list-header">
        <span>已选择文件 ({{ fileList.length }}/{{ maxFiles }})</span>
        <el-button 
          size="small" 
          type="danger" 
          @click="clearFiles"
          :disabled="uploading"
        >
          清空
        </el-button>
      </div>

      <div class="file-items">
        <div 
          v-for="(file, index) in fileList" 
          :key="index"
          class="file-item"
          :class="{ 'uploading': file.uploading, 'success': file.success, 'error': file.error }"
        >
          <div class="file-info">
            <el-icon class="file-icon">
              <Document v-if="file.type === 'document'" />
              <Files v-else-if="file.type === 'model'" />
              <DataAnalysis v-else-if="file.type === 'dataset'" />
              <Notebook v-else-if="file.type === 'report'" />
              <List v-else-if="file.type === 'log'" />
              <FolderOpened v-else-if="file.type === 'archived'" />
              <Document v-else />
            </el-icon>
            <div class="file-details">
              <div class="file-name">{{ file.name }}</div>
              <div class="file-meta">
                <span class="file-size">{{ formatFileSize(file.size) }}</span>
                <span class="file-type">{{ file.type }}</span>
                <span v-if="file.path" class="file-path">{{ file.path }}</span>
              </div>
            </div>
          </div>

          <div class="file-actions">
            <el-icon v-if="file.uploading" class="loading"><Loading /></el-icon>
            <el-icon v-else-if="file.success" class="success"><Check /></el-icon>
            <el-icon v-else-if="file.error" class="error"><Close /></el-icon>
            <el-button 
              v-else
              size="small" 
              type="danger" 
              @click="removeFile(index)"
              :disabled="uploading"
            >
              删除
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 上传配置 -->
    <div class="upload-config" v-if="fileList.length > 0">
      <el-form :model="uploadConfig" label-width="100px" size="small">
        <el-form-item label="子文件夹">
          <el-input 
            v-model="uploadConfig.subFolder" 
            placeholder="可选，如：input、output等"
            :disabled="uploading"
          />
        </el-form-item>
        <el-form-item label="上传模式">
          <el-radio-group v-model="uploadConfig.mode" :disabled="uploading">
            <el-radio value="database">保存到数据库</el-radio>
            <el-radio value="minio">仅上传到MinIO</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
    </div>

    <!-- 上传按钮和进度 -->
    <div class="upload-actions" v-if="fileList.length > 0">
      <el-button 
        type="primary" 
        size="large"
        @click="startUpload"
        :loading="uploading"
        :disabled="fileList.length === 0"
      >
        {{ uploading ? '上传中...' : '开始上传' }}
      </el-button>
      
      <div class="upload-progress" v-if="uploading || uploadComplete">
        <el-progress 
          :percentage="uploadProgress" 
          :status="uploadComplete ? (hasErrors ? 'exception' : 'success') : ''"
        />
        <div class="progress-text">
          {{ uploadProgressText }}
        </div>
      </div>
    </div>

    <!-- 上传结果 -->
    <div class="upload-result" v-if="uploadComplete && uploadResult">
      <el-alert
        :title="uploadResult.success_count > 0 ? '上传完成' : '上传失败'"
        :type="hasErrors ? 'warning' : 'success'"
        :description="`成功: ${uploadResult.success_count}个，失败: ${uploadResult.failed_count}个`"
        show-icon
        :closable="false"
      />
      
      <!-- 失败文件详情 -->
      <div v-if="uploadResult.failed_files && uploadResult.failed_files.length > 0" class="failed-files">
        <h4>失败文件详情：</h4>
        <div v-for="failedFile in uploadResult.failed_files" :key="failedFile.filename" class="failed-item">
          <span class="filename">{{ failedFile.filename }}</span>
          <span class="error-msg">{{ failedFile.error }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, defineProps, defineEmits } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus, Document, Files, DataAnalysis, Notebook, List, FolderOpened, Loading, Check, Close } from '@element-plus/icons-vue'
import { batchUploadFiles, batchUploadFilesToMinioOnly } from '@/api/files'

// Props
const props = defineProps({
  kbId: {
    type: [String, Number],
    required: true
  },
  projectId: {
    type: [String, Number],
    default: null
  },
  taskId: {
    type: [String, Number],
    default: null
  },
  maxFiles: {
    type: Number,
    default: 10
  },
  acceptTypes: {
    type: String,
    default: '.step,.stp,.iges,.igs,.stl,.obj,.pdf,.doc,.docx,.csv,.xlsx,.txt,.log,.zip,.rar'
  }
})

// Emits
const emit = defineEmits(['upload-success', 'upload-error', 'upload-complete'])

// Refs
const fileInput = ref()
const fileList = ref([])
const uploading = ref(false)
const uploadComplete = ref(false)
const uploadResult = ref(null)

// 上传配置
const uploadConfig = ref({
  subFolder: '',
  mode: 'database' // database 或 minio
})

// 计算属性
const uploadProgress = computed(() => {
  if (!uploading.value && !uploadComplete.value) return 0
  if (uploadComplete.value) return 100
  
  const completedFiles = fileList.value.filter(f => f.success || f.error).length
  return Math.round((completedFiles / fileList.value.length) * 100)
})

const uploadProgressText = computed(() => {
  if (!uploading.value && !uploadComplete.value) return ''
  
  const completedFiles = fileList.value.filter(f => f.success || f.error).length
  return `${completedFiles}/${fileList.value.length} 文件已处理`
})

const hasErrors = computed(() => {
  return uploadResult.value && uploadResult.value.failed_count > 0
})

// 方法
const selectFiles = () => {
  fileInput.value.click()
}

const handleFileSelect = (event) => {
  const files = Array.from(event.target.files)
  
  if (files.length + fileList.value.length > props.maxFiles) {
    ElMessage.warning(`最多只能选择${props.maxFiles}个文件`)
    return
  }
  
  files.forEach(file => {
    const fileType = determineFileType(file.name)
    fileList.value.push({
      name: file.name,
      size: file.size,
      type: fileType,
      file: file,
      uploading: false,
      success: false,
      error: false,
      path: null
    })
  })
  
  // 清空input
  event.target.value = ''
}

const removeFile = (index) => {
  fileList.value.splice(index, 1)
}

const clearFiles = () => {
  fileList.value = []
  uploadComplete.value = false
  uploadResult.value = null
}

const determineFileType = (filename) => {
  const ext = filename.toLowerCase().split('.').pop()
  const typeMap = {
    'step': 'model', 'stp': 'model', 'iges': 'model', 'igs': 'model', 'stl': 'model', 'obj': 'model',
    'csv': 'dataset', 'xlsx': 'dataset', 'xls': 'dataset', 'json': 'dataset',
    'pdf': 'report', 'doc': 'report', 'docx': 'report',
    'log': 'log', 'out': 'log',
    'zip': 'archived', 'rar': 'archived', 'gz': 'archived', 'bz2': 'archived', 'tar': 'archived', '7z': 'archived',
    'txt': 'document'
  }
  return typeMap[ext] || 'document'
}

const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const startUpload = async () => {
  if (fileList.value.length === 0) return
  
  uploading.value = true
  uploadComplete.value = false
  uploadResult.value = null
  
  // 重置文件状态
  fileList.value.forEach(file => {
    file.uploading = true
    file.success = false
    file.error = false
    file.path = null
  })
  
  try {
    const files = fileList.value.map(f => f.file)
    
    let result
    if (uploadConfig.value.mode === 'database') {
      result = await batchUploadFiles(
        props.kbId,
        files,
        props.projectId,
        props.taskId,
        uploadConfig.value.subFolder || null
      )
    } else {
      result = await batchUploadFilesToMinioOnly(
        props.kbId,
        files,
        props.taskId,
        uploadConfig.value.subFolder || null
      )
    }
    
    if (result.code === 200) {
      uploadResult.value = result.data
      
      // 更新文件状态
      result.data.success_files.forEach(successFile => {
        const fileItem = fileList.value.find(f => f.name === successFile.filename)
        if (fileItem) {
          fileItem.success = true
          fileItem.uploading = false
          fileItem.path = successFile.file_path
        }
      })
      
      result.data.failed_files.forEach(failedFile => {
        const fileItem = fileList.value.find(f => f.name === failedFile.filename)
        if (fileItem) {
          fileItem.error = true
          fileItem.uploading = false
        }
      })
      
      emit('upload-success', result.data)
      ElMessage.success(result.msg)
    } else {
      throw new Error(result.msg || '上传失败')
    }
  } catch (error) {
    console.error('批量上传失败:', error)
    ElMessage.error('批量上传失败: ' + error.message)
    
    // 标记所有文件为失败
    fileList.value.forEach(file => {
      file.uploading = false
      file.error = true
    })
    
    emit('upload-error', error)
  } finally {
    uploading.value = false
    uploadComplete.value = true
    emit('upload-complete', uploadResult.value)
  }
}
</script>

<style scoped>
.batch-upload-container {
  border: 1px solid #dcdfe6;
  border-radius: 6px;
  padding: 20px;
  background: #fff;
}

.upload-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.upload-header h3 {
  margin: 0;
  color: #303133;
}

.file-list {
  margin: 20px 0;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
  border-bottom: 1px solid #ebeef5;
  margin-bottom: 10px;
}

.file-items {
  max-height: 300px;
  overflow-y: auto;
}

.file-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  margin-bottom: 8px;
  transition: all 0.3s;
}

.file-item.uploading {
  background-color: #f0f9ff;
  border-color: #409eff;
}

.file-item.success {
  background-color: #f0f9f0;
  border-color: #67c23a;
}

.file-item.error {
  background-color: #fef0f0;
  border-color: #f56c6c;
}

.file-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.file-icon {
  font-size: 24px;
  margin-right: 12px;
  color: #606266;
}

.file-details {
  flex: 1;
}

.file-name {
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
}

.file-meta {
  font-size: 12px;
  color: #909399;
}

.file-meta span {
  margin-right: 12px;
}

.file-actions .loading {
  color: #409eff;
  animation: rotate 2s linear infinite;
}

.file-actions .success {
  color: #67c23a;
}

.file-actions .error {
  color: #f56c6c;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.upload-config {
  margin: 20px 0;
  padding: 15px;
  background: #f5f7fa;
  border-radius: 4px;
}

.upload-actions {
  text-align: center;
  margin-top: 20px;
}

.upload-progress {
  margin-top: 15px;
}

.progress-text {
  margin-top: 8px;
  font-size: 14px;
  color: #606266;
}

.upload-result {
  margin-top: 20px;
}

.failed-files {
  margin-top: 15px;
}

.failed-files h4 {
  margin: 0 0 10px 0;
  color: #f56c6c;
}

.failed-item {
  display: flex;
  justify-content: space-between;
  padding: 8px 12px;
  background: #fef0f0;
  border-radius: 4px;
  margin-bottom: 5px;
}

.failed-item .filename {
  font-weight: 500;
}

.failed-item .error-msg {
  color: #f56c6c;
  font-size: 12px;
}
</style>
