import os
import sys
import time
from loguru import logger as _logger
from typing import Dict
from middlewares.trace_middleware import TraceCtx


class LoggerInitializer:
    def __init__(self):
        self.log_path = os.path.join(os.getcwd(), 'logs')
        self.__ensure_log_directory_exists()
        self.log_path_error = os.path.join(self.log_path, f'{time.strftime("%Y-%m-%d")}_error.log')

    def __ensure_log_directory_exists(self):
        """
        确保日志目录存在，如果不存在则创建
        """
        if not os.path.exists(self.log_path):
            os.mkdir(self.log_path)

    @staticmethod
    def __filter(log: Dict):
        """
        自定义日志过滤器，添加trace_id
        """
        log['trace_id'] = TraceCtx.get_id()
        return log

    def get_log_level(self):
        """
        根据环境变量获取日志级别
        """
        app_env = os.getenv('APP_ENV', 'dev').lower()
        
        # 根据环境设置日志级别
        if app_env in ['sit', 'prod']:
            # SIT和PROD环境只显示INFO及以上级别的日志
            return "INFO"
        else:
            # DEV环境显示DEBUG及以上级别的日志
            return "DEBUG"

    def init_log(self):
        """
        初始化日志配置
        """
        # 获取当前环境的日志级别
        log_level = self.get_log_level()

        # 自定义日志格式
        format_str = (
            '<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> | '
            '<cyan>{trace_id}</cyan> | '
            '<level>{level: <8}</level> | '
            '<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - '
            '<level>{message}</level>'
        )

        _logger.remove()

        # 根据环境设置日志级别
        _logger.add(
            sys.stderr,
            filter=self.__filter,
            format=format_str,
            level=log_level,
            enqueue=True
        )

        # 按天分割的全量日志文件
        all_log_path = os.path.join(self.log_path, '{time:YYYY-MM-DD}_all.log')
        _logger.add(
            all_log_path,
            filter=self.__filter,
            format=format_str,
            level=log_level,
            rotation='00:00',  # 每天午夜轮转
            retention='30 days',  # 保留30天
            encoding='utf-8',
            enqueue=True,
            compression='zip',
        )

        # 错误日志文件始终记录ERROR级别
        _logger.add(
            self.log_path_error,
            filter=self.__filter,
            format=format_str,
            level="ERROR",
            rotation='50MB',
            encoding='utf-8',
            enqueue=True,
            compression='zip',
        )

        # 打印当前日志级别配置
        app_env = os.getenv('APP_ENV', 'dev')
        print(f"日志配置: 环境={app_env}, 级别={log_level}")

        return _logger


# 初始化日志处理器
log_initializer = LoggerInitializer()
logger = log_initializer.init_log()
