#!/usr/bin/env python3
"""
权限系统测试运行脚本
"""
import sys
import subprocess
import os
from pathlib import Path


def run_tests():
    """运行权限系统相关的测试"""
    
    # 确保在正确的目录中
    script_dir = Path(__file__).parent
    os.chdir(script_dir)
    
    # 测试文件列表
    test_files = [
        'tests/test_role_constants.py',
        'tests/test_permission_util.py',
        'tests/test_project_members.py',
        'tests/test_permission_integration.py',
    ]
    
    print("🚀 开始运行权限系统测试...")
    print("=" * 60)
    
    # 检查测试文件是否存在
    missing_files = []
    for test_file in test_files:
        if not Path(test_file).exists():
            missing_files.append(test_file)
    
    if missing_files:
        print("❌ 以下测试文件不存在:")
        for file in missing_files:
            print(f"   - {file}")
        return False
    
    # 运行每个测试文件
    all_passed = True
    results = {}
    
    for test_file in test_files:
        print(f"\n📋 运行测试: {test_file}")
        print("-" * 40)
        
        try:
            # 运行pytest
            result = subprocess.run([
                sys.executable, '-m', 'pytest', 
                test_file, 
                '-v',  # 详细输出
                '--tb=short',  # 简短的错误回溯
                '--color=yes',  # 彩色输出
            ], capture_output=True, text=True, timeout=60)
            
            if result.returncode == 0:
                print(f"✅ {test_file} - 所有测试通过")
                results[test_file] = 'PASSED'
            else:
                print(f"❌ {test_file} - 测试失败")
                print("错误输出:")
                print(result.stdout)
                if result.stderr:
                    print("标准错误:")
                    print(result.stderr)
                results[test_file] = 'FAILED'
                all_passed = False
                
        except subprocess.TimeoutExpired:
            print(f"⏰ {test_file} - 测试超时")
            results[test_file] = 'TIMEOUT'
            all_passed = False
            
        except Exception as e:
            print(f"💥 {test_file} - 运行出错: {e}")
            results[test_file] = 'ERROR'
            all_passed = False
    
    # 打印总结
    print("\n" + "=" * 60)
    print("📊 测试结果总结:")
    print("=" * 60)
    
    for test_file, status in results.items():
        status_icon = {
            'PASSED': '✅',
            'FAILED': '❌',
            'TIMEOUT': '⏰',
            'ERROR': '💥'
        }.get(status, '❓')
        
        print(f"{status_icon} {test_file}: {status}")
    
    if all_passed:
        print("\n🎉 所有权限系统测试都通过了！")
        return True
    else:
        print("\n⚠️  部分测试失败，请检查上述错误信息。")
        return False


def run_coverage_report():
    """运行测试覆盖率报告"""
    print("\n📈 生成测试覆盖率报告...")
    print("-" * 40)
    
    try:
        # 运行带覆盖率的测试
        result = subprocess.run([
            sys.executable, '-m', 'pytest',
            'tests/test_role_constants.py',
            'tests/test_permission_util.py', 
            'tests/test_project_members.py',
            'tests/test_permission_integration.py',
            '--cov=config.role_constants',
            '--cov=utils.permission_util',
            '--cov=module_business.service.project_members_service',
            '--cov=module_business.aspect.project_auth',
            '--cov=module_business.aspect.data_scope',
            '--cov-report=html',
            '--cov-report=term-missing',
        ], capture_output=True, text=True, timeout=120)
        
        if result.returncode == 0:
            print("✅ 覆盖率报告生成成功")
            print(result.stdout)
            print("\n📁 HTML覆盖率报告已生成到 htmlcov/ 目录")
        else:
            print("❌ 覆盖率报告生成失败")
            print(result.stdout)
            if result.stderr:
                print(result.stderr)
                
    except subprocess.TimeoutExpired:
        print("⏰ 覆盖率报告生成超时")
    except Exception as e:
        print(f"💥 覆盖率报告生成出错: {e}")


def check_dependencies():
    """检查测试依赖"""
    print("🔍 检查测试依赖...")
    
    required_packages = ['pytest', 'pytest-asyncio', 'pytest-cov']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("❌ 缺少以下测试依赖:")
        for package in missing_packages:
            print(f"   - {package}")
        print("\n请运行以下命令安装依赖:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    print("✅ 所有测试依赖都已安装")
    return True


def main():
    """主函数"""
    print("🔐 权限系统测试套件")
    print("=" * 60)
    
    # 检查依赖
    if not check_dependencies():
        sys.exit(1)
    
    # 运行测试
    success = run_tests()
    
    # 如果测试通过，生成覆盖率报告
    if success:
        try:
            run_coverage_report()
        except Exception as e:
            print(f"⚠️  覆盖率报告生成失败: {e}")
    
    # 退出码
    sys.exit(0 if success else 1)


if __name__ == '__main__':
    main()
