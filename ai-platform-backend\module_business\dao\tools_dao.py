from sqlalchemy import delete, select, update
from sqlalchemy.ext.asyncio import AsyncSession
from module_business.entity.do.tools_do import RdTools
from module_business.entity.vo.tools_vo import ToolsModel, ToolsPageQueryModel
from utils.page_util import PageUtil


class ToolsDao:
    """
    工具管理模块数据库操作层
    """

    @classmethod
    async def get_tools_detail_by_id(cls, db: AsyncSession, tool_id: int):
        """
        根据工具id获取工具管理详细信息

        :param db: orm对象
        :param tool_id: 工具id
        :return: 工具管理信息对象
        """
        tools_info = (
            (
                await db.execute(
                    select(RdTools)
                    .where(
                        RdTools.tool_id == tool_id
                    )
                )
            )
            .scalars()
            .first()
        )

        return tools_info

    @classmethod
    async def get_tools_detail_by_info(cls, db: AsyncSession, tools: ToolsModel):
        """
        根据工具管理参数获取工具管理信息

        :param db: orm对象
        :param tools: 工具管理参数对象
        :return: 工具管理信息对象
        """
        tools_info = (
            (
                await db.execute(
                    select(RdTools).where(
                        RdTools.tool_id == tools.tool_id if tools.tool_id else True,
                    )
                )
            )
            .scalars()
            .first()
        )

        return tools_info

    @classmethod
    async def get_tools_list(cls, db: AsyncSession, query_object: ToolsPageQueryModel, is_page: bool = False):
        """
        根据查询参数获取工具管理列表信息

        :param db: orm对象
        :param query_object: 查询参数对象
        :param is_page: 是否开启分页
        :return: 工具管理列表信息对象
        """
        query = (
            select(RdTools)
            .where(
                RdTools.tool_name.like(f'%{query_object.tool_name}%') if query_object.tool_name else True,
                RdTools.type_id == query_object.type_id if query_object.type_id else True,
                RdTools.description == query_object.description if query_object.description else True,
                RdTools.version == query_object.version if query_object.version else True,
                RdTools.vendor == query_object.vendor if query_object.vendor else True,
                RdTools.executable_api == query_object.executable_api if query_object.executable_api else True,
                RdTools.config_template == query_object.config_template if query_object.config_template else True,
                RdTools.is_active == query_object.is_active if query_object.is_active else True,
                RdTools.is_deleted == False,
                RdTools.created_at == query_object.created_at if query_object.created_at else True,
                RdTools.updated_at == query_object.updated_at if query_object.updated_at else True,
            )
            .order_by(RdTools.tool_id)
            .distinct()
        )
        tools_list = await PageUtil.paginate(db, query, query_object.page_num, query_object.page_size, is_page)

        return tools_list

    @classmethod
    async def add_tools_dao(cls, db: AsyncSession, tools: ToolsModel):
        """
        新增工具管理数据库操作

        :param db: orm对象
        :param tools: 工具管理对象
        :return:
        """
        db_tools = RdTools(**tools.model_dump(exclude={}))
        db.add(db_tools)
        await db.flush()

        return db_tools

    @classmethod
    async def edit_tools_dao(cls, db: AsyncSession, tools: dict):
        """
        编辑工具管理数据库操作

        :param db: orm对象
        :param tools: 需要更新的工具管理字典
        :return:
        """
        await db.execute(update(RdTools), [tools])



