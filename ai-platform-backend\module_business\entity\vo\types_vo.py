from datetime import datetime
from pydantic import BaseModel, ConfigDict, Field
from pydantic.alias_generators import to_camel
from pydantic_validation_decorator import NotBlank
from typing import Optional
from module_admin.annotation.pydantic_annotation import as_query




class TypesModel(BaseModel):
    """
    通用类型管理表对应pydantic模型
    """
    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True)

    type_id: Optional[int] = Field(default=None, description='')
    type_name: Optional[str] = Field(default=None, description='类型名称(英文标识)')
    display_name: Optional[str] = Field(default=None, description='显示名称')
    color_code: Optional[str] = Field(default=None, description='UI显示颜色')
    is_active: Optional[int] = Field(default=1, description='是否激活')
    is_deleted: Optional[int] = Field(default=0, description='')
    created_at: Optional[datetime] = Field(default=None, description='')

    @NotBlank(field_name='type_name', message='类型名称(英文标识)不能为空')
    def get_type_name(self):
        return self.type_name

    @NotBlank(field_name='display_name', message='显示名称不能为空')
    def get_display_name(self):
        return self.display_name


    def validate_fields(self):
        self.get_type_name()
        self.get_display_name()




class TypesQueryModel(TypesModel):
    """
    通用类型管理不分页查询模型
    """
    pass


@as_query
class TypesPageQueryModel(TypesQueryModel):
    """
    通用类型管理分页查询模型
    """

    page_num: int = Field(default=1, description='当前页码')
    page_size: int = Field(default=10, description='每页记录数')


class DeleteTypesModel(BaseModel):
    """
    删除通用类型管理模型
    """

    model_config = ConfigDict(alias_generator=to_camel)

    type_ids: str = Field(description='需要删除的')
