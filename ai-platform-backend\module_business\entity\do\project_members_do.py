from sqlalchemy import TIMESTAMP, Column, BigInteger, String, SmallInteger
from config.database import Base


class RdProjectMembers(Base):
    """
    项目成员关联表
    """

    __tablename__ = 'rd_project_members'

    project_id = Column(BigInteger, primary_key=True, nullable=False, comment='项目ID')
    user_id = Column(BigInteger, primary_key=True, nullable=False, comment='用户ID')
    role_type = Column(String(20), nullable=False, comment='成员角色类型：project_manager(项目管理员), project_member(项目成员)')
    assigned_at = Column(TIMESTAMP, nullable=True, comment='分配时间')
    assigned_by = Column(BigInteger, nullable=False, comment='分配人ID')
    is_deleted = Column(SmallInteger, default=0, comment='删除标志（0代表存在 1代表删除）')
