<template>
  <div class="home-container">    
    <!-- 常用工具部分 -->
    <div class="section">
      <h2 class="section-title">常用工具</h2>
      <div class="tools-grid">
        <el-card 
          v-for="tool in tools" 
          :key="tool.toolName" 
          class="tool-card"
          shadow="hover"
          @click=""
        >
          <template #header>
            <div class="tool-header">
              <span class="tool-name">{{ tool.toolName }}</span>
            </div>
          </template>
          <div class="tool-description">{{ tool.description }}</div>
        </el-card>
      </div>
    </div>

    <!-- 最近项目部分 -->
    <div class="section">
      <h2 class="section-title">最近项目</h2>
      <div class="projects-grid">
        <el-card v-for="project in recentProjects" :key="project.projectId" class="project-card" shadow="hover" @dblclick="goToProjectDetail(project)">
          <template #header>
            <div class="project-header">
              <span class="project-name">{{ project.projectName }}</span>
              <span class="project-types">
                <el-tag
                  v-for="typeId in (project.typeIds || (project.typeId ? [project.typeId] : []))"
                  :key="typeId"
                  :type="getTypeTagType(typeId)"
                  size="small"
                >
                  {{ getTypeName(typeId) }}
                </el-tag>
              </span>
            </div>
          </template>
          <div class="project-content">
            <div class="project-description">{{ project.description }}</div>
            <div class="project-time">创建时间：{{ formatDate(project.createdAt) }}</div>
          </div>
        </el-card>
      </div>
    </div>

    <!-- AI 对话区域 -->
    <!-- <div class="chat-section">
      <ChatDialog />
    </div> -->
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { getProjectsListWithTypes } from '@/api/projects'
import { getToolsList } from '@/api/tools'
import { formatDate } from '@/utils/format'
import { getTypesList } from '@/api/types'
import ChatDialog from './components/ChatDialog.vue'

const router = useRouter()

// 工具列表
const tools = ref([])
const isComponentMounted = ref(true)

// 获取工具列表
const fetchTools = async () => {
  try {
    const response = await getToolsList()
    if (response.code === 200 && isComponentMounted.value) {
      // 为工具添加toolType字段，用于跳转
      tools.value = (response.rows || []).map(tool => {
        // 根据工具名称或类型ID映射到对应的toolType
        let toolType = 'jm' // 默认建模工具
        if (tool.toolName) {
          const toolName = tool.toolName.toLowerCase()
          if (toolName.includes('选型') || toolName.includes('o型圈')) {
            toolType = 'o'
          } else if (toolName.includes('仿真')) {
            toolType = 'fz'
          } else if (toolName.includes('建模仿真')) {
            toolType = 'fzjm'
          }
        }
        return {
          ...tool,
          toolType
        }
      })
    } else if (isComponentMounted.value) {
      ElMessage.error(response.msg || '获取工具列表失败')
    }
  } catch (error) {
    if (isComponentMounted.value) {
      ElMessage.error('获取工具列表失败')
    }
  }
}

// 跳转到工具详情页
const goToToolDetail = (tool) => {
  const toolType = tool.toolType || 'jm'
  router.push(`/tools/details/${toolType}`)
}

// 最近项目列表
const recentProjects = ref([])
const projectLimit = ref(4)  // 默认显示4条

// 获取最近项目
const fetchRecentProjects = async () => {
  try {
    const response = await getProjectsListWithTypes({
      pageNum: 1,
      pageSize: 4,
      orderByColumn: 'updated_at',
      isAsc: false
    })
    if (response.code === 200 && isComponentMounted.value) {
      recentProjects.value = response.rows || []
    } else if (isComponentMounted.value) {
      ElMessage.error(response.msg || '获取最近项目失败')
    }
  } catch (error) {
    if (isComponentMounted.value) {
      ElMessage.error('获取最近项目失败')
    }
  }
}

const types = ref([])

// 获取类型列表
const fetchTypes = async () => {
  try {
    const res = await getTypesList({ pageNum: 1, pageSize: 100, isActive: 1 })
    if (res.code === 200 && isComponentMounted.value) {
      types.value = res.rows
    }
  } catch (e) {
    if (isComponentMounted.value) {
      ElMessage.error('获取类型列表失败')
    }
  }
}

// 获取类型名称
const getTypeName = (typeId) => {
  const type = types.value.find(t => t.typeId === typeId)
  return type ? type.displayName : '未知类型'
}

const getTypeTagType = (typeId) => {
  const type = types.value.find(t => t.typeId === typeId)
  const colorMap = { success: 'success', warning: 'warning', danger: 'danger', info: 'info' }
  return colorMap[type?.colorCode] || 'info'
}

// 双击跳转到项目详情页
const goToProjectDetail = (project) => {
  const projectId = project.projectId || project.project_id
  if (projectId) {
    router.push(`/projects/detail/${projectId}`)
  }
}

// 页面加载时获取数据
onMounted(() => {
  fetchTools()
  fetchRecentProjects()
  fetchTypes()
})

// 组件卸载时设置标志
onUnmounted(() => {
  isComponentMounted.value = false
})
</script>

<style scoped>
.home-container {
  padding: 30px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.section {
  margin-bottom: 40px;
}

.section-title {
  font-size: 28px;
  font-weight: 700;
  margin-bottom: 30px;
  color: #2c3e50;
  position: relative;
  padding-left: 20px;
}

.section-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 2px;
}

.tools-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 24px;
}

.tool-card {
  height: 100%;
  border: none;
  border-radius: 16px;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  overflow: hidden;
  cursor: pointer;
}

.tool-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 16px 48px rgba(102, 126, 234, 0.2);
}

.tool-card :deep(.el-card__header) {
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  padding: 20px;
}

.tool-card :deep(.el-card__body) {
  padding: 20px;
}

.tool-header {
  display: flex;
  align-items: center;
}

.tool-name {
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
}

.tool-description {
  color: #606266;
  line-height: 1.6;
  font-size: 14px;
}

.projects-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 24px;
}

.project-card {
  height: 100%;
  border: none;
  border-radius: 16px;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
}

.project-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 16px 48px rgba(102, 126, 234, 0.2);
}

.project-card :deep(.el-card__header) {
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  padding: 20px;
}

.project-card :deep(.el-card__body) {
  padding: 20px;
}

.project-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 12px;
}

.project-name {
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
  white-space: nowrap;
  flex: 1;
  min-width: 0;
  overflow: hidden;
  text-overflow: ellipsis;
}

.project-types {
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
}

.project-types :deep(.el-tag) {
  border-radius: 6px;
  font-weight: 500;
  border: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.project-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.project-description {
  color: #606266;
  line-height: 1.6;
  font-size: 14px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.project-time {
  color: #909399;
  font-size: 12px;
  font-weight: 500;
}

.chat-section {
  margin-top: 30vh;
  margin-bottom: 40px;
  display: flex;
  justify-content: center;
  width: 100%;
  max-width: 1200px;
  margin-left: auto;
  margin-right: auto;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .home-container {
    padding: 20px;
  }
  
  .section-title {
    font-size: 24px;
  }
  
  .tools-grid {
    grid-template-columns: 1fr;
  }
}
</style> 