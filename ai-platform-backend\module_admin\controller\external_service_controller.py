from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.ext.asyncio import AsyncSession

from config.get_db import get_db
from module_admin.service.external_service_service import ExternalServiceService
from utils.response_util import ResponseUtil

externalServiceController = APIRouter(prefix="/admin/external-service", tags=["外部服务监控"])


@externalServiceController.get("/monitor")
async def get_external_service_monitor(
    query_db: AsyncSession = Depends(get_db)
):
    """
    获取外部服务监控信息
    
    Returns:
        外部服务监控信息
    """
    try:
        monitor_info = await ExternalServiceService.get_external_services_monitor_info(query_db)
        return ResponseUtil.success(data=monitor_info)
    except Exception as e:
        return ResponseUtil.failure(msg=f"获取外部服务监控信息失败: {str(e)}")


@externalServiceController.post("/test/{tool_id}")
async def test_service_health(
    tool_id: int,
    query_db: AsyncSession = Depends(get_db)
):
    """
    测试单个服务的健康状态
    
    Args:
        tool_id: 工具ID
        
    Returns:
        测试结果
    """
    try:
        result = await ExternalServiceService.test_service_health(query_db, tool_id)
        if result["success"]:
            return ResponseUtil.success(data=result, msg=result["message"])
        else:
            return ResponseUtil.failure(data=result, msg=result["message"])
    except Exception as e:
        return ResponseUtil.failure(msg=f"测试服务健康状态失败: {str(e)}") 