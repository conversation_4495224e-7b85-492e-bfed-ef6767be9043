<template>
  <div v-if="toolInfo" class="details-container">
    <div class="left-panel">
      <el-card class="desc-card">
        <div class="tool-header">
          <img :src="toolInfo.logo" class="tool-logo" v-if="toolInfo.logo" />
          <span class="tool-title">{{ toolInfo.toolName || '工具' }}</span>
          <el-tag>{{ getToolTag(toolInfo.toolId) }}</el-tag>
        </div>
        <div class="tool-desc">{{ toolInfo.description || '暂无描述' }}</div>
        <div class="tool-info">
          <div class="info-item">
            <span class="label">供应商:</span>
            <span class="value">{{ toolInfo.vendor || '未知' }}</span>
          </div>
          <div class="info-item">
            <span class="label">版本:</span>
            <span class="value">{{ toolInfo.version || '未知' }}</span>
          </div>
        </div>
      </el-card>
      <el-card class="guide-card">
        <div class="guide-title">参数填写指南</div>
        <ul class="guide-list">
          <li>请根据实际工况条件填写相关参数</li>
          <li>温度范围应符合材料使用要求</li>
          <li>材料选择会影响工具性能和适用性</li>
                          <li>所有参数填写完成后点击创建任务</li>
        </ul>
      </el-card>
    </div>
    <div class="right-panel">
      <div class="panel-title">参数配置</div>
      <el-form :model="form" :rules="rules" ref="formRef" class="tool-form">
        <div class="form-grid" v-if="dynamicFormFields.length > 0">
          <div v-for="field in dynamicFormFields" :key="field.prop" class="form-item">
            <div class="form-label">{{ field.label }}</div>
            <div class="input-wrapper">
              <!-- 文件上传组件 -->
              <FileUpload
                v-if="field.type === 'FileUpload'"
                v-model="form[field.prop]"
                v-bind="field.attrs"
                :class="['form-input', 'file-upload-input']"
              />
              <!-- 其他组件 -->
              <component
                v-else
                :is="field.type"
                v-model="form[field.prop]"
                v-bind="field.attrs"
                :options="field.options"
                :class="['form-input', field.hasUnit ? 'has-unit' : '']"
              >
                <template v-if="field.type === 'el-select'" v-for="opt in field.options" :key="opt.value">
                  <el-option :label="opt.label" :value="opt.value" />
                </template>
              </component>
              <span v-if="field.unit" class="unit-label">{{ field.unit }}</span>
            </div>
          </div>
        </div>
        <el-empty v-else description="暂无表单项" />
        <div class="form-actions">
          <el-button @click="resetForm">重置</el-button>
          <el-button 
            type="primary" 
            @click="submitForm"
            :loading="submitting"
            :disabled="!form.projectName || !form.taskName"
          >
                          创建任务
          </el-button>
        </div>
      </el-form>
    </div>
  </div>
  <div v-else style="padding: 60px 0; text-align: center;">
    <el-empty description="未找到工具信息" />
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, getCurrentInstance, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { getTool } from '@/api/tools'
import { addTask, updateTask } from '@/api/tasks'
import { addProject } from '@/api/projects'
import { addProjectTypeRel } from '@/api/project-types-rel'
import FileUpload from '@/components/FileUpload/index.vue'

const route = useRoute()
const router = useRouter()
const toolType = String(route.params.toolType)

// 工具类型到ID的映射
const toolTypeToIdMap = {
  'jm': 1,    // 单向阀建模工具
  'o': 2,     // O型圈选型工具
  'fz': 3,    // 单向阀仿真工具
  'fzjm': 4   // 单向阀建模仿真工具
}

// 工具标签映射
const getToolTag = (toolId) => {
  const tagMap = {
    1: '建模',
    2: '选型',
    3: '仿真',
    4: '建模仿真'
  }
  return tagMap[toolId] || '默认'
}

// 工具信息
const toolInfo = ref(null)
const loading = ref(false)
const submitting = ref(false)

// 动态表单字段
const dynamicFormFields = ref([])

// 表单数据
const form = reactive({})
const rules = {
  projectName: [
    { required: true, message: '请输入项目名称', trigger: 'blur' }
  ],
  taskName: [
    { required: true, message: '请输入任务名称', trigger: 'blur' }
  ]
}
const formRef = ref(null)

// 获取工具信息
const fetchToolInfo = async () => {
  const toolId = toolTypeToIdMap[toolType]
  console.log('当前toolType:', toolType)
  console.log('映射的toolId:', toolId)
  
  if (!toolId) {
    ElMessage.error('无效的工具类型')
    return
  }

  loading.value = true
  try {
    const response = await getTool(toolId)
    console.log('API响应:', response)
    
    if (response.code === 200) {
      toolInfo.value = response.data
      console.log('工具信息:', toolInfo.value)
      generateDynamicFormFields()
      initForm()
    } else {
      ElMessage.error(response.msg || '获取工具信息失败')
    }
  } catch (error) {
    console.error('获取工具信息失败:', error)
    ElMessage.error('获取工具信息失败')
  } finally {
    loading.value = false
  }
}

// 生成属性名称的辅助函数
const generatePropName = (label) => {
  // 创建一个映射表，将中文标签转换为英文属性名
  const labelMap = {
    // 温度相关
    '应用低温（℃）': 'lowTemp',
    '应用高温（℃）': 'highTemp',
    '过冷/过热度（K）': 'operatingTemp',
    '工作温度': 'workingTemperature',
    
    // 压力相关
    '入口绝对压力（MPa）': 'inletAbsolutePressure',
    '最小开阀压差（目标压降值）Dp（Pa）': 'minOpeningPressureDiff',
    '公称压力': 'nominalPressure',
    '工作压力': 'workingPressure',
    
    // 流量相关
    '入口质量流量（kg/s）': 'inletMassFlow',
    '流量系数': 'flowCoefficient',
    
    // 材质相关
    'O型圈材质（HNBR/EPDM）': 'oRingMaterial',
    'O形圈材质（HNBR/EPDM）': 'oRingMaterial',
    '阀芯材质（PPS+40%GF/PA66+30%GF）': 'coreMaterial',
    '密封材料': 'sealMaterial',
    '阀体材料': 'bodyMaterial',
    
    // 尺寸相关
    '阀口直径d（mm）': 'valveDiameter',
    '阀口直径dmm': 'valveDiameter',
    '阀口直径(mm)': 'valveDiameter',
    '单向阀安装位置前后管路内径D1（mm）': 'pipeDiameter',
    '内壁密封斜度θ（°）': 'sealAngle',
    '阀芯导杆长度H4寻优范围（以设计值为中心）+（mm）': 'guideRodLengthOptRangePositive',
    '阀芯导杆长度H4寻优范围（以设计值为中心）-（mm）': 'guideRodLengthOptRangeNegative',
    '公称直径': 'nominalDiameter',
    
    // 工质相关
    '流体工质（R134a/R1234yf/R744/50EG）': 'fluidMedium',
    '介质类型': 'mediumType',
    
    // 计算参数
    '迭代步长（mm）': 'iterationStep',
    '迭代步长mm': 'iterationStep',
    '最大步数': 'maxSteps',
    
    // 文件上传相关
    '模型上传路径': 'modelFilePath',
    '模型文件上传': 'modelFilePath',
    '3D模型上传': 'modelFilePath',
    
    // 其他
    '连接方式': 'connectionType',
    '泄漏等级': 'leakageClass'
  }
  
  // 先检查是否有直接映射
  if (labelMap[label]) {
    return labelMap[label]
  }

  const result = label
    .replace(/[（）()℃°]/g, '')
    .replace(/[\/、]/g, '_')
    .replace(/\s+/g, '')
    .replace(/^(.)/, (match) => match.toLowerCase())

  return result
}

// 获取字段合理的默认值
const getDefaultValue = (key, configValue) => {
  // 为关键字段提供合理的默认值
  const defaultValues = {
    // 温度相关 - 提供常用的工况温度
    '过冷/过热度（K）': '25',
    '工作温度': '25',
    
    // 压力相关 - 提供常用的压力值
    '入口绝对压力（MPa）': '0.1',
    '公称压力': '1.0',
    '工作压力': '0.6',
    
    // 流量相关 - 提供常用的流量值
    '入口质量流量（kg/s）': '0.01',
    '流量系数': '1.0',
    
    // 工质相关 - 提供默认制冷剂
    '流体工质（R134a/R1234yf/R744/50EG）': 'R134a',
    '流体工质': 'R134a',
    '介质类型': 'R134a',
    
    // 计算参数
    '最大步数': '3000',
    '迭代步长（默认0.1mm）': '0.1'
  }
  
  // 如果配置值存在且不为空，使用配置值
  if (configValue && configValue.trim() !== '') {
    return configValue
  }
  
  // 否则使用默认值
  return defaultValues[key] || ''
}

// 根据configTemplate生成动态表单字段
const generateDynamicFormFields = () => {
  if (!toolInfo.value || !toolInfo.value.configTemplate) {
    dynamicFormFields.value = []
    return
  }

  const configTemplate = toolInfo.value.configTemplate
  const fields = []

  // 添加基础字段
  fields.push(
    { 
      label: '项目名称', 
      prop: 'projectName', 
      type: 'el-input', 
      attrs: { placeholder: '请输入项目名称' },
      hasUnit: false
    },
    { 
      label: '项目描述', 
      prop: 'projectDesc', 
      type: 'el-input', 
      attrs: { placeholder: '请输入项目描述' },
      hasUnit: false
    },
    { 
      label: '任务名称', 
      prop: 'taskName', 
      type: 'el-input', 
      attrs: { placeholder: '请输入任务名称' },
      hasUnit: false
    }
  )

  // 处理新的数据格式
  let orderedKeys = []
  let configData = configTemplate

  // 检查是否为嵌套结构
  if (configTemplate._order && Array.isArray(configTemplate._order)) {
    // 检查是否真的有嵌套结构
    const hasNestedSections = configTemplate._order.some(sectionKey => {
      const section = configTemplate[sectionKey]
      return section && typeof section === 'object' && section._order
    })
    
    if (hasNestedSections) {
      // 如果是嵌套结构，需要展平所有字段
      const allFields = []
      
      configTemplate._order.forEach(sectionKey => {
        const section = configTemplate[sectionKey]
        if (section && typeof section === 'object' && section._order) {
          // 这是一个子部分，如 model 或 simulation
          section._order.forEach(fieldKey => {
            allFields.push(fieldKey)
          })
        }
      })
      
      // 使用展平后的字段
      orderedKeys = allFields
    } else {
      // 扁平结构，直接使用 _order 字段指定的顺序
      orderedKeys = configTemplate._order
    }
  } else {
    // 兼容旧格式，使用 Object.keys()
    orderedKeys = Object.keys(configTemplate).filter(key => key !== '_order')
  }
  
  orderedKeys.forEach((key, index) => {
    const defaultValue = configData[key]
    const field = {
      label: key,
      prop: generatePropName(key),
      type: 'el-input',
      attrs: { placeholder: `请输入${key}` },
      hasUnit: false,
      unit: '',
      defaultValue: defaultValue,
      order: index // 添加排序索引
    }

    // 根据字段名称智能判断输入类型
    if (key.includes('上传路径') || key.includes('模型上传') || key.includes('文件上传') || key.includes('上传文件')) {
      // 文件上传字段
      field.type = 'FileUpload'
      
      // 根据具体的字段类型设置不同的文件类型限制
      if (key.includes('模型') || key.includes('3D')) {
        // 3D模型文件
        field.attrs = { 
          limit: 1,
          fileSize: 100, // 100MB限制，因为3D模型文件可能较大
          fileType: ['step', 'stp', 'iges', 'igs', 'stl', 'obj', '3mf', 'ply'],
          isShowTip: true
        }
      } else if (key.includes('图片') || key.includes('图像')) {
        // 图片文件
        field.attrs = { 
          limit: 1,
          fileSize: 10,
          fileType: ['jpg', 'jpeg', 'png', 'gif', 'bmp'],
          isShowTip: true
        }
      } else {
        // 通用文件
        field.attrs = { 
          limit: 1,
          fileSize: 50,
          fileType: ['doc', 'docx', 'xls', 'xlsx', 'pdf', 'txt', 'zip', 'rar'],
          isShowTip: true
        }
      }
      field.hasUnit = false
    } else if (key.includes('材质') || key.includes('材料')) {
      // 材质选择字段
      field.type = 'el-select'
      field.attrs = { placeholder: `请选择${key}` }
      
      // 根据默认值和字段名称生成选项
      if (key.includes('O型圈材质') || key.includes('O形圈材质')) {
        field.options = [
          { label: 'HNBR', value: 'HNBR' },
          { label: 'EPDM', value: 'EPDM' }
        ]
      } else if (key.includes('阀芯材质')) {
        field.options = [
          { label: 'PPS+40%GF', value: 'PPS+40%GF' },
          { label: 'PA66+30%GF', value: 'PA66+30%GF' }
        ]
      } else {
        // 通用材质选项，基于默认值生成
        const defaultVal = defaultValue.trim()
        field.options = [
          { label: defaultVal, value: defaultVal }
        ]
      }
      field.hasUnit = false
    } else if (key.includes('流体工质') || key.includes('工质')) {
      // 流体工质选择字段
      field.type = 'el-select'
      field.attrs = { placeholder: '请选择流体工质' }
      field.options = [
        { label: 'R134a', value: 'R134a' },
        { label: 'R1234yf', value: 'R1234yf' },
        { label: 'R744', value: 'R744' },
        { label: '50EG', value: '50EG' }
      ]
      field.hasUnit = false
    } else if (key.includes('温度') || key.includes('温（℃）')) {
      // 温度输入字段
      field.attrs.type = 'number'
      field.unit = '℃'
      field.hasUnit = true
    } else if (key.includes('直径') || key.includes('内径') || key.includes('(mm)')) {
      // 直径/长度输入字段
      field.attrs.type = 'number'
      field.unit = 'mm'
      field.hasUnit = true
    } else if (key.includes('角度') || key.includes('斜度') || key.includes('(°)')) {
      // 角度输入字段
      field.attrs.type = 'number'
      field.unit = '°'
      field.hasUnit = true
    }

    fields.push(field)
  })

  // 确保按照原始顺序排列字段
  const sortedFields = fields.sort((a, b) => a.order - b.order)
  console.log('动态表单字段顺序:', sortedFields.map(f => ({ label: f.label, order: f.order })))
  dynamicFormFields.value = sortedFields
}

// 初始化表单
const initForm = () => {
  if (dynamicFormFields.value.length > 0) {
    // 按照字段的order属性顺序初始化（确保与configTemplate中的key顺序一致）
    const sortedFields = [...dynamicFormFields.value].sort((a, b) => a.order - b.order)
    sortedFields.forEach(field => {
      // 文件上传字段初始化为空字符串
      if (field.type === 'FileUpload') {
        form[field.prop] = ''
      } else {
        // 使用改进的默认值获取逻辑
        form[field.prop] = getDefaultValue(field.label, field.defaultValue)
      }
    })
  }
}

// 重置表单
function resetForm() {
  // 重置到默认值
  if (dynamicFormFields.value.length > 0) {
    // 按照字段的order属性顺序重置（确保与configTemplate中的key顺序一致）
    const sortedFields = [...dynamicFormFields.value].sort((a, b) => a.order - b.order)
    sortedFields.forEach(field => {
      if (['projectName', 'projectDesc', 'taskName'].includes(field.prop)) {
        form[field.prop] = '' // 项目和任务相关字段重置为空
      } else if (field.type === 'FileUpload') {
        form[field.prop] = '' // 文件上传字段重置为空
      } else {
        // 使用改进的默认值获取逻辑
        form[field.prop] = getDefaultValue(field.label, field.defaultValue)
      }
    })
  }
  if (formRef.value) formRef.value.clearValidate()
}

// 提交表单
const submitForm = async () => {
  try {
    // 验证表单
    if (formRef.value) {
      await formRef.value.validate()
    } else {
      console.error('表单引用不存在')
      ElMessage.error('表单验证失败')
      return
    }
    
    submitting.value = true
    
    // 验证必填字段
    if (!form.projectName) {
      ElMessage.error('项目名称不能为空')
      submitting.value = false
      return
    }
    
    if (!form.taskName) {
      ElMessage.error('任务名称不能为空')
      submitting.value = false
      return
    }
    
    if (!toolInfo.value || !toolInfo.value.typeId) {
      console.error('工具信息不完整:', toolInfo.value)
      ElMessage.error('工具信息不完整')
      submitting.value = false
      return
    }
    
    // 验证关键参数字段
    if (toolInfo.value.configTemplate) {
      // 处理新的数据格式
      const configTemplate = toolInfo.value.configTemplate
      let orderedKeys = []

      // 检查是否为嵌套结构
      if (configTemplate._order && Array.isArray(configTemplate._order)) {
        // 检查是否真的有嵌套结构
        const hasNestedSections = configTemplate._order.some(sectionKey => {
          const section = configTemplate[sectionKey]
          return section && typeof section === 'object' && section._order
        })
        
        if (hasNestedSections) {
          // 如果是嵌套结构，需要展平所有字段
          const allFields = []
          
          configTemplate._order.forEach(sectionKey => {
            const section = configTemplate[sectionKey]
            if (section && typeof section === 'object' && section._order) {
              // 这是一个子部分，如 model 或 simulation
              section._order.forEach(fieldKey => {
                allFields.push(fieldKey)
              })
            }
          })
          
          // 使用展平后的字段
          orderedKeys = allFields
        } else {
          // 扁平结构，直接使用 _order 字段指定的顺序
          orderedKeys = configTemplate._order
        }
      } else {
        // 兼容旧格式，使用 Object.keys()
        orderedKeys = Object.keys(configTemplate).filter(key => key !== '_order')
      }

      const requiredFields = [
        '过冷/过热度（K）',
        '入口绝对压力（MPa）',
        '入口质量流量（kg/s）',
        '流体工质（R134a/R1234yf/R744/50EG）',
        '最大步数'
      ]
      
      for (let key of orderedKeys) {
        const propName = generatePropName(key)
        const fieldValue = form[propName]
        
        // 检查必填字段
        if (requiredFields.includes(key)) {
          if (!fieldValue || fieldValue.toString().trim() === '') {
            ElMessage.error(`${key} 为必填字段，请填写`)
            submitting.value = false
            return
          }
        }
      }
    }
    
    // 筛选需要上传的模型文件
    const modelFilesToUpload = {}
    if (toolInfo.value.configTemplate) {
      // 处理新的数据格式
      const configTemplate = toolInfo.value.configTemplate
      let orderedKeys = []

      // 检查是否为嵌套结构
      if (configTemplate._order && Array.isArray(configTemplate._order)) {
        // 检查是否真的有嵌套结构
        const hasNestedSections = configTemplate._order.some(sectionKey => {
          const section = configTemplate[sectionKey]
          return section && typeof section === 'object' && section._order
        })
        
        if (hasNestedSections) {
          // 如果是嵌套结构，需要展平所有字段
          const allFields = []
          
          configTemplate._order.forEach(sectionKey => {
            const section = configTemplate[sectionKey]
            if (section && typeof section === 'object' && section._order) {
              // 这是一个子部分，如 model 或 simulation
              section._order.forEach(fieldKey => {
                allFields.push(fieldKey)
              })
            }
          })
          
          // 使用展平后的字段
          orderedKeys = allFields
        } else {
          // 扁平结构，直接使用 _order 字段指定的顺序
          orderedKeys = configTemplate._order
        }
      } else {
        // 兼容旧格式，使用 Object.keys()
        orderedKeys = Object.keys(configTemplate).filter(key => key !== '_order')
      }

      const modelUploadFields = orderedKeys.filter(key => 
        key.includes('模型上传') || key.includes('模型文件') || 
        (key.includes('上传') && (key.includes('模型') || key.includes('3D')))
      )
      
      for (let key of modelUploadFields) {
        const propName = generatePropName(key)
        if (!form[propName] || form[propName].trim() === '') {
          ElMessage.error(`请上传${key.replace(/路径|上传|模型/g, '')}`)
          submitting.value = false
          return
        }
        
        // 这里需要从FileUpload组件获取实际的文件对象
        // 暂时跳过文件检查，因为这个页面是即时上传的
      }
    }
    
    console.log('当前工具信息:', toolInfo.value)
    console.log('表单数据:', form)
    
    // 步骤1: 先创建项目
    ElMessage.info('正在创建项目...')
    const projectData = {
      projectName: form.projectName.trim(),
      description: form.projectDesc || '',
      isDeleted: 0
    }
    
    console.log('创建项目数据:', projectData)
    
    const projectRes = await addProject(projectData)
    console.log('项目创建响应:', projectRes)
    
    if (projectRes.code !== 200) {
      console.error('项目创建失败:', projectRes)
      ElMessage.error(projectRes.msg || '项目创建失败')
      submitting.value = false
      return
    }
    
    const projectId = projectRes.data.projectId
    console.log('项目ID:', projectId)
    
    // 步骤2: 添加项目类型关联
    try {
      const typeRelData = {
        projectId: projectId,
        typeId: parseInt(toolInfo.value.typeId)
      }
      console.log('添加项目类型关联数据:', typeRelData)
      
      const typeRelRes = await addProjectTypeRel(typeRelData)
      console.log('项目类型关联响应:', typeRelRes)
    } catch (error) {
      console.warn('添加项目类型关联失败:', error)
    }
    
    // 步骤3: 准备任务参数（排除项目和任务相关字段）
    const parametersForm = {}
    // 按照configTemplate的原始顺序收集参数
    if (toolInfo.value.configTemplate) {
      // 处理新的数据格式
      const configTemplate = toolInfo.value.configTemplate
      let orderedKeys = []

      // 检查是否为嵌套结构
      if (configTemplate._order && Array.isArray(configTemplate._order)) {
        // 检查是否真的有嵌套结构
        const hasNestedSections = configTemplate._order.some(sectionKey => {
          const section = configTemplate[sectionKey]
          return section && typeof section === 'object' && section._order
        })
        
        if (hasNestedSections) {
          // 如果是嵌套结构，需要展平所有字段
          const allFields = []
          
          configTemplate._order.forEach(sectionKey => {
            const section = configTemplate[sectionKey]
            if (section && typeof section === 'object' && section._order) {
              // 这是一个子部分，如 model 或 simulation
              section._order.forEach(fieldKey => {
                allFields.push(fieldKey)
              })
            }
          })
          
          // 使用展平后的字段
          orderedKeys = allFields
        } else {
          // 扁平结构，直接使用 _order 字段指定的顺序
          orderedKeys = configTemplate._order
        }
      } else {
        // 兼容旧格式，使用 Object.keys()
        orderedKeys = Object.keys(configTemplate).filter(key => key !== '_order')
      }

      orderedKeys.forEach(key => {
        const propName = generatePropName(key)
        if (form[propName] !== undefined) {
          parametersForm[propName] = form[propName]
        }
      })
    }
    
    console.log('过滤后的参数表单:', parametersForm)
    
    // 步骤4: 创建任务
    ElMessage.info('正在创建任务...')
    const taskData = {
      taskName: form.taskName.trim(),
      projectId: projectId,
      typeId: parseInt(toolInfo.value.typeId),
      toolId: parseInt(toolInfo.value.toolId),
      parameters: parametersForm || {},
      description: `使用工具: ${toolInfo.value.toolName}`,
      status: 'pending',
      progress: 0,
      isDeleted: 0
    }
    
    // 验证关键字段
    if (!taskData.projectId || !taskData.typeId || !taskData.toolId) {
      console.error('任务数据验证失败:', {
        projectId: taskData.projectId,
        typeId: taskData.typeId,
        toolId: taskData.toolId
      })
      ElMessage.error('任务数据不完整')
      submitting.value = false
      return
    }
    
    console.log('创建任务数据:', taskData)
    console.log('参数表单数据:', parametersForm)
    
    const taskRes = await addTask(taskData)
    console.log('任务创建响应:', taskRes)
    
    if (taskRes.code === 200) {
      ElMessage.success('项目和任务创建成功！')
      
      // 跳转到项目详情页面
      router.push(`/projects/detail/${projectId}`)
    } else {
      console.error('任务创建失败:', taskRes)
      ElMessage.error(taskRes.msg || taskRes.message || '创建任务失败')
    }
  } catch (error) {
    console.error('提交失败详细信息:', error)
    console.error('错误对象:', JSON.stringify(error, null, 2))
    
    // 处理不同类型的错误
    if (error.response) {
      const status = error.response.status
      const data = error.response.data
      
      if (status === 400) {
        ElMessage.error(data.msg || '请求参数错误')
      } else if (status === 401) {
        ElMessage.error('登录已过期，请重新登录')
      } else if (status === 403) {
        ElMessage.error('没有权限执行此操作')
      } else if (status === 500) {
        ElMessage.error('服务器内部错误')
      } else {
        ElMessage.error(data.msg || '创建失败')
      }
    } else if (error.request) {
      ElMessage.error('网络连接失败，请检查网络设置')
    } else {
      ElMessage.error('创建失败: ' + error.message)
    }
  } finally {
    submitting.value = false
  }
}

// 页面加载时获取工具信息
onMounted(() => {
  fetchToolInfo()
})
</script>

<style scoped>
.details-container {
  display: flex;
  height: calc(100vh - 60px); /* 顶栏高度 */
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 30px;
  box-sizing: border-box;
}
.left-panel {
  width: 30%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  gap: 10%;
}
.desc-card, .guide-card {
  flex: 1;
  margin-bottom: 0;
  border-radius: 16px;
  background: #fff;
  box-shadow: 0 4px 24px rgba(102, 126, 234, 0.08);
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 24px;
}
.guide-card {
  margin-top: 0;
  background: #E3F2FD;
}
.tool-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}
.tool-logo {
  width: 36px;
  height: 36px;
}
.tool-title {
  font-size: 18px;
  font-weight: 600;
}
.tool-desc {
  color: #606266;
  font-size: 14px;
  line-height: 1.6;
  margin-bottom: 16px;
}
.tool-info {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}
.info-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 14px;
}
.info-item .label {
  color: #909399;
  font-weight: 500;
}
.info-item .value {
  color: #606266;
  font-weight: 600;
}
.guide-title {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 8px;
}
.guide-list {
  padding-left: 18px;
  color: #909399;
  font-size: 13px;
  line-height: 1.7;
}
.right-panel {
  flex: 1;
  margin-left: 3%;
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 4px 24px rgba(102, 126, 234, 0.08);
  padding: 32px 24px 24px 24px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.tool-form {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.form-actions {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-top: auto;
  padding-top: 20px;
}
.panel-title {
  font-size: 30px;
  font-weight: 600;
  margin-bottom: 20px;
}
.form-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}
.form-item {
  width: calc(50% - 8px);
  min-width: 500px;
}
.form-label {
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 8px;
}
.form-input {
  width: 100%;
  min-height: 50px;
  min-width: 200px;
  max-width: 100%;
}

.form-input:deep(.el-textarea__inner) {
  min-height: 80px;
}
.form-input.has-unit {
  padding-right: 25px;
  width: 100%;
  min-width: 200px;
  max-width: 100%;
}
.input-wrapper {
  position: relative;
  width: 100%;
  min-width: 200px;
  max-width: 100%;
}
.unit-label {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  color: #909399;
  font-size: 13px;
}

.file-upload-input {
  height: auto;
  min-height: 50px;
}

.file-upload-input :deep(.upload-file) {
  width: 100%;
}

.file-upload-input :deep(.upload-file-uploader) {
  margin-bottom: 10px;
}

.file-upload-input :deep(.el-upload__tip) {
  margin: 5px 0;
  color: #909399;
  font-size: 12px;
}
</style>
