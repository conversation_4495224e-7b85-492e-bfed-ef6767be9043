import request from '@/utils/request'

// 提交任务到队列
export function submitTaskToQueue(taskId) {
  return request({
    url: `/business/task-queue/submit/${taskId}`,
    method: 'post',
    timeout: 30000  // 增加超时时间到30秒，避免任务提交时的超时问题
  })
}

// 获取任务在队列中的位置
export function getTaskQueuePosition(taskId) {
  return request({
    url: `/business/task-queue/position/${taskId}`,
    method: 'get'
  })
}

// 获取队列长度
export function getQueueLength() {
  return request({
    url: '/business/task-queue/length',
    method: 'get'
  })
}

// 获取队列状态
export function getQueueStatus() {
  return request({
    url: '/business/task-queue/status',
    method: 'get'
  })
}

// 清空队列（管理员功能）
export function clearQueue() {
  return request({
    url: '/business/task-queue/clear',
    method: 'delete'
  })
} 