import asyncio
from typing import Optional
from redis import asyncio as aioredis
from sqlalchemy.ext.asyncio import AsyncSession
from module_scheduler.service.task_scheduler_service import TaskSchedulerService
from utils.log_util import logger


class SchedulerManager:
    """
    任务调度器管理器
    负责管理任务调度器的生命周期
    """
    
    def __init__(self):
        self.scheduler_service: Optional[TaskSchedulerService] = None
        self.redis_client: Optional[aioredis.Redis] = None
        self.db_session: Optional[AsyncSession] = None
        self.is_initialized = False
    
    async def initialize(self, redis_client: aioredis.Redis, db_session: AsyncSession):
        """
        初始化调度器管理器
        
        Args:
            redis_client: Redis客户端
            db_session: 数据库会话
        """
        try:
            self.redis_client = redis_client
            self.db_session = db_session
            self.scheduler_service = TaskSchedulerService(redis_client, db_session)
            self.is_initialized = True
            logger.info("任务调度器管理器初始化成功")
        except Exception as e:
            logger.error(f"任务调度器管理器初始化失败: {e}")
            raise
    
    async def start_scheduler(self):
        """
        启动任务调度器
        """
        if not self.is_initialized:
            logger.error("调度器管理器未初始化")
            return False
        
        try:
            success = await self.scheduler_service.start_scheduler()
            if success:
                logger.info("任务调度器启动成功")
            else:
                logger.error("任务调度器启动失败")
            return success
        except Exception as e:
            logger.error(f"启动任务调度器失败: {e}")
            return False
    
    async def stop_scheduler(self):
        """
        停止任务调度器
        """
        if not self.is_initialized or not self.scheduler_service:
            return
        
        try:
            await self.scheduler_service.stop_scheduler()
            logger.info("任务调度器停止成功")
        except Exception as e:
            logger.error(f"停止任务调度器失败: {e}")
    
    async def get_scheduler_status(self):
        """
        获取调度器状态
        
        Returns:
            调度器状态信息
        """
        if not self.is_initialized or not self.scheduler_service:
            return {
                "is_initialized": False,
                "is_running": False,
                "error": "调度器未初始化"
            }
        
        try:
            status = await self.scheduler_service.get_scheduler_status()
            status["is_initialized"] = True
            return status
        except Exception as e:
            logger.error(f"获取调度器状态失败: {e}")
            return {
                "is_initialized": True,
                "is_running": False,
                "error": str(e)
            }


# 全局调度器管理器实例
scheduler_manager = SchedulerManager() 