from fastapi import APIRouter, Depends, Form, Request, Query
from pydantic_validation_decorator import <PERSON><PERSON>te<PERSON>ields
from sqlalchemy.ext.asyncio import AsyncSession
from config.enums import BusinessType
from config.get_db import get_db
from module_admin.annotation.log_annotation import Log
from module_admin.aspect.interface_auth import CheckUserInterfaceAuth
from module_admin.entity.vo.user_vo import CurrentUserModel
from module_admin.service.login_service import LoginService
from module_business.service.kb_types_rel_service import KbTypesRelService
from module_business.entity.vo.kb_types_rel_vo import DeleteKbTypesRelModel, KbTypesRelModel, KbTypesRelPageQueryModel
from module_business.entity.vo.knowledge_bases_vo import KnowledgeBasesModel
from utils.common_util import bytes2file_response
from utils.log_util import logger
from utils.page_util import PageResponseModel
from utils.response_util import ResponseUtil
from datetime import datetime
from typing import List


kb_types_relController = APIRouter(prefix='/business/kb_types_rel', dependencies=[Depends(LoginService.get_current_user)])


@kb_types_relController.get(
    '/list', response_model=PageResponseModel  # dependencies=[Depends(CheckUserInterfaceAuth('business:kb_types_rel:list'))]
)
async def get_business_kb_types_rel_list(
    request: Request,
kb_types_rel_page_query: KbTypesRelPageQueryModel = Depends(),
    query_db: AsyncSession = Depends(get_db),
):
    # 获取分页数据
    kb_types_rel_page_query_result = await KbTypesRelService.get_kb_types_rel_list_services(query_db, kb_types_rel_page_query, is_page=True)
    logger.info('获取成功')

    return ResponseUtil.success(model_content=kb_types_rel_page_query_result)


@kb_types_relController.post('')  # dependencies=[Depends(CheckUserInterfaceAuth('business:kb_types_rel:add'))]
@ValidateFields(validate_model='add_kb_types_rel')
@Log(title='数据库-类型关联关系', business_type=BusinessType.INSERT)
async def add_business_kb_types_rel(
    request: Request,
    add_kb_types_rel: KbTypesRelModel,
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    add_kb_types_rel.assigned_at = datetime.now()
    add_kb_types_rel.assigned_by = current_user.user.user_id
    add_kb_types_rel_result = await KbTypesRelService.add_kb_types_rel_services(query_db, add_kb_types_rel)
    logger.info(add_kb_types_rel_result.message)

    return ResponseUtil.success(msg=add_kb_types_rel_result.message)


@kb_types_relController.get(
    '/{kb_id}', response_model=KbTypesRelModel  # dependencies=[Depends(CheckUserInterfaceAuth('business:kb_types_rel:query'))]
)
async def query_detail_business_kb_types_rel(request: Request, kb_id: int, query_db: AsyncSession = Depends(get_db)):
    kb_types_rel_detail_result = await KbTypesRelService.kb_types_rel_detail_services(query_db, kb_id)
    logger.info(f'获取kb_id为{kb_id}的信息成功')

    return ResponseUtil.success(data=kb_types_rel_detail_result)


@kb_types_relController.get(
    '/list/{kb_id}', response_model=List[KbTypesRelModel]  # dependencies=[Depends(CheckUserInterfaceAuth('business:kb_types_rel:query'))]
)
async def query_kb_types_rel_list(
    request: Request,
    kb_id: int,
    query_db: AsyncSession = Depends(get_db),
):
    kb_types_rel_list_result = await KbTypesRelService.get_kb_types_rel_list_by_kb_id_services(query_db, kb_id)
    logger.info(f'获取kb_id为{kb_id}的所有关联类型信息成功')

    return ResponseUtil.success(data=kb_types_rel_list_result)


@kb_types_relController.get("/knowledge_bases/by_types")
async def get_knowledge_bases_by_types(
    type_ids: str = Query("", description="类型ID列表，逗号分隔"),
    page_num: int = Query(1, description="页码"),
    page_size: int = Query(10, description="每页大小"),
    kb_name: str = Query(None, description="数据库名称搜索"),
    query_db: AsyncSession = Depends(get_db)
):
    """
    根据类型ID列表获取数据库列表
    """
    try:
        # 解析类型ID列表
        type_id_list = []
        if type_ids:
            type_id_list = [int(tid.strip()) for tid in type_ids.split(',') if tid.strip()]
        
        result = await KbTypesRelService.get_knowledge_bases_by_types_services(
            query_db, type_id_list, page_num, page_size, kb_name
        )
        return ResponseUtil.success(data=result)
    except Exception as e:
        return ResponseUtil.failure(msg=str(e))
