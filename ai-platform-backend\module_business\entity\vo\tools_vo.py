from datetime import datetime
from pydantic import BaseModel, ConfigDict, Field
from pydantic.alias_generators import to_camel
from pydantic_validation_decorator import NotBlank
from typing import Optional
from module_admin.annotation.pydantic_annotation import as_query




class ToolsModel(BaseModel):
    """
    工具管理表对应pydantic模型
    """
    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True)

    tool_id: Optional[int] = Field(default=None, description='工具id')
    tool_name: Optional[str] = Field(default=None, description='工具名称')
    type_id: Optional[int] = Field(default=None, description='关联rd_sys_types中的类型')
    description: Optional[str] = Field(default=None, description='工具描述')
    version: Optional[str] = Field(default=None, description='版本号')
    vendor: Optional[str] = Field(default=None, description='供应商')
    executable_api: Optional[str] = Field(default=None, description='接口调用路径')
    health_url: Optional[str] = Field(default=None, description='健康检查URL')
    config_template: Optional[dict] = Field(default=None, description='配置模板(JSON格式)')
    is_active: Optional[int] = Field(default=1, description='是否激活')
    is_deleted: Optional[int] = Field(default=0, description='是否删除')
    queue_required: Optional[int] = Field(default=0, description='是否需要队列')
    is_health: Optional[int] = Field(default=1, description='是否启用健康检查')
    remark: Optional[str] = Field(default=None, description='备注/错误信息')
    created_at: Optional[datetime] = Field(default=None, description='创建时间')
    updated_at: Optional[datetime] = Field(default=None, description='更新时间')

    @NotBlank(field_name='tool_name', message='工具名称不能为空')
    def get_tool_name(self):
        return self.tool_name

    @NotBlank(field_name='type_id', message='关联rd_sys_types中的类型不能为空')
    def get_type_id(self):
        return self.type_id


    def validate_fields(self):
        self.get_tool_name()
        self.get_type_id()




class ToolsQueryModel(ToolsModel):
    """
    工具管理不分页查询模型
    """
    pass


@as_query
class ToolsPageQueryModel(ToolsQueryModel):
    """
    工具管理分页查询模型
    """

    page_num: int = Field(default=1, description='当前页码')
    page_size: int = Field(default=10, description='每页记录数')


class DeleteToolsModel(BaseModel):
    """
    删除工具管理模型
    """

    model_config = ConfigDict(alias_generator=to_camel)

    tool_ids: str = Field(description='需要删除的工具id')
