"""
角色常量测试
"""
import pytest
from config.role_constants import RoleConstants


class TestRoleConstants:
    """角色常量测试类"""

    def test_role_constants_values(self):
        """测试角色常量值"""
        assert RoleConstants.SUPER_ADMIN == 'super_admin'
        assert RoleConstants.PROJECT_MANAGER == 'project_manager'
        assert RoleConstants.PROJECT_MEMBER == 'project_member'
        assert RoleConstants.COMMON_USER == 'common_user'

    def test_role_names(self):
        """测试角色名称"""
        assert RoleConstants.ROLE_NAMES[RoleConstants.SUPER_ADMIN] == '超级管理员'
        assert RoleConstants.ROLE_NAMES[RoleConstants.PROJECT_MANAGER] == '项目管理员'
        assert RoleConstants.ROLE_NAMES[RoleConstants.PROJECT_MEMBER] == '项目成员'
        assert RoleConstants.ROLE_NAMES[RoleConstants.COMMON_USER] == '普通用户'

    def test_project_role_types(self):
        """测试项目角色类型"""
        assert RoleConstants.PROJECT_ROLE_MANAGER == 'project_manager'
        assert RoleConstants.PROJECT_ROLE_MEMBER == 'project_member'

    def test_role_levels(self):
        """测试角色级别"""
        assert RoleConstants.ROLE_LEVELS[RoleConstants.SUPER_ADMIN] == 1
        assert RoleConstants.ROLE_LEVELS[RoleConstants.PROJECT_MANAGER] == 2
        assert RoleConstants.ROLE_LEVELS[RoleConstants.PROJECT_MEMBER] == 3
        assert RoleConstants.ROLE_LEVELS[RoleConstants.COMMON_USER] == 4

    def test_data_scope_constants(self):
        """测试数据权限范围常量"""
        assert RoleConstants.DATA_SCOPE_ALL == '1'
        assert RoleConstants.DATA_SCOPE_CUSTOM == '2'
        assert RoleConstants.DATA_SCOPE_DEPT == '3'
        assert RoleConstants.DATA_SCOPE_DEPT_AND_CHILD == '4'
        assert RoleConstants.DATA_SCOPE_SELF == '5'

    def test_is_super_admin(self):
        """测试超级管理员判断"""
        assert RoleConstants.is_super_admin(['super_admin']) is True
        assert RoleConstants.is_super_admin(['project_manager']) is False
        assert RoleConstants.is_super_admin(['project_member']) is False
        assert RoleConstants.is_super_admin(['common_user']) is False
        assert RoleConstants.is_super_admin(['super_admin', 'project_manager']) is True
        assert RoleConstants.is_super_admin([]) is False

    def test_is_project_manager(self):
        """测试项目管理员判断"""
        assert RoleConstants.is_project_manager(['super_admin']) is False
        assert RoleConstants.is_project_manager(['project_manager']) is True
        assert RoleConstants.is_project_manager(['project_member']) is False
        assert RoleConstants.is_project_manager(['common_user']) is False
        assert RoleConstants.is_project_manager(['project_manager', 'project_member']) is True
        assert RoleConstants.is_project_manager([]) is False

    def test_is_project_member(self):
        """测试项目成员判断"""
        assert RoleConstants.is_project_member(['super_admin']) is False
        assert RoleConstants.is_project_member(['project_manager']) is False
        assert RoleConstants.is_project_member(['project_member']) is True
        assert RoleConstants.is_project_member(['common_user']) is False
        assert RoleConstants.is_project_member(['project_member', 'common_user']) is True
        assert RoleConstants.is_project_member([]) is False

    def test_is_common_user(self):
        """测试普通用户判断"""
        assert RoleConstants.is_common_user(['super_admin']) is False
        assert RoleConstants.is_common_user(['project_manager']) is False
        assert RoleConstants.is_common_user(['project_member']) is False
        assert RoleConstants.is_common_user(['common_user']) is True
        assert RoleConstants.is_common_user(['common_user', 'project_member']) is True
        assert RoleConstants.is_common_user([]) is False

    def test_get_highest_role_level(self):
        """测试获取最高权限级别"""
        assert RoleConstants.get_highest_role_level(['super_admin']) == 1
        assert RoleConstants.get_highest_role_level(['project_manager']) == 2
        assert RoleConstants.get_highest_role_level(['project_member']) == 3
        assert RoleConstants.get_highest_role_level(['common_user']) == 4
        assert RoleConstants.get_highest_role_level(['super_admin', 'project_manager']) == 1
        assert RoleConstants.get_highest_role_level(['project_manager', 'project_member']) == 2
        assert RoleConstants.get_highest_role_level(['unknown_role']) == 999
        assert RoleConstants.get_highest_role_level([]) == 999

    def test_can_manage_projects(self):
        """测试是否可以管理项目"""
        assert RoleConstants.can_manage_projects(['super_admin']) is True
        assert RoleConstants.can_manage_projects(['project_manager']) is True
        assert RoleConstants.can_manage_projects(['project_member']) is False
        assert RoleConstants.can_manage_projects(['common_user']) is False
        assert RoleConstants.can_manage_projects(['super_admin', 'project_member']) is True
        assert RoleConstants.can_manage_projects([]) is False

    def test_can_access_database(self):
        """测试是否可以访问数据库管理"""
        assert RoleConstants.can_access_database(['super_admin']) is True
        assert RoleConstants.can_access_database(['project_manager']) is True
        assert RoleConstants.can_access_database(['project_member']) is False
        assert RoleConstants.can_access_database(['common_user']) is False
        assert RoleConstants.can_access_database(['project_manager', 'project_member']) is True
        assert RoleConstants.can_access_database([]) is False

    def test_can_manage_users(self):
        """测试是否可以管理用户"""
        assert RoleConstants.can_manage_users(['super_admin']) is True
        assert RoleConstants.can_manage_users(['project_manager']) is False
        assert RoleConstants.can_manage_users(['project_member']) is False
        assert RoleConstants.can_manage_users(['common_user']) is False
        assert RoleConstants.can_manage_users(['super_admin', 'project_manager']) is True
        assert RoleConstants.can_manage_users([]) is False

    def test_can_view_all_data(self):
        """测试是否可以查看所有数据"""
        assert RoleConstants.can_view_all_data(['super_admin']) is True
        assert RoleConstants.can_view_all_data(['project_manager']) is False
        assert RoleConstants.can_view_all_data(['project_member']) is False
        assert RoleConstants.can_view_all_data(['common_user']) is False
        assert RoleConstants.can_view_all_data(['super_admin', 'project_manager']) is True
        assert RoleConstants.can_view_all_data([]) is False

    def test_role_hierarchy(self):
        """测试角色层次结构"""
        # 超级管理员 > 项目管理员 > 项目成员 > 普通用户
        super_admin_level = RoleConstants.get_highest_role_level(['super_admin'])
        project_manager_level = RoleConstants.get_highest_role_level(['project_manager'])
        project_member_level = RoleConstants.get_highest_role_level(['project_member'])
        common_user_level = RoleConstants.get_highest_role_level(['common_user'])
        
        assert super_admin_level < project_manager_level
        assert project_manager_level < project_member_level
        assert project_member_level < common_user_level

    def test_multiple_roles(self):
        """测试多角色情况"""
        # 用户同时拥有多个角色时，应该取最高权限
        roles = ['project_member', 'project_manager', 'common_user']
        
        assert RoleConstants.is_project_manager(roles) is True
        assert RoleConstants.get_highest_role_level(roles) == 2
        assert RoleConstants.can_manage_projects(roles) is True

    def test_edge_cases(self):
        """测试边界情况"""
        # 空角色列表
        assert RoleConstants.is_super_admin([]) is False
        assert RoleConstants.get_highest_role_level([]) == 999
        assert RoleConstants.can_manage_projects([]) is False
        
        # 不存在的角色
        assert RoleConstants.is_super_admin(['unknown_role']) is False
        assert RoleConstants.get_highest_role_level(['unknown_role']) == 999
        
        # None值处理（如果方法支持的话）
        try:
            assert RoleConstants.is_super_admin(None) is False
        except (TypeError, AttributeError):
            pass  # 如果方法不支持None值，跳过测试

    def test_role_permissions_matrix(self):
        """测试角色权限矩阵"""
        roles_permissions = {
            'super_admin': {
                'manage_projects': True,
                'access_database': True,
                'manage_users': True,
                'view_all_data': True,
            },
            'project_manager': {
                'manage_projects': True,
                'access_database': True,
                'manage_users': False,
                'view_all_data': False,
            },
            'project_member': {
                'manage_projects': False,
                'access_database': False,
                'manage_users': False,
                'view_all_data': False,
            },
            'common_user': {
                'manage_projects': False,
                'access_database': False,
                'manage_users': False,
                'view_all_data': False,
            },
        }
        
        for role, permissions in roles_permissions.items():
            role_list = [role]
            assert RoleConstants.can_manage_projects(role_list) == permissions['manage_projects']
            assert RoleConstants.can_access_database(role_list) == permissions['access_database']
            assert RoleConstants.can_manage_users(role_list) == permissions['manage_users']
            assert RoleConstants.can_view_all_data(role_list) == permissions['view_all_data']
