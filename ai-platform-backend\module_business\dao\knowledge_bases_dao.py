from sqlalchemy import delete, select, update
from sqlalchemy.ext.asyncio import AsyncSession
from module_business.entity.do.knowledge_bases_do import RdKnowledgeBases
from module_business.entity.vo.knowledge_bases_vo import KnowledgeBasesModel, KnowledgeBasesPageQueryModel
from utils.page_util import PageUtil
from utils.log_util import logger


class KnowledgeBasesDao:
    """
    数据库管理模块数据库操作层
    """

    @classmethod
    async def _get_kb_by_id(cls, db: AsyncSession, kb_id: int):
        """
        根据ID获取数据库基本信息（私有方法）
        """
        return (
            await db.execute(
                select(RdKnowledgeBases).where(RdKnowledgeBases.kb_id == kb_id)
            )
        ).scalars().first()

    @classmethod
    async def _get_kb_types(cls, db: AsyncSession, kb_id: int):
        """
        获取数据库关联的类型信息（私有方法）
        """
        from module_business.entity.do.types_do import RdSysTypes
        from module_business.entity.do.kb_types_rel_do import RdKbTypesRel
        
        types_query = (
            select(RdSysTypes)
            .join(RdKbTypesRel, RdSysTypes.type_id == RdKbTypesRel.type_id)
            .where(RdKbTypesRel.kb_id == kb_id)
        )
        return (await db.execute(types_query)).scalars().all()

    @classmethod
    async def _kb_to_dict(cls, kb_obj, types=None):
        """
        将数据库对象转换为字典（私有方法）
        """
        kb_dict = {
            'kbId': kb_obj.kb_id,
            'projectId': kb_obj.project_id,
            'kbName': kb_obj.kb_name,
            'description': kb_obj.description,
            'ownerId': kb_obj.owner_id,
            'vectorStoreId': kb_obj.vector_store_id,
            'isPublic': kb_obj.is_public,
            'createdAt': kb_obj.created_at,
            'updatedAt': kb_obj.updated_at,
            'isDeleted': kb_obj.is_deleted,
            'fileTypeStats': kb_obj.file_type_stats,
        }
        
        if types:
            kb_dict['typeIds'] = [t.type_id for t in types]
            kb_dict['typeNames'] = [t.display_name for t in types]
        else:
            kb_dict['typeIds'] = []
            kb_dict['typeNames'] = []
        
        return kb_dict

    @classmethod
    async def get_knowledge_bases_detail_by_id(cls, db: AsyncSession, kb_id: int):
        """
        根据数据库id获取数据库管理详细信息
        """
        return await cls._get_kb_by_id(db, kb_id)

    @classmethod
    async def get_knowledge_bases_detail_with_types_by_id(cls, db: AsyncSession, kb_id: int):
        """
        根据数据库id获取数据库管理详细信息（包含类型信息）
        """
        kb_obj = await cls._get_kb_by_id(db, kb_id)
        if not kb_obj:
            return None
        
        types = await cls._get_kb_types(db, kb_id)
        logger.info(f"Database {kb_id} has {len(types)} associated types")
        
        return await cls._kb_to_dict(kb_obj, types)

    @classmethod
    async def get_knowledge_bases_list(cls, db: AsyncSession, query_object: KnowledgeBasesPageQueryModel, is_page: bool = False):
        """
        根据查询参数获取数据库管理列表信息
        """
        query = (
            select(RdKnowledgeBases)
            .where(
                RdKnowledgeBases.kb_name.like(f'%{query_object.kb_name}%') if query_object.kb_name else True,
                RdKnowledgeBases.description == query_object.description if query_object.description else True,
                RdKnowledgeBases.owner_id == query_object.owner_id if query_object.owner_id else True,
                RdKnowledgeBases.is_deleted == query_object.is_deleted if query_object.is_deleted is not None else True,
                RdKnowledgeBases.project_id == query_object.project_id if hasattr(query_object, 'project_id') and query_object.project_id else True,
            )
            .order_by(RdKnowledgeBases.kb_id)
            .distinct()
        )
        return await PageUtil.paginate(db, query, query_object.page_num, query_object.page_size, is_page)

    @classmethod
    async def add_knowledge_bases_dao(cls, db: AsyncSession, knowledge_bases: KnowledgeBasesModel):
        """
        新增数据库管理数据库操作
        """
        # 排除不属于数据库模型的字段
        db_knowledge_bases = RdKnowledgeBases(**knowledge_bases.model_dump(exclude={'type_ids', 'type_names'}))
        db.add(db_knowledge_bases)
        await db.flush()
        return db_knowledge_bases

    @classmethod
    async def edit_knowledge_bases_dao(cls, db: AsyncSession, knowledge_bases: dict):
        """
        编辑数据库管理数据库操作
        """
        await db.execute(update(RdKnowledgeBases), [knowledge_bases])

    @classmethod
    async def delete_knowledge_bases_dao(cls, db: AsyncSession, knowledge_bases: KnowledgeBasesModel):
        """
        删除数据库管理数据库操作
        """
        await db.execute(delete(RdKnowledgeBases).where(RdKnowledgeBases.kb_id.in_([knowledge_bases.kb_id])))
    
    @classmethod
    async def get_knowledge_bases_by_project_id(cls, db: AsyncSession, project_id: int):
        """
        根据项目ID获取关联的数据库列表
        """
        query = select(RdKnowledgeBases).where(
            RdKnowledgeBases.project_id == project_id,
            RdKnowledgeBases.is_deleted == 0
        )
        return (await db.execute(query)).scalars().all()
    
    @classmethod
    async def delete_knowledge_bases_by_project_id(cls, db: AsyncSession, project_id: int):
        """
        根据项目ID逻辑删除关联的数据库
        """
        await db.execute(
            update(RdKnowledgeBases)
            .where(RdKnowledgeBases.project_id == project_id)
            .values(is_deleted=1)
        )

    @classmethod
    async def get_knowledge_bases_with_types(cls, db: AsyncSession, page_num: int = 1, page_size: int = 10, kb_name: str = None):
        """
        获取所有数据库及其类型信息
        """
        # 构建基础查询
        query = (
            select(RdKnowledgeBases)
            .where(RdKnowledgeBases.is_deleted == False)
        )
        
        # 添加名称搜索条件
        if kb_name:
            query = query.where(RdKnowledgeBases.kb_name.like(f'%{kb_name}%'))
        
        # 执行分页查询
        result = await PageUtil.paginate(db, query, page_num, page_size, True)
        
        # 为每个数据库获取类型信息
        kb_list = result.get('rows', [])
        kb_with_types = []
        
        for kb in kb_list:
            types = await cls._get_kb_types(db, kb.kb_id)
            kb_dict = await cls._kb_to_dict(kb, types)
            kb_with_types.append(kb_dict)
        
        # 更新结果
        result['rows'] = kb_with_types
        return result

