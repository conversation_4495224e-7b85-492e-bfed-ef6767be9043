from datetime import datetime
from sqlalchemy import and_, delete, desc, func, or_, select, update
from sqlalchemy.ext.asyncio import AsyncSession
from module_business.entity.do.project_members_do import RdProjectMembers
from module_business.entity.do.projects_do import RdProjects
from module_admin.entity.do.user_do import SysUser
from module_business.entity.vo.project_members_vo import (
    ProjectMembersModel,
    ProjectMembersPageQueryModel,
    ProjectMembersQueryModel,
)
from utils.page_util import PageUtil


class ProjectMembersDao:
    """
    项目成员管理模块数据库操作层
    """

    @classmethod
    async def get_project_members_detail_by_info(cls, db: AsyncSession, project_members: ProjectMembersModel):
        """
        根据项目成员参数获取项目成员信息

        :param db: orm对象
        :param project_members: 项目成员参数对象
        :return: 项目成员信息对象
        """
        project_members_info = (
            (
                await db.execute(
                    select(RdProjectMembers).where(
                        RdProjectMembers.project_id == project_members.project_id if project_members.project_id else True,
                        RdProjectMembers.user_id == project_members.user_id if project_members.user_id else True,
                        RdProjectMembers.is_deleted == 0,
                    )
                )
            )
            .scalars()
            .first()
        )

        return project_members_info

    @classmethod
    async def get_project_members_list(
        cls, db: AsyncSession, query_object: ProjectMembersPageQueryModel, is_page: bool = False
    ):
        """
        根据查询参数获取项目成员列表信息

        :param db: orm对象
        :param query_object: 查询参数对象
        :param is_page: 是否开启分页
        :return: 项目成员列表信息对象
        """
        query = (
            select(
                RdProjectMembers.project_id,
                RdProjectMembers.user_id,
                RdProjectMembers.role_type,
                RdProjectMembers.assigned_at,
                RdProjectMembers.assigned_by,
                RdProjectMembers.is_deleted,
                SysUser.user_name,
                SysUser.nick_name,
                RdProjects.project_name,
            )
            .select_from(RdProjectMembers)
            .join(SysUser, RdProjectMembers.user_id == SysUser.user_id)
            .join(RdProjects, RdProjectMembers.project_id == RdProjects.project_id)
            .where(
                RdProjectMembers.is_deleted == 0,
                RdProjects.is_deleted == 0,
                SysUser.del_flag == '0',
                RdProjectMembers.project_id == query_object.project_id if query_object.project_id else True,
                RdProjectMembers.user_id == query_object.user_id if query_object.user_id else True,
                RdProjectMembers.role_type == query_object.role_type if query_object.role_type else True,
            )
            .order_by(desc(RdProjectMembers.assigned_at))
            .distinct()
        )
        project_members_list = await PageUtil.paginate(db, query, query_object.page_num, query_object.page_size, is_page)

        return project_members_list

    @classmethod
    async def add_project_members_dao(cls, db: AsyncSession, project_members: ProjectMembersModel):
        """
        新增项目成员数据库操作

        :param db: orm对象
        :param project_members: 项目成员对象
        :return: 新增校验结果
        """
        db_project_members = RdProjectMembers(**project_members.model_dump())
        db.add(db_project_members)
        await db.flush()

        return db_project_members

    @classmethod
    async def edit_project_members_dao(cls, db: AsyncSession, project_members: ProjectMembersModel):
        """
        编辑项目成员数据库操作

        :param db: orm对象
        :param project_members: 项目成员对象
        :return: 编辑校验结果
        """
        await db.execute(
            update(RdProjectMembers)
            .where(
                RdProjectMembers.project_id == project_members.project_id,
                RdProjectMembers.user_id == project_members.user_id,
            )
            .values(**project_members.model_dump(exclude_unset=True))
        )

    @classmethod
    async def delete_project_members_dao(cls, db: AsyncSession, project_members: ProjectMembersModel):
        """
        删除项目成员数据库操作

        :param db: orm对象
        :param project_members: 项目成员对象
        :return: 删除校验结果
        """
        await db.execute(
            update(RdProjectMembers)
            .where(
                RdProjectMembers.project_id == project_members.project_id,
                RdProjectMembers.user_id == project_members.user_id,
            )
            .values(is_deleted=1)
        )

    @classmethod
    async def get_user_projects(cls, db: AsyncSession, user_id: int):
        """
        获取用户所属的项目列表

        :param db: orm对象
        :param user_id: 用户ID
        :return: 项目列表
        """
        query = (
            select(RdProjects.project_id, RdProjects.project_name, RdProjectMembers.role_type)
            .select_from(RdProjectMembers)
            .join(RdProjects, RdProjectMembers.project_id == RdProjects.project_id)
            .where(
                RdProjectMembers.user_id == user_id,
                RdProjectMembers.is_deleted == 0,
                RdProjects.is_deleted == 0,
            )
        )
        result = (await db.execute(query)).fetchall()
        return result

    @classmethod
    async def get_project_members_by_project_id(cls, db: AsyncSession, project_id: int):
        """
        根据项目ID获取项目成员列表

        :param db: orm对象
        :param project_id: 项目ID
        :return: 项目成员列表
        """
        query = (
            select(
                RdProjectMembers.user_id,
                RdProjectMembers.role_type,
                SysUser.user_name,
                SysUser.nick_name,
            )
            .select_from(RdProjectMembers)
            .join(SysUser, RdProjectMembers.user_id == SysUser.user_id)
            .where(
                RdProjectMembers.project_id == project_id,
                RdProjectMembers.is_deleted == 0,
                SysUser.del_flag == '0',
            )
        )
        result = (await db.execute(query)).fetchall()
        return result

    @classmethod
    async def check_user_project_permission(cls, db: AsyncSession, user_id: int, project_id: int, required_role: str = None):
        """
        检查用户对项目的权限

        :param db: orm对象
        :param user_id: 用户ID
        :param project_id: 项目ID
        :param required_role: 需要的角色类型，None表示只要是项目成员即可
        :return: 权限检查结果
        """
        query = (
            select(RdProjectMembers.role_type)
            .where(
                RdProjectMembers.user_id == user_id,
                RdProjectMembers.project_id == project_id,
                RdProjectMembers.is_deleted == 0,
            )
        )
        
        if required_role:
            query = query.where(RdProjectMembers.role_type == required_role)
            
        result = (await db.execute(query)).scalar()
        return result is not None

    @classmethod
    async def get_managed_projects(cls, db: AsyncSession, user_id: int):
        """
        获取用户管理的项目列表（项目管理员角色）

        :param db: orm对象
        :param user_id: 用户ID
        :return: 管理的项目ID列表
        """
        query = (
            select(RdProjectMembers.project_id)
            .where(
                RdProjectMembers.user_id == user_id,
                RdProjectMembers.role_type == 'project_manager',
                RdProjectMembers.is_deleted == 0,
            )
        )
        result = (await db.execute(query)).scalars().all()
        return result
