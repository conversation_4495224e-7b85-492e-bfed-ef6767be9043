from sqlalchemy import Integer, TIMESTAMP, Column, BigInteger
from config.database import Base


class RdKbTypesRel(Base):
    """
    数据库-类型关联关系表
    """

    __tablename__ = 'rd_kb_types'

    kb_id = Column(BigInteger, primary_key=True, nullable=False, comment='数据库id')
    type_id = Column(Integer, primary_key=True, nullable=False, comment='类型id')
    assigned_at = Column(TIMESTAMP, nullable=True, comment='创建时间')
    assigned_by = Column(BigInteger, nullable=False, comment='创建者')



