# 任务日志服务

本模块提供任务日志的订阅、处理和展示功能，通过Kafka接收来自外部服务的任务执行日志。**日志服务仅作为平台主服务的一个模块，随主服务自动启动和关闭，无需单独部署或测试。**

## 功能特性

### 核心功能
- **Kafka订阅**: 自动订阅任务日志主题
- **实时处理**: 实时接收和处理日志消息
- **日志缓存**: 在内存中缓存最近的日志
- **多维度查询**: 支持按任务ID、服务类型、日志级别过滤
- **统计分析**: 提供日志统计信息
- **REST API**: 提供完整的日志查询接口

### 日志格式
```json
{
    "task_id": 123,
    "service_type": "modeling",
    "timestamp": "2024-01-01T10:00:00",
    "level": "INFO",
    "message": "建模任务进度: 数据预处理 (10%)",
    "progress": 10,
    "step": "数据预处理"
}
```

## 架构设计

### 组件结构
```
module_log/
├── __init__.py
├── controller/
│   └── log_controller.py      # 日志控制器
├── service/
│   └── log_service.py         # 日志服务
└── README.md
```

### 数据流
1. **外部服务** → **Kafka** → **日志服务** → **内存缓存** → **REST API** → **前端**

## API接口

### 获取最近日志
```http
GET /log/recent?limit=100&task_id=123&service_type=modeling&level=INFO
```

### 获取任务日志
```http
GET /log/task/{task_id}?limit=100
```

### 获取服务日志
```http
GET /log/service/{service_type}?limit=100
```

### 获取统计信息
```http
GET /log/stats
```

### 清空日志缓存
```http
DELETE /log/clear
```

### 获取服务状态
```http
GET /log/status
```

### 健康检查
```http
GET /log/health
```

## 配置说明

### Kafka配置
在 `config/get_kafka.py` 中配置：
- `KAFKA_BOOTSTRAP_SERVERS`: Kafka服务器地址
- `KAFKA_TASK_LOGS_TOPIC`: 任务日志主题
- `CONSUMER_GROUP_ID`: 消费者组ID

### 环境变量
```bash
# Kafka配置
KAFKA_BOOTSTRAP_SERVERS=localhost:9092
KAFKA_TASK_LOGS_TOPIC=task_logs
KAFKA_CONSUMER_GROUP_ID=ai_platform_log_consumer
```

## 使用示例

### 1. 查询日志
```python
from module_log.service.log_service import get_log_service

# 获取日志服务实例
log_service = get_log_service()

# 获取最近100条日志
recent_logs = log_service.get_recent_logs(limit=100)

# 获取指定任务的日志
task_logs = log_service.get_task_logs(task_id=123, limit=50)

# 获取指定服务的日志
service_logs = log_service.get_service_logs(service_type="modeling", limit=50)
```

### 2. 添加日志处理器
```python
def custom_log_handler(log_data):
    """自定义日志处理器"""
    print(f"收到日志: {log_data}")

# 添加处理器
from module_log.service.log_service import add_log_handler
add_log_handler(custom_log_handler)
```

### 3. 获取统计信息
```python
stats = log_service.get_stats()
print(f"总日志数: {stats['total_logs']}")
print(f"服务统计: {stats['service_stats']}")
print(f"级别统计: {stats['level_stats']}")
```

## 服务管理

### 检查状态
```python
stats = log_service.get_stats()
is_running = stats['is_consumer_running']
```

## 性能优化

### 内存管理
- 默认缓存最近1000条日志
- 自动清理过期日志
- 支持配置缓存大小

### 查询优化
- 支持多维度过滤
- 按时间倒序排序
- 分页查询支持

### 并发处理
- 线程安全的日志处理
- 异步Kafka消费
- 非阻塞API响应

## 监控和调试

### 日志级别
- `INFO`: 一般信息
- `WARNING`: 警告信息
- `ERROR`: 错误信息
- `DEBUG`: 调试信息

### 状态监控
- 消费者运行状态
- 日志处理统计
- 内存使用情况

### 错误处理
- Kafka连接异常处理
- 日志解析错误处理
- 内存溢出保护

## 扩展功能

### 自定义日志处理器
```python
def my_log_handler(log_data):
    # 自定义处理逻辑
    pass

add_log_handler(my_log_handler)
```

### 日志持久化
可以扩展支持数据库存储：
```python
# 示例：保存到数据库
async def save_to_database(log_data):
    # 保存日志到数据库
    pass

add_log_handler(save_to_database)
```

### 实时推送
可以扩展支持WebSocket推送：
```python
# 示例：推送到前端
async def push_to_frontend(log_data):
    # 通过WebSocket推送到前端
    pass

add_log_handler(push_to_frontend)
```

## 故障排除

### 常见问题

1. **Kafka连接失败**
   - 检查Kafka服务是否运行
   - 验证连接配置是否正确
   - 查看网络连接状态

2. **日志不显示**
   - 检查消费者是否正常运行
   - 验证主题名称是否正确
   - 查看日志级别设置

3. **内存占用过高**
   - 调整缓存大小限制
   - 检查是否有内存泄漏
   - 优化日志处理逻辑

### 调试命令
```bash
# 检查Kafka连接
kafka-console-consumer --bootstrap-server localhost:9092 --topic task_logs

# 查看服务状态
curl http://localhost:9099/log/status

# 查看健康状态
curl http://localhost:9099/log/health
```

## 最佳实践

1. **合理设置缓存大小**: 根据内存情况调整缓存限制
2. **定期清理日志**: 避免内存占用过高
3. **监控服务状态**: 定期检查消费者运行状态
4. **错误日志处理**: 及时处理错误日志
5. **性能监控**: 监控日志处理性能

## 版本历史

- **v1.0.0**: 初始版本，支持基本的日志订阅和处理功能
- 支持Kafka消息订阅
- 支持日志缓存和查询
- 提供REST API接口 