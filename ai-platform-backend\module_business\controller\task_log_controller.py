import asyncio
import json
import queue
import threading
from datetime import datetime
from fastapi import APIRouter, WebSocket, WebSocketDisconnect, Depends
from utils.log_util import logger
from module_log.service.log_service import get_log_service, add_log_handler, remove_log_handler
from module_admin.service.login_service import LoginService

# 创建两个路由器：一个用于HTTP API（需要认证），一个用于WebSocket（不需要认证）
taskLogController = APIRouter(prefix="/business/task-logs", tags=["任务日志"], dependencies=[Depends(LoginService.get_current_user)])
taskLogWebSocketController = APIRouter(prefix="/business/task-logs", tags=["任务日志"])

class ConnectionManager:
    def __init__(self):
        self.active_connections: dict = {}  # task_id -> [websocket1, websocket2, ...]
        self.message_queue = queue.Queue()
        self.worker_thread = None
        self.is_running = False
    
    def start_worker(self):
        """启动消息处理工作线程"""
        if not self.is_running:
            self.is_running = True
            self.worker_thread = threading.Thread(target=self._message_worker, daemon=True)
            self.worker_thread.start()
            logger.info("WebSocket消息处理工作线程已启动")
    
    def stop_worker(self):
        """停止消息处理工作线程"""
        self.is_running = False
        if self.worker_thread:
            self.worker_thread.join(timeout=5)
            logger.info("WebSocket消息处理工作线程已停止")
    
    def _message_worker(self):
        """消息处理工作线程"""
        while self.is_running:
            try:
                # 从队列中获取消息，超时1秒
                message_data = self.message_queue.get(timeout=1)
                task_id = message_data['task_id']
                log_data = message_data['log_data']
                
                # 直接调用同步方法发送消息，避免事件循环冲突
                self._send_log_to_task_sync(task_id, log_data)
                    
            except queue.Empty:
                continue
            except Exception as e:
                logger.error(f"消息处理工作线程异常: {e}")
    
    def _send_log_to_task_sync(self, task_id: int, log_data: dict):
        """同步发送日志到任务"""
        if task_id in self.active_connections:
            disconnected_connections = []
            for connection in self.active_connections[task_id]:
                try:
                    # 使用线程安全的方式发送消息
                    import asyncio
                    import threading
                    
                    # 检查当前线程是否在主事件循环中
                    try:
                        loop = asyncio.get_running_loop()
                        # 如果已经在事件循环中，直接发送
                        if threading.current_thread() is threading.main_thread():
                            # 在主线程中，可以直接使用asyncio.create_task
                            asyncio.create_task(connection.send_text(json.dumps(log_data, ensure_ascii=False)))
                        else:
                            # 在工作线程中，使用run_coroutine_threadsafe
                            future = asyncio.run_coroutine_threadsafe(
                                connection.send_text(json.dumps(log_data, ensure_ascii=False)), 
                                loop
                            )
                            future.result(timeout=5)
                    except RuntimeError:
                        # 没有运行中的事件循环，创建一个新的
                        loop = asyncio.new_event_loop()
                        asyncio.set_event_loop(loop)
                        try:
                            loop.run_until_complete(connection.send_text(json.dumps(log_data, ensure_ascii=False)))
                        finally:
                            loop.close()
                        
                except Exception as e:
                    logger.error(f"发送日志到WebSocket失败: {e}")
                    disconnected_connections.append(connection)
            
            for connection in disconnected_connections:
                self.disconnect(connection, task_id)
    
    async def _send_log_to_task_async(self, task_id: int, log_data: dict):
        """异步发送日志到任务"""
        if task_id in self.active_connections:
            disconnected_connections = []
            for connection in self.active_connections[task_id]:
                try:
                    await connection.send_text(json.dumps(log_data, ensure_ascii=False))
                except Exception as e:
                    logger.error(f"发送日志到WebSocket失败: {e}")
                    disconnected_connections.append(connection)
            
            for connection in disconnected_connections:
                self.disconnect(connection, task_id)
    
    async def connect(self, websocket: WebSocket, task_id: int):
        try:
            await websocket.accept()
            if task_id not in self.active_connections:
                self.active_connections[task_id] = []
            self.active_connections[task_id].append(websocket)
            logger.info(f"WebSocket连接已建立，任务ID: {task_id}")
            
            # 确保工作线程已启动
            if not self.is_running:
                self.start_worker()
        except Exception as e:
            logger.error(f"WebSocket连接建立失败: {e}")
            raise
    
    def disconnect(self, websocket: WebSocket, task_id: int):
        try:
            if task_id in self.active_connections:
                if websocket in self.active_connections[task_id]:
                    self.active_connections[task_id].remove(websocket)
                if not self.active_connections[task_id]:
                    del self.active_connections[task_id]
            logger.info(f"WebSocket连接已断开，任务ID: {task_id}")
        except Exception as e:
            logger.error(f"WebSocket断开连接时出错: {e}")
    
    def send_log_to_task(self, task_id: int, log_data: dict):
        """同步方法：将消息放入队列"""
        try:
            if task_id in self.active_connections and self.active_connections[task_id]:
                self.message_queue.put({
                    'task_id': task_id,
                    'log_data': log_data
                })
                logger.debug(f"消息已加入队列，任务ID: {task_id}")
            else:
                logger.debug(f"任务 {task_id} 没有活跃的WebSocket连接")
        except Exception as e:
            logger.error(f"将消息放入队列失败: {e}")

manager = ConnectionManager()

# 任务特定的日志处理器
task_log_handlers = {}

def create_task_log_handler(task_id: int):
    """为特定任务创建日志处理器（已禁用Kafka功能）"""
    def task_log_handler(log_data: dict):
        if log_data.get('task_id') == task_id:
            # 使用同步方法发送消息
            manager.send_log_to_task(task_id, {
                "type": "log",
                "task_id": task_id,
                "message": log_data.get('message', ''),
                "level": log_data.get('level', 'INFO'),
                "timestamp": log_data.get('timestamp', datetime.now().isoformat()),
                "service_type": log_data.get('service_type', ''),
                "source": "manual"  # 改为manual，因为Kafka已禁用
            })
    
    
    return task_log_handler

@taskLogWebSocketController.websocket("/ws/{task_id}")
async def websocket_endpoint(websocket: WebSocket, task_id: int):
    """WebSocket连接，用于实时推送任务日志"""
    logger.info(f"WebSocket连接请求: task_id={task_id}")
    
    try:
        await manager.connect(websocket, task_id)
        
        # 为当前任务创建日志处理器
        task_handler = create_task_log_handler(task_id)
        task_log_handlers[task_id] = task_handler
        add_log_handler(task_handler)
        
        # 发送连接成功消息
        await websocket.send_text(json.dumps({
            "type": "connection",
            "message": "WebSocket连接成功",
            "task_id": task_id,
            "timestamp": datetime.now().isoformat()
        }, ensure_ascii=False))
        
        # 发送日志服务状态消息（已禁用Kafka）
        # log_service = get_log_service()
        # manager.send_log_to_task(task_id, {
        #     "type": "warning",
        #     "task_id": task_id,
        #     "message": "Kafka日志服务已禁用，无法接收实时日志",
        #     "level": "WARNING",
        #     "timestamp": datetime.now().isoformat()
        # })
        
        # 保持连接
        while True:
            try:
                data = await websocket.receive_text()
                try:
                    message = json.loads(data)
                    if message.get("type") == "ping":
                        await websocket.send_text(json.dumps({
                            "type": "pong",
                            "timestamp": datetime.now().isoformat()
                        }, ensure_ascii=False))
                except json.JSONDecodeError:
                    logger.warning(f"收到无效的JSON消息: {data}")
            except WebSocketDisconnect:
                logger.info(f"WebSocket客户端断开连接，任务ID: {task_id}")
                break
            except Exception as e:
                logger.error(f"WebSocket消息处理异常: {e}")
                break
                
    except WebSocketDisconnect:
        logger.info(f"WebSocket连接断开，任务ID: {task_id}")
    except Exception as e:
        logger.error(f"WebSocket处理异常: {e}")
    finally:
        # 清理连接和处理器
        try:
            manager.disconnect(websocket, task_id)
            # 移除日志处理器
            if task_id in task_log_handlers:
                remove_log_handler(task_log_handlers[task_id])
                del task_log_handlers[task_id]
        except Exception as e:
            logger.error(f"清理WebSocket资源时出错: {e}")

# 提供日志推送接口（供其他服务调用）
async def push_task_log(task_id: int, log_message: str, log_level: str = "INFO", **kwargs):
    """推送任务日志到前端"""
    # 提取特殊参数
    message_type = kwargs.pop('message_type', 'log')
    progress = kwargs.pop('progress', None)

    log_data = {
        "type": message_type,
        "task_id": task_id,
        "message": log_message,
        "level": log_level,
        "timestamp": datetime.now().isoformat(),
        **kwargs
    }

    # 如果是进度更新消息，添加进度字段
    if message_type == "progress_update" and progress is not None:
        log_data["progress"] = progress

    manager.send_log_to_task(task_id, log_data)
    logger.info(f"推送任务日志: task_id={task_id}, type={message_type}, message={log_message}")

# 导出函数供其他模块使用
__all__ = ['push_task_log'] 