import { defineConfig, loadEnv } from 'vite'
import path from 'path'
import createVitePlugins from './vite/plugins'

// https://vitejs.dev/config/
export default defineConfig(({ mode, command }) => {
  const env = loadEnv(mode, process.cwd())
  const { VITE_APP_ENV } = env
  return {
    // 部署生产环境和开发环境下的URL。
    // 默认情况下，vite 会假设你的应用是被部署在一个域名的根路径上
    base: VITE_APP_ENV === 'production' ? '/' : '/',
    plugins: createVitePlugins(env, command === 'build'),
    define: {
      'import.meta.env.VITE_APP_BASE_API': JSON.stringify('/dev-api'),
      'import.meta.env.VITE_APP_TITLE': JSON.stringify('CV-AI产品设计平台')
    },
    resolve: {
      // https://cn.vitejs.dev/config/#resolve-alias
      alias: {
        // 设置路径
        '~': path.resolve(__dirname, './'),
        // 设置别名
        '@': path.resolve(__dirname, './src')
      },
      // https://cn.vitejs.dev/config/#resolve-extensions
      extensions: ['.mjs', '.js', '.ts', '.jsx', '.tsx', '.json', '.vue']
    },
    // vite 相关配置
    server: {
      port: 3000,
      host: true,
      open: false,
      proxy: {
        // https://cn.vitejs.dev/config/#server-proxy
        '/dev-api': {
          target: 'http://127.0.0.1:9099',
          changeOrigin: true,
          rewrite: (p) => p.replace(/^\/dev-api/, ''),
          ws: true // 启用WebSocket代理
        },
        // Chat服务代理 - 需要放在/dev-api之前，避免被主后端代理拦截
        '/dev-api/chat': {
          target: 'http://127.0.0.1:8004',
          changeOrigin: true,
          rewrite: (p) => p.replace(/^\/dev-api\/chat/, '/chat'),
          ws: true
        },
        // Modeling服务代理
        '/modeling': {
          target: 'http://127.0.0.1:8001',
          changeOrigin: true,
          ws: true
        },
        // Simulation服务代理
        '/simulation': {
          target: 'http://127.0.0.1:8002',
          changeOrigin: true,
          ws: true
        },
        // Selection服务代理
        '/selection': {
          target: 'http://127.0.0.1:8003',
          changeOrigin: true,
          ws: true
        }
      }
    },
    //fix:error:stdin>:7356:1: warning: "@charset" must be the first rule in the file
    css: {
      postcss: {
        plugins: [
          {
            postcssPlugin: 'internal:charset-removal',
            AtRule: {
              charset: (atRule) => {
                if (atRule.name === 'charset') {
                  atRule.remove();
                }
              }
            }
          }
        ]
      }
    }
  }
})
