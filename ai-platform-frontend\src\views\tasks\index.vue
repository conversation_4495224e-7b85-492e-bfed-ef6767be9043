<template>
  <div class="app-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>任务管理</span>
          <el-button type="primary" @click="handleCreate">
            <el-icon><Plus /></el-icon>
            新建任务
          </el-button>
        </div>
      </template>

      <!-- 搜索表单 -->
      <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
        <el-form-item label="任务名称" prop="taskName">
          <el-input
            v-model="queryParams.taskName"
            placeholder="请输入任务名称"
            clearable
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="项目" prop="projectId">
          <el-select v-model="queryParams.projectId" placeholder="请选择项目" clearable>
            <el-option
              v-for="project in projectList"
              :key="project.projectId"
              :label="project.projectName"
              :value="project.projectId"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
            <el-option label="待执行" value="pending" />
            <el-option label="执行中" value="running" />
            <el-option label="已完成" value="completed" />
            <el-option label="失败" value="failed" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 任务列表 -->
      <el-table v-loading="loading" :data="taskList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="任务ID" align="center" prop="taskId" width="80" />
        <el-table-column label="任务名称" align="center" prop="taskName" :show-overflow-tooltip="true" />
        <el-table-column label="项目名称" align="center" prop="projectName" :show-overflow-tooltip="true" />
        <el-table-column label="状态" align="center" prop="status" width="100">
          <template #default="scope">
            <el-tag
              :type="getStatusType(scope.row.status)"
              disable-transitions
            >
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="进度" align="center" prop="progress" width="120">
          <template #default="scope">
            <el-progress :percentage="scope.row.progress || 0" :stroke-width="6" />
          </template>
        </el-table-column>
        <el-table-column label="创建时间" align="center" prop="createdAt" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.createdAt, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200">
          <template #default="scope">
            <el-button
              type="primary"
              icon="View"
              @click="handleView(scope.row)"
              v-hasPermi="['business:tasks:query']"
            >
              查看
            </el-button>
            <el-button
              type="success"
              icon="Edit"
              @click="handleUpdate(scope.row)"
              v-hasPermi="['business:tasks:edit']"
              v-if="canEditTaskLocal(scope.row)"
            >
              编辑
            </el-button>
            <el-button
              type="danger"
              icon="Delete"
              @click="handleDelete(scope.row)"
              v-hasPermi="['business:tasks:remove']"
              v-if="canDeleteTaskLocal(scope.row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </el-card>
  </div>
</template>

<script setup name="Tasks">
import { getTasksList } from '@/api/tasks'
import { getProjectsList } from '@/api/projects'
import { parseTime } from '@/utils/ruoyi'
import { canEditTask, canDeleteTask } from '@/utils/permission'

const { proxy } = getCurrentInstance()

const taskList = ref([])
const projectList = ref([])
const loading = ref(true)
const showSearch = ref(true)
const ids = ref([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)

const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  taskName: null,
  projectId: null,
  status: null
})

/** 查询任务列表 */
function getList() {
  loading.value = true
  getTasksList(queryParams.value).then(response => {
    taskList.value = response.rows
    total.value = response.total
    loading.value = false
  })
}

/** 查询项目列表 */
function getProjectList() {
  getProjectsList().then(response => {
    projectList.value = response.rows || response.data || []
  })
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef")
  handleQuery()
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.taskId)
  single.value = selection.length != 1
  multiple.value = !selection.length
}

/** 新增按钮操作 */
function handleCreate() {
  proxy.$router.push('/projects')
}

/** 查看按钮操作 */
function handleView(row) {
  proxy.$router.push(`/tasks/detail/${row.taskId}`)
}

/** 修改按钮操作 */
function handleUpdate(row) {
  // 根据任务所属项目跳转到项目详情页进行编辑
  proxy.$router.push(`/projects/detail/${row.projectId}`)
}

/** 删除按钮操作 */
function handleDelete(row) {
  const taskIds = row.taskId || ids.value
  proxy.$modal.confirm('是否确认删除任务编号为"' + taskIds + '"的数据项？').then(function() {
    // 这里需要调用删除API
    proxy.$modal.msgSuccess("删除成功")
    getList()
  }).catch(() => {})
}

/** 获取状态类型 */
function getStatusType(status) {
  const statusMap = {
    'pending': 'info',
    'running': 'warning',
    'completed': 'success',
    'failed': 'danger'
  }
  return statusMap[status] || 'info'
}

/** 获取状态文本 */
function getStatusText(status) {
  const statusMap = {
    'pending': '待执行',
    'running': '执行中',
    'completed': '已完成',
    'failed': '失败'
  }
  return statusMap[status] || '未知'
}

/** 判断是否可以编辑任务 */
function canEditTaskLocal(row) {
  return canEditTask(row)
}

/** 判断是否可以删除任务 */
function canDeleteTaskLocal(row) {
  return canDeleteTask(row)
}

onMounted(() => {
  getList()
  getProjectList()
})
</script>

<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
