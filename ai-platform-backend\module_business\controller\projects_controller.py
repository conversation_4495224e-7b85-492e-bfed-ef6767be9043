from fastapi import APIRouter, Depends, Form, Request, Query
from pydantic_validation_decorator import Val<PERSON>te<PERSON>ields
from sqlalchemy.ext.asyncio import AsyncSession
from config.enums import BusinessType
from config.get_db import get_db
from module_admin.annotation.log_annotation import Log
from module_admin.aspect.interface_auth import CheckUserInterfaceAuth
from module_admin.entity.vo.user_vo import CurrentUserModel
from module_admin.service.login_service import LoginService
from module_business.service.projects_service import ProjectsService
from module_business.aspect.project_auth import ProjectDataScope, CheckProjectPermission
from module_business.service.knowledge_bases_service import KnowledgeBasesService
from module_business.entity.vo.projects_vo import ProjectsModel, ProjectsPageQueryModel
from module_business.entity.vo.knowledge_bases_vo import KnowledgeBasesModel
from utils.common_util import bytes2file_response
from utils.log_util import logger
from utils.page_util import PageResponseModel
from utils.response_util import ResponseUtil
from datetime import datetime
from typing import List
from fastapi.responses import JSONResponse
from fastapi.encoders import jsonable_encoder


projectsController = APIRouter(prefix='/business/projects', dependencies=[Depends(LoginService.get_current_user)])


@projectsController.get(
    '/list', response_model=PageResponseModel
    # , dependencies=[Depends(CheckUserInterfaceAuth('business:projects:list'))]
)
async def get_business_projects_list(
    projects_page_query: ProjectsPageQueryModel = Depends(),
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
    data_scope_sql: str = Depends(ProjectDataScope('RdProjects')),
):
    """
    获取项目列表
    :param projects_page_query: 查询参数
    :param query_db: 数据库会话
    :param current_user: 当前用户
    :param data_scope_sql: 数据权限过滤条件
    :return: 项目列表
    """
    # 设置默认排序为按更新时间倒序
    if not projects_page_query.order_by_column:
        projects_page_query.order_by_column = 'updated_at'
        projects_page_query.is_asc = False

    # 获取分页数据（带数据权限过滤）
    projects_page_query_result = await ProjectsService.get_projects_list_services(
        query_db, projects_page_query, data_scope_sql, is_page=True
    )
    logger.info('获取项目列表成功')

    return ResponseUtil.success(model_content=projects_page_query_result)


@projectsController.post('')  # dependencies=[Depends(CheckUserInterfaceAuth('business:projects:add'))]
@ValidateFields(validate_model='add_projects')
@Log(title='项目管理', business_type=BusinessType.INSERT)
async def add_business_projects(
    request: Request,
    add_projects: ProjectsModel,
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    try:
        # 设置创建者信息
        add_projects.created_at = datetime.now()
        add_projects.updated_at = datetime.now()
        add_projects.owner_id = current_user.user.user_id
        
        # 添加项目
        add_projects_result = await ProjectsService.add_projects_services(query_db, add_projects)
        
        # 同步创建数据库
        kb_model = KnowledgeBasesModel(
            kbName=add_projects.project_name+"数据库",  # 使用项目名称作为数据库名称
            projectId=add_projects_result.result.project_id,  # 使用新创建的项目ID
            description=add_projects.description,  # 使用项目描述作为数据库描述
            ownerId=current_user.user.user_id,
            createdAt=datetime.now(),
            updatedAt=datetime.now()
        )
        await KnowledgeBasesService.add_knowledge_bases_services(query_db, kb_model)
        
        # 提交事务
        await query_db.commit()
        
        logger.info(add_projects_result.message)
        
        # 返回创建的项目数据
        from utils.common_util import CamelCaseUtil
        project_data = CamelCaseUtil.transform_result(add_projects_result.result)
        
        return ResponseUtil.success(msg=add_projects_result.message, data=project_data)
    except Exception as e:
        # 回滚事务
        await query_db.rollback()
        raise e


@projectsController.put('')  # dependencies=[Depends(CheckUserInterfaceAuth('business:projects:edit'))]
@ValidateFields(validate_model='edit_projects')
@Log(title='项目信息管理', business_type=BusinessType.UPDATE)
async def edit_business_projects(
    edit_projects: ProjectsModel,
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    # 检查项目权限
    from module_business.aspect.project_auth import _check_project_edit_permission
    await _check_project_edit_permission(edit_projects.project_id, current_user, query_db)

    edit_projects.updated_at = datetime.now()
    edit_projects_result = await ProjectsService.edit_projects_services(query_db, edit_projects)
    logger.info(edit_projects_result.message)

    return ResponseUtil.success(msg=edit_projects_result.message)

@projectsController.get('/list_with_types', summary='获取带类型标签的项目列表')
async def get_projects_list_with_types(
    request: Request,
    projects_page_query: ProjectsPageQueryModel = Depends(),
    query_db: AsyncSession = Depends(get_db),
):
    """
    获取带类型标签的项目列表，rows 每项带 typeIds
    """
    # 获取分页数据
    projects_list_result = await ProjectsService.get_projects_list_services(query_db, projects_page_query, is_page=True)
    # 关键：转为 dict
    if hasattr(projects_list_result, 'model_dump'):
        projects_list_result = projects_list_result.model_dump()
    rows = []
    for project in projects_list_result['rows']:
        # ORM对象转dict
        if hasattr(project, 'model_dump'):
            project_dict = project.model_dump()
        else:
            from utils.common_util import CamelCaseUtil
            project_dict = CamelCaseUtil.transform_result(project)
        # 聚合 typeIds
        from module_business.dao.project_types_rel_dao import ProjectTypesRelDao
        rels = await ProjectTypesRelDao.get_project_types_rel_list_by_project_id(query_db, project_dict['projectId'])
        project_dict['typeIds'] = [rel.type_id for rel in rels] if rels else []
        rows.append(project_dict)
    # 保留分页结构
    result = dict(projects_list_result)
    result['rows'] = rows
    return JSONResponse(content=jsonable_encoder({"code": 200, "msg": "操作成功", **result}))

@projectsController.get(
    '/{project_id}', response_model=ProjectsModel  # dependencies=[Depends(CheckUserInterfaceAuth('business:projects:query'))]
)
async def query_detail_business_projects(request: Request, project_id: int, query_db: AsyncSession = Depends(get_db)):
    projects_detail_result = await ProjectsService.projects_detail_services(query_db, project_id)
    logger.info(f'获取project_id为{project_id}的信息成功')

    return ResponseUtil.success(data=projects_detail_result)
