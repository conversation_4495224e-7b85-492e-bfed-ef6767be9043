"""
日志调度器
提供日志文件的定时清理和压缩功能
"""

import asyncio
from datetime import datetime, time
from typing import Optional

from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.triggers.cron import CronTrigger

from utils.log_util import logger
from utils.log_manager import get_log_manager


class LogScheduler:
    """日志调度器"""
    
    def __init__(self):
        """初始化日志调度器"""
        self.scheduler: Optional[AsyncIOScheduler] = None
        self.log_manager = get_log_manager()
        self.is_running = False
    
    async def start(self):
        """启动调度器"""
        if self.is_running:
            logger.warning("日志调度器已经在运行")
            return
        
        try:
            self.scheduler = AsyncIOScheduler()
            
            # 添加定时任务
            self._add_scheduled_jobs()
            
            # 启动调度器
            self.scheduler.start()
            self.is_running = True
            
            logger.info("日志调度器启动成功")
            
        except Exception as e:
            logger.error(f"启动日志调度器失败: {e}")
            raise
    
    async def stop(self):
        """停止调度器"""
        if not self.is_running:
            return
        
        try:
            if self.scheduler:
                self.scheduler.shutdown(wait=False)
                self.scheduler = None
            
            self.is_running = False
            logger.info("日志调度器已停止")
            
        except Exception as e:
            logger.error(f"停止日志调度器失败: {e}")
    
    def _add_scheduled_jobs(self):
        """添加定时任务"""
        try:
            # 每天凌晨2点清理30天前的日志
            self.scheduler.add_job(
                func=self._clean_old_logs_job,
                trigger=CronTrigger(hour=2, minute=0),
                id='clean_old_logs',
                name='清理旧日志',
                replace_existing=True
            )
            
            # 每天凌晨3点压缩7天前的日志
            self.scheduler.add_job(
                func=self._compress_logs_job,
                trigger=CronTrigger(hour=3, minute=0),
                id='compress_logs',
                name='压缩日志',
                replace_existing=True
            )
            
            logger.info("日志调度任务添加成功")
            
        except Exception as e:
            logger.error(f"添加日志调度任务失败: {e}")
            raise
    
    async def _clean_old_logs_job(self):
        """清理旧日志的定时任务"""
        try:
            logger.info("开始执行定时清理旧日志任务")
            
            # 清理30天前的日志
            deleted_count = self.log_manager.clean_old_logs(days_to_keep=30)
            
            logger.info(f"定时清理旧日志完成，删除了 {deleted_count} 个文件")
            
        except Exception as e:
            logger.error(f"定时清理旧日志失败: {e}")
    
    async def _compress_logs_job(self):
        """压缩日志的定时任务"""
        try:
            logger.info("开始执行定时压缩日志任务")
            
            # 压缩7天前的日志
            compressed_count = self.log_manager.compress_logs(days_old=7)
            
            logger.info(f"定时压缩日志完成，压缩了 {compressed_count} 个文件")
            
        except Exception as e:
            logger.error(f"定时压缩日志失败: {e}")
    
    async def manual_clean_logs(self, days_to_keep: int = 30) -> int:
        """
        手动清理日志
        
        :param days_to_keep: 保留天数
        :return: 删除的文件数量
        """
        try:
            logger.info(f"开始手动清理 {days_to_keep} 天前的日志")
            deleted_count = self.log_manager.clean_old_logs(days_to_keep=days_to_keep)
            logger.info(f"手动清理日志完成，删除了 {deleted_count} 个文件")
            return deleted_count
            
        except Exception as e:
            logger.error(f"手动清理日志失败: {e}")
            raise
    
    async def manual_compress_logs(self, days_old: int = 7) -> int:
        """
        手动压缩日志
        
        :param days_old: 压缩多少天前的日志
        :return: 压缩的文件数量
        """
        try:
            logger.info(f"开始手动压缩 {days_old} 天前的日志")
            compressed_count = self.log_manager.compress_logs(days_old=days_old)
            logger.info(f"手动压缩日志完成，压缩了 {compressed_count} 个文件")
            return compressed_count
            
        except Exception as e:
            logger.error(f"手动压缩日志失败: {e}")
            raise
    
    def get_scheduler_status(self) -> dict:
        """
        获取调度器状态
        
        :return: 状态信息
        """
        if not self.scheduler:
            return {
                'running': False,
                'jobs': []
            }
        
        jobs = []
        for job in self.scheduler.get_jobs():
            jobs.append({
                'id': job.id,
                'name': job.name,
                'next_run_time': job.next_run_time.strftime('%Y-%m-%d %H:%M:%S') if job.next_run_time else None,
                'trigger': str(job.trigger)
            })
        
        return {
            'running': self.is_running,
            'jobs': jobs
        }


# 全局日志调度器实例
_log_scheduler = None


def get_log_scheduler() -> LogScheduler:
    """
    获取日志调度器实例（单例模式）
    
    :return: LogScheduler实例
    """
    global _log_scheduler
    if _log_scheduler is None:
        _log_scheduler = LogScheduler()
    return _log_scheduler


async def start_log_scheduler():
    """启动日志调度器"""
    scheduler = get_log_scheduler()
    await scheduler.start()


async def stop_log_scheduler():
    """停止日志调度器"""
    scheduler = get_log_scheduler()
    await scheduler.stop()
