from fastapi import APIRouter, Depends, Form, Request
from pydantic_validation_decorator import Validate<PERSON>ields
from sqlalchemy.ext.asyncio import AsyncSession
from config.enums import BusinessType
from config.get_db import get_db
from module_admin.annotation.log_annotation import Log
from module_admin.aspect.interface_auth import CheckUserInterfaceAuth
from module_admin.entity.vo.user_vo import CurrentUserModel
from module_admin.service.login_service import LoginService
from module_business.service.tools_service import ToolsService
from module_business.entity.vo.tools_vo import DeleteToolsModel, ToolsModel, ToolsPageQueryModel
from utils.common_util import bytes2file_response
from utils.log_util import logger
from utils.page_util import PageResponseModel
from utils.response_util import ResponseUtil
from datetime import datetime


toolsController = APIRouter(prefix='/business/tools', dependencies=[Depends(LoginService.get_current_user)])


@toolsController.get(
    '/list', response_model=PageResponseModel  # dependencies=[Depends(CheckUserInterfaceAuth('business:tools:list'))]
)

async def get_tools_list(
    request: Request,
    tools_page_query: ToolsPageQueryModel = Depends(),
    query_db: AsyncSession = Depends(get_db),
):
    # 获取分页数据
    tools_page_query_result = await ToolsService.get_tools_list_services(query_db, tools_page_query, is_page=True)
    logger.info('获取成功')

    return ResponseUtil.success(model_content=tools_page_query_result)


@toolsController.post('')  # dependencies=[Depends(CheckUserInterfaceAuth('business:tools:add'))]
@ValidateFields(validate_model='add_tools')
@Log(title='工具管理', business_type=BusinessType.INSERT)
async def add_tools(
    request: Request,
    add_tools: ToolsModel,
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    add_tools.created_at = datetime.now()
    add_tools.updated_at = datetime.now()
    add_tools_result = await ToolsService.add_tools_services(query_db, add_tools)
    logger.info(add_tools_result.message)

    return ResponseUtil.success(msg=add_tools_result.message)


@toolsController.put('')  # dependencies=[Depends(CheckUserInterfaceAuth('business:tools:edit'))]
@ValidateFields(validate_model='edit_tools')
@Log(title='工具管理', business_type=BusinessType.UPDATE)
async def edit_tools(
    request: Request,
    edit_tools: ToolsModel,
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    # edit_tools.update_by = current_user.user.user_name
    edit_tools.updated_at = datetime.now()
    edit_tools_result = await ToolsService.edit_tools_services(query_db, edit_tools)
    logger.info(edit_tools_result.message)

    return ResponseUtil.success(msg=edit_tools_result.message)


@toolsController.get(
    '/{tool_id}', response_model=ToolsModel  # dependencies=[Depends(CheckUserInterfaceAuth('business:tools:query'))]
)
async def query_detail_tools(request: Request, tool_id: int, query_db: AsyncSession = Depends(get_db)):
    tools_detail_result = await ToolsService.tools_detail_services(query_db, tool_id)
    logger.info(f'获取tool_id为{tool_id}的信息成功')

    return ResponseUtil.success(data=tools_detail_result)


@toolsController.post('/export')  # dependencies=[Depends(CheckUserInterfaceAuth('business:tools:export'))]
@Log(title='工具管理', business_type=BusinessType.EXPORT)
async def export_tools_list(
    request: Request,
    tools_page_query: ToolsPageQueryModel = Form(),
    query_db: AsyncSession = Depends(get_db),
):
    # 获取全量数据
    tools_query_result = await ToolsService.get_tools_list_services(query_db, tools_page_query, is_page=False)
    tools_export_result = await ToolsService.export_tools_list_services(tools_query_result)
    logger.info('导出成功')

    return ResponseUtil.streaming(data=bytes2file_response(tools_export_result))
