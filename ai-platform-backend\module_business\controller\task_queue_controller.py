from fastapi import APIRouter, Depends, Request
from sqlalchemy.ext.asyncio import AsyncSession
from config.get_db import get_db
from module_admin.entity.vo.user_vo import CurrentUserModel
from module_admin.service.login_service import LoginService
from module_business.entity.vo.tasks_vo import TasksModel
from module_business.service.tasks_service import TasksService
from module_scheduler.service.task_queue_service import TaskQueueService
from utils.log_util import logger
from utils.response_util import ResponseUtil
from datetime import datetime
import json


taskQueueController = APIRouter(prefix='/business/task-queue', dependencies=[Depends(LoginService.get_current_user)])


@taskQueueController.post('/submit/{task_id}')  # dependencies=[Depends(CheckUserInterfaceAuth('business:task-queue:submit'))]
async def submit_task_to_queue(
    request: Request,
    task_id: int,
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    提交任务到队列或直接执行
    
    Args:
        task_id: 任务ID
        
    Returns:
        包含队列位置信息或执行状态的响应
    """
    try:
        # 获取任务信息
        task_info = await TasksService.tasks_detail_services(query_db, task_id)
        if not task_info.task_id:
            return ResponseUtil.failure(msg="任务不存在")
        
        # 检查任务状态
        if task_info.status not in ["pending"]:
            return ResponseUtil.failure(msg=f"任务状态为 {task_info.status}，无法提交")

        # 验证任务参数
        # 1. 首先检查参数是否为空
        if not task_info.parameters:
            return ResponseUtil.failure(msg="任务参数为空，请先配置任务参数后再提交。您可以编辑任务来添加必要的参数。")

        # 2. 检查参数是否为空字典
        if not isinstance(task_info.parameters, dict) or len(task_info.parameters) == 0:
            return ResponseUtil.failure(msg="任务参数为空，请先配置任务参数后再提交。您可以编辑任务来添加必要的参数。")

        # 3. 进行详细的参数验证
        try:
            await TasksService._validate_task_parameters(task_info)
        except Exception as e:
            # 提供更友好的错误提示
            error_msg = str(e)
            if "不能为空" in error_msg:
                return ResponseUtil.failure(msg=f"任务参数不完整：{error_msg}。请编辑任务并填写所有必需的参数后再提交。")
            else:
                return ResponseUtil.failure(msg=f"任务参数验证失败：{error_msg}。请检查参数格式是否正确。")
        
        # 检查工具是否需要队列
        tool_requires_queue = True  # 默认需要队列（除了选型任务）
        if task_info.tool_id is not None:
            try:
                from module_business.dao.tools_dao import ToolsDao
                tool_info = await ToolsDao.get_tools_detail_by_id(query_db, task_info.tool_id)
                if tool_info:
                    tool_requires_queue = bool(tool_info.queue_required)
                    logger.info(f"任务 {task_id} 的工具 {task_info.tool_id} ({tool_info.tool_name}) queue_required: {tool_requires_queue}")
                else:
                    logger.warning(f"任务 {task_id} 的工具 {task_info.tool_id} 不存在")
            except Exception as e:
                logger.error(f"获取工具信息失败: {e}")
        else:
            logger.warning(f"任务 {task_id} 没有关联工具，使用默认队列设置")
        
        if not tool_requires_queue:
            # 工具不需要队列，直接执行
            logger.info(f"任务 {task_id} 的工具不需要队列，直接执行")
            
            # 更新任务状态为运行中
            update_data = {
                "task_id": task_id,
                "status": "running",
                "started_at": datetime.now(),
                "updated_at": datetime.now()
            }
            await TasksService.edit_tasks_services(query_db, TasksModel(**update_data))
            
            # 异步调用外部服务（不阻塞当前请求）
            import asyncio
            # 创建新的数据库会话，避免会话冲突
            from config.get_db import AsyncSessionLocal
            asyncio.create_task(_execute_task_directly(task_id, task_info))
            
            return ResponseUtil.success(data={
                "task_id": task_id,
                "status": "running",
                "message": "任务已开始执行"
            }, msg="任务已开始执行")
        
        else:
            # 工具需要队列，提交到队列
            logger.info(f"任务 {task_id} 的工具需要队列，提交到队列")
            
            # 获取Redis客户端
            redis_client = request.app.state.redis
            queue_service = TaskQueueService(redis_client)
            
            # 准备任务数据
            task_data = {
                "task_id": task_info.task_id,
                "task_name": task_info.task_name,
                "project_id": task_info.project_id,
                "type_id": task_info.type_id,
                "parameters": json.dumps(task_info.parameters) if task_info.parameters else "{}",
                "submitted_at": datetime.now().isoformat(),
                "submitted_by": current_user.user.user_id
            }
            
            # 添加可选字段，过滤掉None值
            logger.info(f"任务 {task_id} 的 tool_id: {task_info.tool_id}")
            if task_info.tool_id is not None:
                task_data["tool_id"] = task_info.tool_id
                logger.info(f"任务 {task_id} 已添加 tool_id: {task_info.tool_id}")
            else:
                logger.warning(f"任务 {task_id} 缺少 tool_id")
            if task_info.assigned_to is not None:
                task_data["assigned_to"] = task_info.assigned_to
            
            # 先更新任务状态为队列中，再提交到队列（确保数据库和队列同步）
            update_data = {
                "task_id": task_id,
                "status": "queued",
                "updated_at": datetime.now()
            }
            await TasksService.edit_tasks_services(query_db, TasksModel(**update_data))

            try:
                # 提交任务到队列
                queue_result = await queue_service.submit_task(task_id, task_data)
            except Exception as queue_error:
                # 如果队列提交失败，回滚任务状态
                logger.error(f"提交任务 {task_id} 到队列失败: {queue_error}")
                rollback_data = {
                    "task_id": task_id,
                    "status": "pending",
                    "updated_at": datetime.now()
                }
                await TasksService.edit_tasks_services(query_db, TasksModel(**rollback_data))
                raise queue_error
            
            logger.info(f"用户 {current_user.user.user_id} 提交任务 {task_id} 到队列，位置: {queue_result['queue_position']}")
            
            return ResponseUtil.success(data=queue_result, msg="任务已提交到队列")
        
    except Exception as e:
        logger.error(f"提交任务失败: {e}")
        return ResponseUtil.failure(msg=f"提交任务失败: {str(e)}")


async def _execute_task_directly(task_id: int, task_info):
    """
    直接执行任务（异步）
    
    Args:
        task_id: 任务ID
        task_info: 任务信息
    """
    try:
        from module_scheduler.service.task_scheduler_service import TaskSchedulerService
        from module_scheduler.service.scheduler_manager import scheduler_manager
        from config.get_db import AsyncSessionLocal
        from config.get_redis import RedisUtil
        
        # 获取Redis连接
        redis_client = None
        if scheduler_manager.is_initialized and scheduler_manager.redis_client:
            redis_client = scheduler_manager.redis_client
        else:
            # 如果调度器管理器未初始化，创建新的Redis连接
            redis_client = await RedisUtil.create_redis_pool()
        
        # 创建新的数据库会话，避免会话冲突
        async with AsyncSessionLocal() as db_session:
            scheduler_service = TaskSchedulerService(redis_client, db_session)
            
            # 准备任务数据
            task_data = {
                "task_id": task_info.task_id,
                "task_name": task_info.task_name,
                "project_id": task_info.project_id,
                "type_id": task_info.type_id,
                "parameters": task_info.parameters,
                "tool_id": task_info.tool_id,
                "assigned_to": task_info.assigned_to
            }
            
            # 直接处理任务
            await scheduler_service._process_task(task_data)
        
        # 如果创建了新的Redis连接，关闭它
        if not scheduler_manager.is_initialized or not scheduler_manager.redis_client:
            await redis_client.close()
        
    except Exception as e:
        logger.error(f"直接执行任务 {task_id} 失败: {e}")
        # 更新任务状态为失败
        try:
            from module_business.service.tasks_service import TasksService
            from module_business.entity.vo.tasks_vo import TasksModel
            
            # 创建新的数据库会话来更新任务状态
            async with AsyncSessionLocal() as db_session:
                update_data = {
                    "task_id": task_id,
                    "status": "failed",
                    "updated_at": datetime.now()
                }
                await TasksService.edit_tasks_services(db_session, TasksModel(**update_data))
        except Exception as update_error:
            logger.error(f"更新任务状态失败: {update_error}")


@taskQueueController.get('/position/{task_id}')  # dependencies=[Depends(CheckUserInterfaceAuth('business:task-queue:query'))]
async def get_task_queue_new_position(
    request: Request,
    task_id: int,
    query_db: AsyncSession = Depends(get_db),
):
    """
    获取任务在队列中的位置
    
    Args:
        task_id: 任务ID
        
    Returns:
        队列位置信息
    """
    try:
        # 获取Redis客户端
        redis_client = request.app.state.redis
        queue_service = TaskQueueService(redis_client)
        
        # 获取队列位置
        position = await queue_service.get_queue_position(task_id)
        logger.info(f"查询任务 {task_id} 的队列位置: {position}")

        # if position is None:
        #     # 任务不在队列中，检查是否在处理队列中
        #     redis_client = request.app.state.redis
        #     queue_service = TaskQueueService(redis_client)

        #     # 检查是否在处理队列中
        #     processing_queue_length = await redis_client.llen("task_processing_queue_new")
        #     is_in_processing_queue = False
        #     if processing_queue_length > 0:
        #         processing_task_ids = await redis_client.lrange("task_processing_queue_new", 0, -1)
        #         is_in_processing_queue = str(task_id) in processing_task_ids

        #     # 检查任务状态
        #     task_info = await TasksService.tasks_detail_services(query_db, task_id)
        #     if not task_info.task_id:
        #         return ResponseUtil.failure(msg="任务不存在")

        #     logger.info(f"任务 {task_id} 不在主队列中，当前状态: {task_info.status}, 在处理队列中: {is_in_processing_queue}")

        #     # 根据任务状态返回不同的消息
        #     if task_info.status == "running":
        #         # 任务正在运行
        #         return ResponseUtil.success(data={
        #             "task_id": task_id,
        #             "status": "running",
        #             "message": "任务正在执行中"
        #         })
        #     elif task_info.status == "completed":
        #         return ResponseUtil.success(data={
        #             "task_id": task_id,
        #             "status": "completed",
        #             "message": "任务已完成"
        #         })
        #     elif task_info.status == "failed":
        #         return ResponseUtil.success(data={
        #             "task_id": task_id,
        #             "status": "failed",
        #             "message": "任务执行失败"
        #         })
        #     elif task_info.status == "queued":
        #         # 任务状态是queued但不在主队列中
        #         if is_in_processing_queue:
        #             # 在处理队列中，说明正在被调度器处理
        #             return ResponseUtil.success(data={
        #                 "task_id": task_id,
        #                 "status": "starting",
        #                 "message": "任务正在启动中"
        #             })
        #         else:
        #             # 不在任何队列中，状态不一致，尝试自动修复
        #             logger.warning(f"任务 {task_id} 状态为queued但不在任何队列中")

        #             # 尝试自动修复：重新提交到队列
        #             try:
        #                 queue_service = TaskQueueService(redis_client)

        #                 # 构造任务数据
        #                 import json as json_lib

        #                 # 处理参数字段
        #                 parameters = {}
        #                 if task_info.parameters:
        #                     if isinstance(task_info.parameters, dict):
        #                         parameters = task_info.parameters
        #                     elif isinstance(task_info.parameters, str):
        #                         try:
        #                             parameters = json_lib.loads(task_info.parameters)
        #                         except:
        #                             parameters = {}

        #                 task_data = {
        #                     "task_id": str(task_id),
        #                     "project_id": str(task_info.project_id) if task_info.project_id else "",
        #                     "tool_id": str(task_info.tool_id) if task_info.tool_id else "",
        #                     "task_name": task_info.task_name or "",
        #                     "type_id": str(task_info.type_id) if task_info.type_id else "",
        #                     "submitted_by": str(task_info.assigned_to) if task_info.assigned_to else "",
        #                     "assigned_to": str(task_info.assigned_to) if task_info.assigned_to else "",
        #                     "submitted_at": datetime.now().isoformat(),
        #                     "parameters": json_lib.dumps(parameters) if parameters else "{}"
        #                 }

        #                 # 重新提交到队列
        #                 await queue_service.submit_task(task_id, task_data)
        #                 logger.info(f"✅ 任务 {task_id} 已自动重新提交到队列")

        #                 # 返回队列位置
        #                 new_position = await queue_service.get_queue_position(task_id)
        #                 return ResponseUtil.success(data={
        #                     "task_id": task_id,
        #                     "position": new_position,
        #                     "status": "queued",
        #                     "message": f"排队中，位置: {new_position}"
        #                 })

        #             except Exception as fix_error:
        #                 logger.error(f"自动修复任务 {task_id} 失败: {fix_error}")
        #                 return ResponseUtil.success(data={
        #                     "task_id": task_id,
        #                     "status": "pending_restart",
        #                     "message": "任务状态异常，请重新提交"
        #                 })
        #     elif task_info.status == "pending":
        #         # 任务状态是pending但不在队列中，可能是还未提交到队列
        #         return ResponseUtil.success(data={
        #             "task_id": task_id,
        #             "status": "pending",
        #             "message": "任务等待提交"
        #         })
        #     else:
        #         # 其他未知状态
        #         logger.warning(f"任务 {task_id} 状态为 {task_info.status} 但不在队列中")
        #         return ResponseUtil.success(data={
        #             "task_id": task_id,
        #             "status": task_info.status,
        #             "message": f"任务状态: {task_info.status}"
        #         })
        
        return ResponseUtil.success(data={
            "task_id": task_id,
            "queue_position": position,
            "status": "queued"
        })
        
    except Exception as e:
        logger.error(f"获取任务队列位置失败: {e}")
        return ResponseUtil.failure(msg=f"获取队列位置失败: {str(e)}")





@taskQueueController.get('/length')  # dependencies=[Depends(CheckUserInterfaceAuth('business:task-queue:query'))]
async def get_queue_length(
    request: Request,
):
    """
    获取队列长度
    
    Returns:
        队列长度信息
    """
    try:
        # 获取Redis客户端
        redis_client = request.app.state.redis
        queue_service = TaskQueueService(redis_client)
        
        # 获取队列长度
        length = await queue_service.get_queue_length()
        
        return ResponseUtil.success(data={
            "queue_length": length
        })
        
    except Exception as e:
        logger.error(f"获取队列长度失败: {e}")
        return ResponseUtil.failure(msg=f"获取队列长度失败: {str(e)}")


@taskQueueController.get('/status')  # dependencies=[Depends(CheckUserInterfaceAuth('business:task-queue:query'))]
async def get_queue_status(
    request: Request,
):
    """
    获取队列状态信息
    
    Returns:
        队列状态信息
    """
    try:
        # 获取Redis客户端
        redis_client = request.app.state.redis
        queue_service = TaskQueueService(redis_client)
        
        # 获取队列状态
        status = await queue_service.get_queue_status()
        
        return ResponseUtil.success(data=status)
        
    except Exception as e:
        logger.error(f"获取队列状态失败: {e}")
        return ResponseUtil.failure(msg=f"获取队列状态失败: {str(e)}")


@taskQueueController.delete('/clear')  # dependencies=[Depends(CheckUserInterfaceAuth('business:task-queue:clear'))]
async def clear_queue(
    request: Request,
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    清空队列（管理员功能）
    
    Returns:
        清空结果
    """
    try:
        # 获取Redis客户端
        redis_client = request.app.state.redis
        queue_service = TaskQueueService(redis_client)
        
        # 清空队列
        success = await queue_service.clear_queue()
        
        if success:
            logger.info(f"用户 {current_user.user.user_id} 清空了任务队列")
            return ResponseUtil.success(msg="队列已清空")
        else:
            return ResponseUtil.failure(msg="清空队列失败")
        
    except Exception as e:
        logger.error(f"清空队列失败: {e}")
        return ResponseUtil.failure(msg=f"清空队列失败: {str(e)}")


@taskQueueController.get('/scheduler-status')  # dependencies=[Depends(CheckUserInterfaceAuth('business:task-queue:query'))]
async def get_scheduler_status(
    request: Request,
):
    """
    获取调度器状态（调试用）
    
    Returns:
        调度器状态信息
    """
    try:
        from module_scheduler.service.scheduler_manager import scheduler_manager
        status = await scheduler_manager.get_scheduler_status()
        return ResponseUtil.success(data=status)
        
    except Exception as e:
        logger.error(f"获取调度器状态失败: {e}")
        return ResponseUtil.failure(msg=f"获取调度器状态失败: {str(e)}")


@taskQueueController.post('/restart-scheduler')
async def restart_scheduler(
    request: Request,
):
    """
    重启调度器（紧急修复用）
    """
    try:
        from module_scheduler.service.scheduler_manager import scheduler_manager

        logger.info("🔄 开始重启调度器...")

        # 停止当前调度器
        await scheduler_manager.stop_scheduler()
        logger.info("🛑 调度器已停止")

        # 等待一秒
        import asyncio
        await asyncio.sleep(1)

        # 重新启动调度器
        success = await scheduler_manager.start_scheduler()

        if success:
            logger.info("🚀 调度器重启成功")
            return ResponseUtil.success(data={"message": "调度器重启成功"})
        else:
            logger.error("❌ 调度器重启失败")
            return ResponseUtil.failure(msg="调度器重启失败")

    except Exception as e:
        logger.error(f"重启调度器失败: {e}")
        return ResponseUtil.failure(msg=f"重启调度器失败: {str(e)}")


@taskQueueController.get('/task-status/{task_id}')
async def get_task_detailed_status(
    request: Request,
    task_id: int,
    query_db: AsyncSession = Depends(get_db),
):
    """
    获取任务的详细状态信息（包括Redis处理标记）
    """
    try:
        # 获取任务基本信息
        task_info = await TasksService.tasks_detail_services(query_db, task_id)
        if not task_info.task_id:
            return ResponseUtil.failure(msg="任务不存在")

        # 检查Redis中的状态
        redis_client = request.app.state.redis
        queue_service = TaskQueueService(redis_client)

        # 检查是否在队列中
        queue_position = await queue_service.get_queue_position(task_id)

        # 检查是否在处理队列中
        processing_queue_length = await redis_client.llen("task_processing_queue_new")
        is_in_processing_queue = False
        if processing_queue_length > 0:
            processing_task_ids = await redis_client.lrange("task_processing_queue_new", 0, -1)
            is_in_processing_queue = str(task_id) in processing_task_ids

        # 检查任务信息是否在Redis中
        task_info_key = f"task_info:{task_id}"
        has_task_info = await redis_client.exists(task_info_key)

        status_info = {
            "task_id": task_id,
            "db_status": task_info.status,
            "db_progress": task_info.progress,
            "queue_position": queue_position,
            "is_in_processing_queue": is_in_processing_queue,
            "has_redis_info": bool(has_task_info),
            "diagnosis": _diagnose_task_status(task_info.status, queue_position, is_in_processing_queue)
        }

        return ResponseUtil.success(data=status_info)

    except Exception as e:
        logger.error(f"获取任务详细状态失败: {e}")
        return ResponseUtil.failure(msg=f"获取任务详细状态失败: {str(e)}")


def _diagnose_task_status(db_status: str, queue_position: int, is_in_processing_queue: bool) -> str:
    """
    诊断任务状态
    """
    if db_status == "pending":
        return "任务已创建，等待提交到队列"
    elif db_status == "queued":
        if queue_position:
            return f"任务在主队列中，位置: {queue_position}"
        elif is_in_processing_queue:
            return "任务在处理队列中，正在被调度器处理"
        else:
            return "任务状态异常：已标记为queued但不在任何队列中"
    elif db_status == "running":
        return "任务正在执行中"
    elif db_status == "completed":
        return "任务已完成"
    elif db_status == "failed":
        return "任务执行失败"
    else:
        return f"未知状态: {db_status}"


@taskQueueController.post('/fix-task-status/{task_id}')
async def fix_task_status(
    request: Request,
    task_id: int,
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    修复卡住的任务状态
    """
    try:
        # 获取任务信息
        task_info = await TasksService.tasks_detail_services(query_db, task_id)
        if not task_info.task_id:
            return ResponseUtil.failure(msg="任务不存在")

        redis_client = request.app.state.redis

        # 检查任务状态
        if task_info.status == "running":
            # 任务正在运行，检查是否在处理队列中
            processing_queue_length = await redis_client.llen("task_processing_queue_new")
            is_in_processing_queue = False
            if processing_queue_length > 0:
                processing_task_ids = await redis_client.lrange("task_processing_queue_new", 0, -1)
                is_in_processing_queue = str(task_id) in processing_task_ids

            if not is_in_processing_queue:
                # 如果不在处理队列中，添加到处理队列
                await redis_client.lpush("task_processing_queue_new", task_id)
                logger.info(f"为运行中的任务 {task_id} 重新添加到处理队列")

            return ResponseUtil.success(data={
                "task_id": task_id,
                "action": "ensure_in_processing_queue",
                "message": "已确保运行中的任务在处理队列中"
            })

        elif task_info.status == "queued":
            # 检查任务是否在队列中
            queue_service = TaskQueueService(redis_client)
            position = await queue_service.get_queue_position(task_id)

            if position is None:
                # 任务不在队列中，可能需要重新提交
                logger.warning(f"任务 {task_id} 状态为queued但不在队列中")
                return ResponseUtil.success(data={
                    "task_id": task_id,
                    "action": "requeue_needed",
                    "message": "任务需要重新提交到队列"
                })

        return ResponseUtil.success(data={
            "task_id": task_id,
            "action": "no_fix_needed",
            "message": f"任务状态正常: {task_info.status}"
        })

    except Exception as e:
        logger.error(f"修复任务状态失败: {e}")
        return ResponseUtil.failure(msg=f"修复任务状态失败: {str(e)}")