"""
日志服务
功能：订阅Kafka中的任务日志，处理并展示给前端
注意：Kafka功能已被禁用
"""

import asyncio
import json
import threading
from datetime import datetime
from typing import Dict, List, Optional, Callable
# from config.get_kafka import consume_task_logs, send_task_log
from utils.log_util import logger


class LogService:
    """日志服务类"""
    
    def __init__(self):
        self.log_handlers: List[Callable] = []
        self.consumer_thread: Optional[threading.Thread] = None
        self.is_running = False
        self.recent_logs: List[Dict] = []
        self.max_recent_logs = 1000  # 保留最近1000条日志
        
    def add_log_handler(self, handler: Callable):
        """
        添加日志处理器
        
        :param handler: 处理日志的回调函数
        """
        self.log_handlers.append(handler)
        logger.info(f"添加日志处理器: {handler.__name__}")
    
    def remove_log_handler(self, handler: Callable):
        """
        移除日志处理器
        
        :param handler: 处理日志的回调函数
        """
        if handler in self.log_handlers:
            self.log_handlers.remove(handler)
            logger.info(f"移除日志处理器: {handler.__name__}")
    
    def process_log(self, log_data: Dict):
        """
        处理接收到的日志数据
        
        :param log_data: 日志数据
        """
        try:
            # 添加时间戳（如果没有的话）
            if 'timestamp' not in log_data:
                log_data['timestamp'] = datetime.now().isoformat()
            
            # 添加到最近日志列表
            self.recent_logs.append(log_data)
            
            # 保持日志数量限制
            if len(self.recent_logs) > self.max_recent_logs:
                self.recent_logs = self.recent_logs[-self.max_recent_logs:]
            
            # 调用所有处理器
            for handler in self.log_handlers:
                try:
                    handler(log_data)
                except Exception as e:
                    logger.error(f"日志处理器 {handler.__name__} 执行失败: {e}")
            
            # 打印到控制台（用于调试）
            self._print_log(log_data)
            
        except Exception as e:
            logger.error(f"处理日志数据失败: {e}")
    
    def _print_log(self, log_data: Dict):
        """
        打印日志到控制台
        
        :param log_data: 日志数据
        """
        try:
            task_id = log_data.get('task_id', 'N/A')
            service_type = log_data.get('service_type', 'unknown')
            level = log_data.get('level', 'INFO')
            message = log_data.get('message', '')
            timestamp = log_data.get('timestamp', '')
            
            # 格式化输出
            log_line = f"[{timestamp}] [{level}] [{service_type.upper()}] Task-{task_id}: {message}"
            
            # 根据日志级别选择输出方式
            if level.upper() == 'ERROR':
                logger.error(log_line)
            elif level.upper() == 'WARNING':
                logger.warning(log_line)
            elif level.upper() == 'DEBUG':
                logger.debug(log_line)
            else:
                logger.info(log_line)
                
        except Exception as e:
            logger.error(f"打印日志失败: {e}")
    
    def start_consumer(self):
        """启动Kafka消费者（已禁用）"""
        logger.warning("Kafka功能已被禁用，跳过启动消费者")
        return
        # if self.is_running:
        #     logger.warning("⚠️ 日志消费者已在运行")
        #     return
        # 
        # try:
        #     self.is_running = True
        #     self.consumer_thread = threading.Thread(
        #         target=self._consumer_worker,
        #         daemon=True,
        #         name="LogConsumer"
        #     )
        #     self.consumer_thread.start()
        #     logger.info("✅ Kafka日志消费者已启动")
        #     logger.info(f"📊 消费者线程状态: {self.consumer_thread.is_alive()}")
        # except Exception as e:
        #     logger.error(f"❌ 启动Kafka日志消费者失败: {e}")
        #     self.is_running = False
        #     raise
    
    def stop_consumer(self):
        """停止Kafka消费者（已禁用）"""
        logger.warning("Kafka功能已被禁用")
        return
        # if not self.is_running:
        #     logger.warning("日志消费者未运行")
        #     return
        # 
        # self.is_running = False
        # if self.consumer_thread and self.consumer_thread.is_alive():
        #     self.consumer_thread.join(timeout=5)
        # logger.info("日志消费者已停止")
    
    def _consumer_worker(self):
        """消费者工作线程（已禁用）"""
        logger.warning("Kafka功能已被禁用")
        return
        # try:
        #     logger.info("🔄 Kafka消费者工作线程开始运行")
        #     consume_task_logs(self.process_log)
        # except Exception as e:
        #     logger.error(f"❌ Kafka日志消费者工作线程异常: {e}")
        #     import traceback
        #     logger.error(f"❌ 异常堆栈: {traceback.format_exc()}")
        # finally:
        #     logger.info("🛑 Kafka消费者工作线程已停止")
        #     self.is_running = False
    
    def get_recent_logs(self, limit: int = 100, task_id: Optional[int] = None, 
                       service_type: Optional[str] = None, level: Optional[str] = None) -> List[Dict]:
        """
        获取最近的日志
        
        :param limit: 返回的日志数量限制
        :param task_id: 按任务ID过滤
        :param service_type: 按服务类型过滤
        :param level: 按日志级别过滤
        :return: 日志列表
        """
        logs = self.recent_logs.copy()
        
        # 应用过滤器
        if task_id is not None:
            logs = [log for log in logs if log.get('task_id') == task_id]
        
        if service_type is not None:
            logs = [log for log in logs if log.get('service_type') == service_type]
        
        if level is not None:
            logs = [log for log in logs if log.get('level', '').upper() == level.upper()]
        
        # 按时间倒序排序并限制数量
        logs.sort(key=lambda x: x.get('timestamp', ''), reverse=True)
        return logs[:limit]
    
    def get_task_logs(self, task_id: int, limit: int = 100) -> List[Dict]:
        """
        获取指定任务的日志
        
        :param task_id: 任务ID
        :param limit: 返回的日志数量限制
        :return: 日志列表
        """
        return self.get_recent_logs(limit=limit, task_id=task_id)
    
    def get_service_logs(self, service_type: str, limit: int = 100) -> List[Dict]:
        """
        获取指定服务的日志
        
        :param service_type: 服务类型
        :param limit: 返回的日志数量限制
        :return: 日志列表
        """
        return self.get_recent_logs(limit=limit, service_type=service_type)
    
    def clear_logs(self):
        """清空日志缓存"""
        self.recent_logs.clear()
        logger.info("日志缓存已清空")
    
    def get_stats(self) -> Dict:
        """
        获取日志统计信息
        
        :return: 统计信息
        """
        total_logs = len(self.recent_logs)
        
        # 按服务类型统计
        service_stats = {}
        for log in self.recent_logs:
            service_type = log.get('service_type', 'unknown')
            service_stats[service_type] = service_stats.get(service_type, 0) + 1
        
        # 按级别统计
        level_stats = {}
        for log in self.recent_logs:
            level = log.get('level', 'INFO')
            level_stats[level] = level_stats.get(level, 0) + 1
        
        # 按任务ID统计
        task_stats = {}
        for log in self.recent_logs:
            task_id = log.get('task_id')
            if task_id:
                task_stats[task_id] = task_stats.get(task_id, 0) + 1
        
        return {
            'total_logs': total_logs,
            'service_stats': service_stats,
            'level_stats': level_stats,
            'task_stats': task_stats,
            'is_consumer_running': self.is_running
        }


# 全局日志服务实例
log_service = LogService()


def get_log_service() -> LogService:
    """
    获取日志服务实例
    
    :return: LogService实例
    """
    return log_service


def start_log_service():
    """启动日志服务（已禁用）"""
    logger.warning("Kafka功能已被禁用，跳过启动日志服务")
    return
    # try:
    #     log_service.start_consumer()
    #     logger.info("日志服务已启动")
    # except Exception as e:
    #     logger.error(f"启动日志服务失败: {e}")


def stop_log_service():
    """停止日志服务（已禁用）"""
    logger.warning("Kafka功能已被禁用，跳过停止日志服务")
    return
    # try:
    #     log_service.stop_consumer()
    #     logger.info("日志服务已停止")
    # except Exception as e:
    #     logger.error(f"停止日志服务失败: {e}")


def add_log_handler(handler: Callable):
    """
    添加日志处理器
    
    :param handler: 处理日志的回调函数
    """
    log_service.add_log_handler(handler)


def remove_log_handler(handler: Callable):
    """
    移除日志处理器
    
    :param handler: 处理日志的回调函数
    """
    log_service.remove_log_handler(handler) 