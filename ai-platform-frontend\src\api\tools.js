import request from '@/utils/request'

// 查询工具列表
export function getToolsList(query) {
  return request({
    url: '/business/tools/list',
    method: 'get',
    params: query
  })
}

// 查询工具详细
export function getTool(toolId) {
  return request({
    url: '/business/tools/' + toolId,
    method: 'get'
  })
}

// 新增工具
export function addTool(data) {
  return request({
    url: '/business/tools',
    method: 'post',
    data: data
  })
}

// 修改工具
export function updateTool(data) {
  return request({
    url: '/business/tools',
    method: 'put',
    data: data
  })
}

// 删除工具
export function delTool(toolIds) {
  return request({
    url: '/business/tools/' + toolIds,
    method: 'delete'
  })
}

// 导出工具
export function exportTool(query) {
  return request({
    url: '/business/tools/export',
    method: 'post',
    params: query
  })
} 