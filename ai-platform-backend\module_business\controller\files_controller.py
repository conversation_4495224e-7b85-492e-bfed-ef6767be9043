from fastapi import APIRouter, Depends, Form, Request, UploadFile, File
from typing import List
from fastapi.responses import StreamingResponse
from pydantic_validation_decorator import Val<PERSON>teFields
from sqlalchemy.ext.asyncio import AsyncSession
from config.enums import BusinessType
from config.get_db import get_db
from module_admin.annotation.log_annotation import Log
from module_admin.aspect.interface_auth import CheckUserInterfaceAuth
from module_admin.entity.vo.user_vo import CurrentUserModel
from module_admin.service.login_service import LoginService
from module_business.service.files_service import FilesService
from module_business.entity.vo.files_vo import FilesModel, FilesPageQueryModel, FilesDeleteModel
from utils.common_util import bytes2file_response
from utils.log_util import logger
from utils.page_util import PageResponseModel
from utils.response_util import ResponseUtil
from utils.minio_util import MinioUtil
from datetime import datetime
import io
import os
import urllib.parse


filesController = APIRouter(prefix='/business/files')


@filesController.get(
    '/list', response_model=PageResponseModel  # dependencies=[Depends(CheckUserInterfaceAuth('business:files:list'))]
)
async def get_business_files_list(
    request: Request,
    files_page_query: FilesPageQueryModel = Depends(),
    query_db: AsyncSession = Depends(get_db),
):
    # 获取分页数据
    files_page_query_result = await FilesService.get_files_list_services(query_db, files_page_query, is_page=True)
    logger.info('获取成功')

    return ResponseUtil.success(model_content=files_page_query_result)


@filesController.post('')  # dependencies=[Depends(CheckUserInterfaceAuth('business:files:add'))]
@ValidateFields(validate_model='add_files')
@Log(title='数据库文件存储', business_type=BusinessType.INSERT)
async def add_business_files(
    request: Request,
    add_files: FilesModel,
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):  
    from datetime import datetime
    if not add_files.created_at:
        add_files.created_at = datetime.now()
    add_files_result = await FilesService.add_files_services(query_db, add_files)
    logger.info(add_files_result.message)

    return ResponseUtil.success(msg=add_files_result.message)


@filesController.put('')  # dependencies=[Depends(CheckUserInterfaceAuth('business:files:edit'))]
@Log(title='数据库文件存储', business_type=BusinessType.UPDATE)
async def edit_business_files(
    request: Request,
    edit_files: FilesModel,
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    编辑/更新文件信息（支持逻辑删除）
    
    :param edit_files: 文件信息对象，如果is_deleted=1则执行逻辑删除
    :return: 操作结果
    """
    # 如果是逻辑删除操作，使用专门的删除模型进行验证
    if edit_files.is_deleted == 1:
        delete_model = FilesDeleteModel(
            fileId=edit_files.file_id,
            isDeleted=edit_files.is_deleted
        )
        delete_model.validate_fields()
    else:
        # 普通编辑操作，使用完整的验证
        edit_files.validate_fields()
    
    edit_files_result = await FilesService.edit_files_services(query_db, edit_files)
    logger.info(edit_files_result.message)

    return ResponseUtil.success(msg=edit_files_result.message)




@filesController.post('/upload/{kb_id}/{file_type}')
@Log(title='数据库文件上传', business_type=BusinessType.INSERT)
async def upload_file(
    request: Request,
    kb_id: int,
    file_type: str,
    file: UploadFile = File(...),
    project_id: int = None,
    source_task_id: int = None,
    sub_folder: str = None,
    query_db: AsyncSession = Depends(get_db),
):
    """
    上传文件到MinIO并保存到数据库
    
    :param kb_id: 数据库ID
    :param file_type: 文件类型 (model/report/dataset/log/document)
    :param file: 上传的文件
    :param project_id: 项目ID（可选）
    :param source_task_id: 来源任务ID（可选）
    :param sub_folder: 子文件夹名称（可选）
    :return: 上传结果
    """
    try:
        # 验证文件类型
        valid_types = ['model', 'report', 'dataset', 'log', 'document']
        if file_type not in valid_types:
            return ResponseUtil.error(msg=f"Invalid file type. Must be one of {valid_types}")

        # 直接使用FastAPI提供的文件名，简化处理逻辑，提高对API调用的兼容性
        original_filename = file.filename

        # 读取文件内容并计算MD5
        import hashlib
        file_content = await file.read()
        file_md5 = hashlib.md5(file_content).hexdigest()

        # 重置文件指针
        import io
        file_obj = io.BytesIO(file_content)

        # 上传文件到MinIO
        minio_util = MinioUtil.get_instance()
        file_path = minio_util.upload_file(
            file=file_obj,
            kb_id=kb_id,
            file_type=file_type,
            file_name=original_filename,
            task_id=source_task_id,  # 传递任务ID，如果为None则使用旧路径格式
            sub_folder=sub_folder    # 传递子文件夹名称
        )
        
        # 获取文件大小
        import math
        file_size = math.ceil((os.fstat(file.file.fileno()).st_size) / 1024)
        
        # 获取文件扩展名
        file_format = os.path.splitext(original_filename)[1][1:].lower()
        
        # 保存文件信息到数据库
        from datetime import datetime
        file_model = FilesModel(
            kbId=kb_id,
            originalName=original_filename,
            storagePath=file_path,
            fileType=file_type,
            fileSize=file_size,
            fileFormat=file_format,
            projectId=project_id,
            sourceTaskId=source_task_id,
            createdAt=datetime.now()
        )
        
        # 记录文件上传信息
        logger.info(f"文件上传信息: kb_id={kb_id}, file_type={file_type}, project_id={project_id}, source_task_id={source_task_id}")
        logger.info(f"文件模型数据: {file_model.model_dump()}")
        
        # 验证必填字段
        file_model.validate_fields()
        add_result = await FilesService.add_files_services(query_db, file_model)
        return ResponseUtil.success(msg="文件上传成功", data={
            "file_path": file_path,
            "file_md5": file_md5
        })
        
    except Exception as e:
        logger.error(f"File upload failed: {str(e)}")
        return ResponseUtil.error(msg=f"文件上传失败: {str(e)}")
    finally:
        file.file.close()

@filesController.post('/upload-minio/{kb_id}/{file_type}')
async def upload_file_to_minio_only(
    request: Request,
    kb_id: int,
    file_type: str,
    file: UploadFile = File(...),
    task_id: int = None,
    sub_folder: str = None,
):
    """
    仅上传文件到MinIO，不保存到数据库
    专门供外部工具使用，避免重复保存
    
    :param kb_id: 数据库ID
    :param file_type: 文件类型 (model/report/dataset/log/document)
    :param file: 上传的文件
    :param task_id: 任务ID（可选）
    :param sub_folder: 子文件夹名称（可选）
    :return: 上传结果
    """
    try:
        # 验证文件类型
        valid_types = ['model', 'report', 'dataset', 'log', 'document', 'archived']
        if file_type not in valid_types:
            return ResponseUtil.error(msg=f"Invalid file type. Must be one of {valid_types}")
        
        # 直接使用FastAPI提供的文件名
        original_filename = file.filename
        
        # 上传文件到MinIO
        minio_util = MinioUtil.get_instance()
        file_path = minio_util.upload_file(
            file=file.file,
            kb_id=kb_id,
            file_type=file_type,
            file_name=original_filename,
            task_id=task_id,      # 传递任务ID，如果为None则使用旧路径格式
            sub_folder=sub_folder # 传递子文件夹名称
        )
        
        logger.info(f"文件已上传到MinIO: {file_path}")
        return ResponseUtil.success(msg="文件上传到MinIO成功", data={"file_path": file_path})

    except Exception as e:
        logger.error(f"File upload to MinIO failed: {str(e)}")
        return ResponseUtil.error(msg=f"文件上传到MinIO失败: {str(e)}")
    finally:
        file.file.close()

@filesController.post('/batch-upload/{kb_id}')
@Log(title='批量文件上传', business_type=BusinessType.INSERT)
async def batch_upload_files(
    request: Request,
    kb_id: int,
    files: List[UploadFile] = File(...),
    project_id: int = None,
    source_task_id: int = None,
    sub_folder: str = None,
    query_db: AsyncSession = Depends(get_db),
):
    """
    批量上传文件到MinIO并保存到数据库

    :param kb_id: 数据库ID
    :param files: 上传的文件列表
    :param project_id: 项目ID（可选）
    :param source_task_id: 来源任务ID（可选）
    :param sub_folder: 子文件夹名称（可选）
    :return: 批量上传结果
    """
    try:
        if not files:
            return ResponseUtil.error(msg="没有选择文件")

        if len(files) > 10:  # 限制批量上传数量
            return ResponseUtil.error(msg="批量上传文件数量不能超过10个")

        upload_results = []
        failed_files = []

        for file in files:
            try:
                # 根据文件扩展名自动判断文件类型
                file_extension = file.filename.lower().split('.')[-1] if '.' in file.filename else ''
                file_type = determine_file_type(file_extension)

                logger.info(f"开始上传文件: {file.filename}, 类型: {file_type}")

                # 读取文件内容并计算MD5
                import hashlib
                file_content = await file.read()
                file_md5 = hashlib.md5(file_content).hexdigest()

                # 重置文件指针
                import io
                file_obj = io.BytesIO(file_content)

                # 上传文件到MinIO
                minio_util = MinioUtil.get_instance()
                file_path = minio_util.upload_file(
                    file=file_obj,
                    kb_id=kb_id,
                    file_type=file_type,
                    file_name=file.filename,
                    task_id=source_task_id,
                    sub_folder=sub_folder
                )

                # 创建文件模型
                file_model = FilesModel(
                    kb_id=kb_id,
                    file_type=file_type,
                    original_name=file.filename,
                    storage_path=file_path,
                    file_size=len(file_content),
                    file_md5=file_md5,
                    project_id=project_id,
                    source_task_id=source_task_id
                )

                # 保存到数据库
                file_model.validate_fields()
                await FilesService.add_files_services(query_db, file_model)

                upload_results.append({
                    "filename": file.filename,
                    "file_path": file_path,
                    "file_md5": file_md5,
                    "file_type": file_type,
                    "status": "success"
                })

                logger.info(f"文件上传成功: {file.filename} -> {file_path}")

            except Exception as e:
                logger.error(f"文件 {file.filename} 上传失败: {str(e)}")
                failed_files.append({
                    "filename": file.filename,
                    "error": str(e),
                    "status": "failed"
                })

        # 提交数据库事务
        await query_db.commit()

        result_data = {
            "total_files": len(files),
            "success_count": len(upload_results),
            "failed_count": len(failed_files),
            "success_files": upload_results,
            "failed_files": failed_files
        }

        if failed_files:
            return ResponseUtil.success(
                msg=f"批量上传完成，成功{len(upload_results)}个，失败{len(failed_files)}个",
                data=result_data
            )
        else:
            return ResponseUtil.success(msg="批量上传全部成功", data=result_data)

    except Exception as e:
        await query_db.rollback()
        logger.error(f"Batch file upload failed: {str(e)}")
        return ResponseUtil.error(msg=f"批量文件上传失败: {str(e)}")

def determine_file_type(file_extension: str) -> str:
    """
    根据文件扩展名确定文件类型

    :param file_extension: 文件扩展名
    :return: 文件类型
    """
    extension_map = {
        # 3D模型文件
        'step': 'model', 'stp': 'model', 'iges': 'model', 'igs': 'model',
        'stl': 'model', 'obj': 'model', '3mf': 'model', 'ply': 'model',

        # 数据文件
        'csv': 'dataset', 'xlsx': 'dataset', 'xls': 'dataset', 'json': 'dataset',
        'xml': 'dataset', 'txt': 'dataset',

        # 报告文件
        'pdf': 'report', 'doc': 'report', 'docx': 'report', 'ppt': 'report',
        'pptx': 'report', 'html': 'report', 'htm': 'report',

        # 日志文件
        'log': 'log', 'out': 'log',

        # 图片文件
        'jpg': 'document', 'jpeg': 'document', 'png': 'document', 'gif': 'document',
        'bmp': 'document',

        # 压缩文件 - 整包存档
        'zip': 'archived', 'rar': 'archived', 'gz': 'archived', 'bz2': 'archived',
        'tar': 'archived', '7z': 'archived', 'xz': 'archived'
    }

    return extension_map.get(file_extension.lower(), 'document')

@filesController.post('/batch-upload-minio/{kb_id}')
async def batch_upload_files_to_minio_only(
    request: Request,
    kb_id: int,
    files: List[UploadFile] = File(...),
    task_id: int = None,
    sub_folder: str = None,
):
    """
    批量上传文件到MinIO（不保存到数据库）

    :param kb_id: 数据库ID
    :param files: 上传的文件列表
    :param task_id: 任务ID（可选）
    :param sub_folder: 子文件夹名称（可选）
    :return: 批量上传结果
    """
    try:
        if not files:
            return ResponseUtil.error(msg="没有选择文件")

        if len(files) > 10:  # 限制批量上传数量
            return ResponseUtil.error(msg="批量上传文件数量不能超过10个")

        upload_results = []
        failed_files = []

        for file in files:
            try:
                # 根据文件扩展名自动判断文件类型
                file_extension = file.filename.lower().split('.')[-1] if '.' in file.filename else ''
                file_type = determine_file_type(file_extension)

                logger.info(f"开始上传文件到MinIO: {file.filename}, 类型: {file_type}")

                # 上传文件到MinIO
                minio_util = MinioUtil.get_instance()
                file_path = minio_util.upload_file(
                    file=file.file,
                    kb_id=kb_id,
                    file_type=file_type,
                    file_name=file.filename,
                    task_id=task_id,
                    sub_folder=sub_folder
                )

                upload_results.append({
                    "filename": file.filename,
                    "file_path": file_path,
                    "file_type": file_type,
                    "status": "success"
                })

                logger.info(f"文件上传到MinIO成功: {file.filename} -> {file_path}")

            except Exception as e:
                logger.error(f"文件 {file.filename} 上传到MinIO失败: {str(e)}")
                failed_files.append({
                    "filename": file.filename,
                    "error": str(e),
                    "status": "failed"
                })
            finally:
                file.file.close()

        result_data = {
            "total_files": len(files),
            "success_count": len(upload_results),
            "failed_count": len(failed_files),
            "success_files": upload_results,
            "failed_files": failed_files
        }

        if failed_files:
            return ResponseUtil.success(
                msg=f"批量上传到MinIO完成，成功{len(upload_results)}个，失败{len(failed_files)}个",
                data=result_data
            )
        else:
            return ResponseUtil.success(msg="批量上传到MinIO全部成功", data=result_data)

    except Exception as e:
        logger.error(f"Batch file upload to MinIO failed: {str(e)}")
        return ResponseUtil.error(msg=f"批量文件上传到MinIO失败: {str(e)}")

@filesController.get('/public/download')
async def public_download_file_by_path(
    request: Request,
    path: str,
    query_db: AsyncSession = Depends(get_db),
):
    """
    公共文件下载端点（不需要认证）
    仅用于图片等静态资源的显示
    
    :param path: 文件路径
    :return: 文件流
    """
    try:
        logger.info(f"公共下载请求: {path}")
        
        # 从MinIO下载文件
        minio_util = MinioUtil.get_instance()
        file_content = await minio_util.download_file_by_path(path)
        
        # 获取文件名
        file_name = os.path.basename(path)
        
        # 对文件名进行URL编码
        encoded_filename = urllib.parse.quote(file_name)
        
        logger.info(f"公共文件下载成功: {path}, 大小: {len(file_content)} 字节")
        
        # 返回文件流
        return StreamingResponse(
            io.BytesIO(file_content),
            media_type=minio_util._get_content_type(file_name),
            headers={
                "Content-Disposition": f"inline; filename*=UTF-8''{encoded_filename}",
                "Cache-Control": "public, max-age=3600"  # 缓存1小时
            }
        )
        
    except Exception as e:
        logger.error(f"公共文件下载失败: {str(e)}")
        return ResponseUtil.error(msg=f"文件下载失败: {str(e)}")

@filesController.get('/download-by-path')
async def download_file_by_path(
    request: Request,
    path: str,
    query_db: AsyncSession = Depends(get_db),
):
    """
    根据文件路径从MinIO下载文件
    
    :param path: 文件路径
    :return: 文件流
    """
    try:
        logger.info(f"尝试下载文件: {path}")

        # 清理路径，移除可能的URL前缀
        clean_path = path
        if path.startswith('http://') or path.startswith('https://'):
            # 如果路径是完整URL，提取path参数
            from urllib.parse import urlparse, parse_qs
            parsed_url = urlparse(path)
            if 'path' in parse_qs(parsed_url.query):
                clean_path = parse_qs(parsed_url.query)['path'][0]
                logger.info(f"从URL中提取路径: {clean_path}")

        # 清理文件名中的特殊字符
        import re
        clean_path = re.sub(r'[<>:"|?*]', '_', clean_path)  # 替换Windows不支持的字符
        logger.info(f"清理后的路径: {clean_path}")

        # 从MinIO下载文件
        minio_util = MinioUtil.get_instance()
        file_content = await minio_util.download_file_by_path(clean_path)
        
        # 获取文件名（使用清理后的路径）
        file_name = os.path.basename(clean_path)
        
        # 对文件名进行URL编码
        encoded_filename = urllib.parse.quote(file_name)
        
        logger.info(f"文件下载成功: {path}, 大小: {len(file_content)} 字节")
        
        # 返回文件流
        return StreamingResponse(
            io.BytesIO(file_content),
            media_type=minio_util._get_content_type(file_name),
            headers={
                "Content-Disposition": f"inline; filename*=UTF-8''{encoded_filename}"
            }
        )
        
    except Exception as e:
        logger.error(f"File download by path failed: {str(e)}")
        return ResponseUtil.error(msg=f"文件下载失败: {str(e)}")

@filesController.get('/download/{file_id}')  # dependencies=[Depends(LoginService.get_current_user), Depends(CheckUserInterfaceAuth('business:files:query'))]
@Log(title='数据库文件下载', business_type=BusinessType.OTHER)
async def download_file(
    request: Request,
    file_id: int,
    query_db: AsyncSession = Depends(get_db),
):
    """
    根据文件ID从MinIO下载文件
    
    :param file_id: 文件ID
    :return: 文件流
    """
    try:
        # 从MinIO下载文件
        minio_util = MinioUtil.get_instance()
        file_content = await minio_util.download_file_by_id(
            file_id=file_id,
            query_db=query_db
        )
        
        # 获取文件信息
        file_info = await FilesService.files_detail_services(query_db, file_id)
        if not file_info:
            return ResponseUtil.error(msg=f"File with ID {file_id} not found")
        
        # 对文件名进行URL编码
        encoded_filename = urllib.parse.quote(file_info.original_name)
        
        # 返回文件流
        return StreamingResponse(
            io.BytesIO(file_content),
            media_type=minio_util._get_content_type(file_info.original_name),
            headers={
                "Content-Disposition": f"attachment; filename*=UTF-8''{encoded_filename}"
            }
        )

    except Exception as e:
        logger.error(f"File download failed: {str(e)}")
        return ResponseUtil.error(msg=f"文件下载失败: {str(e)}")

@filesController.get('/list-by-task/{task_id}')
async def get_files_by_task(
    request: Request,
    task_id: int,
    query_db: AsyncSession = Depends(get_db),
):
    """
    根据任务ID获取文件列表

    :param task_id: 任务ID
    :return: 文件列表
    """
    try:
        # 查询该任务相关的文件
        files_result = await FilesService.get_files_by_task_id(query_db, task_id)

        return ResponseUtil.success(data=files_result, msg="获取任务文件列表成功")

    except Exception as e:
        logger.error(f"Get files by task failed: {str(e)}")
        return ResponseUtil.error(msg=f"获取任务文件列表失败: {str(e)}")

@filesController.get('/stats/{kb_id}')  # dependencies=[Depends(CheckUserInterfaceAuth('business:files:query'))]
async def get_file_stats_by_kb(
    request: Request,
    kb_id: int,
    query_db: AsyncSession = Depends(get_db),
):
    """
    根据数据库ID获取文件类型统计
    
    :param kb_id: 数据库ID
    :return: 各类型文件数量统计
    """
    try:
        file_stats = await FilesService.get_file_stats_by_kb_services(query_db, kb_id)
        logger.info(f'获取数据库{kb_id}的文件统计成功')
        return ResponseUtil.success(data=file_stats)
    except Exception as e:
        logger.error(f"获取文件统计失败: {str(e)}")
        return ResponseUtil.error(msg=f"获取文件统计失败: {str(e)}")

@filesController.get('/list/{kb_id}')  # dependencies=[Depends(CheckUserInterfaceAuth('business:files:query'))]
async def get_files_by_kb_and_type(
    request: Request,
    kb_id: int,
    file_type: str = None,
    query_db: AsyncSession = Depends(get_db),
):
    """
    根据数据库ID和文件类型获取文件列表
    
    :param kb_id: 数据库ID
    :param file_type: 文件类型（可选，不传则获取所有类型）
    :return: 文件列表
    """
    try:
        files = await FilesService.get_files_by_kb_and_type_services(query_db, kb_id, file_type)
        logger.info(f'获取数据库{kb_id}的文件列表成功')
        return ResponseUtil.success(data=files)
    except Exception as e:
        logger.error(f"获取文件列表失败: {str(e)}")
        return ResponseUtil.error(msg=f"获取文件列表失败: {str(e)}")

@filesController.get('/grouped/{kb_id}')  # dependencies=[Depends(CheckUserInterfaceAuth('business:files:query'))]
async def get_files_grouped_by_task(
    request: Request,
    kb_id: int,
    file_type: str = None,
    query_db: AsyncSession = Depends(get_db),
):
    """
    根据数据库ID和文件类型获取按task_id分组的文件列表

    :param kb_id: 数据库ID
    :param file_type: 文件类型（可选，不传则获取所有类型）
    :return: 按task_id分组的文件列表
    """
    try:
        grouped_files = await FilesService.get_files_grouped_by_task_services(query_db, kb_id, file_type)
        logger.info(f'获取数据库{kb_id}的分组文件列表成功')
        return ResponseUtil.success(data=grouped_files)
    except Exception as e:
        logger.error(f"获取分组文件列表失败: {str(e)}")
        return ResponseUtil.error(msg=f"获取分组文件列表失败: {str(e)}")

@filesController.get(
    '/{file_id}', response_model=FilesModel  # dependencies=[Depends(CheckUserInterfaceAuth('business:files:query'))]
)
async def query_detail_business_files(request: Request, file_id: int, query_db: AsyncSession = Depends(get_db)):
    files_detail_result = await FilesService.files_detail_services(query_db, file_id)
    logger.info(f'获取file_id为{file_id}的信息成功')

    return ResponseUtil.success(data=files_detail_result)
