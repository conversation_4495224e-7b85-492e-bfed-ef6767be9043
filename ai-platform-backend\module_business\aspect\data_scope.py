"""
数据权限过滤器
"""
from typing import List, Optional
from sqlalchemy import and_, or_, select
from sqlalchemy.ext.asyncio import AsyncSession
from config.role_constants import RoleConstants
from module_admin.entity.vo.user_vo import CurrentUserModel
from module_business.dao.project_members_dao import ProjectMembersDao
from module_business.dao.projects_dao import ProjectsDao


class DataScopeFilter:
    """
    数据权限过滤器基类
    """

    def __init__(self, table_alias: str = None):
        """
        初始化数据权限过滤器

        :param table_alias: 表别名
        """
        self.table_alias = table_alias

    async def apply_data_scope(
        self,
        query,
        current_user: CurrentUserModel,
        query_db: AsyncSession,
        scope_type: str = 'project'
    ):
        """
        应用数据权限过滤

        :param query: SQLAlchemy查询对象
        :param current_user: 当前用户
        :param query_db: 数据库会话
        :param scope_type: 权限范围类型 ('project', 'task', 'data')
        :return: 过滤后的查询对象
        """
        user_id = current_user.user.user_id
        user_role_keys = [role.role_key for role in current_user.user.role]

        # 超级管理员可以查看所有数据
        if RoleConstants.is_super_admin(user_role_keys):
            return query

        # 根据不同的权限范围类型应用不同的过滤逻辑
        if scope_type == 'project':
            return await self._apply_project_scope(query, user_id, user_role_keys, query_db)
        elif scope_type == 'task':
            return await self._apply_task_scope(query, user_id, user_role_keys, query_db)
        elif scope_type == 'data':
            return await self._apply_data_scope(query, user_id, user_role_keys, query_db)
        else:
            return query

    async def _apply_project_scope(
        self,
        query,
        user_id: int,
        user_role_keys: List[str],
        query_db: AsyncSession
    ):
        """
        应用项目级别的数据权限过滤

        :param query: SQLAlchemy查询对象
        :param user_id: 用户ID
        :param user_role_keys: 用户角色键值列表
        :param query_db: 数据库会话
        :return: 过滤后的查询对象
        """
        from module_business.entity.do.projects_do import RdProjects

        # 项目管理员和项目成员只能查看自己参与的项目
        if RoleConstants.is_project_manager(user_role_keys) or RoleConstants.is_project_member(user_role_keys):
            # 获取用户参与的项目ID列表
            user_projects = await ProjectMembersDao.get_user_projects(query_db, user_id)
            project_ids = [project.project_id for project in user_projects]
            
            # 获取用户拥有的项目ID列表
            owned_projects = await ProjectsDao.get_projects_by_owner(query_db, user_id)
            owned_project_ids = [project.project_id for project in owned_projects]
            
            # 合并项目ID列表
            all_project_ids = list(set(project_ids + owned_project_ids))
            
            if all_project_ids:
                # 过滤条件：用户参与的项目 OR 用户拥有的项目
                if self.table_alias:
                    project_table = getattr(query.column_descriptions[0]['type'], self.table_alias, RdProjects)
                else:
                    project_table = RdProjects
                
                query = query.where(
                    or_(
                        project_table.project_id.in_(all_project_ids),
                        project_table.owner_id == user_id
                    )
                )
            else:
                # 只能查看自己拥有的项目
                if self.table_alias:
                    project_table = getattr(query.column_descriptions[0]['type'], self.table_alias, RdProjects)
                else:
                    project_table = RdProjects
                query = query.where(project_table.owner_id == user_id)
        else:
            # 普通用户无法查看任何项目
            query = query.where(False)  # 永远不匹配的条件

        return query

    async def _apply_task_scope(
        self,
        query,
        user_id: int,
        user_role_keys: List[str],
        query_db: AsyncSession
    ):
        """
        应用任务级别的数据权限过滤

        :param query: SQLAlchemy查询对象
        :param user_id: 用户ID
        :param user_role_keys: 用户角色键值列表
        :param query_db: 数据库会话
        :return: 过滤后的查询对象
        """
        from module_business.entity.do.tasks_do import RdTasks
        from module_business.entity.do.projects_do import RdProjects

        # 项目管理员和项目成员只能查看自己参与项目的任务
        if RoleConstants.is_project_manager(user_role_keys) or RoleConstants.is_project_member(user_role_keys):
            # 获取用户参与的项目ID列表
            user_projects = await ProjectMembersDao.get_user_projects(query_db, user_id)
            project_ids = [project.project_id for project in user_projects]
            
            # 获取用户拥有的项目ID列表
            owned_projects = await ProjectsDao.get_projects_by_owner(query_db, user_id)
            owned_project_ids = [project.project_id for project in owned_projects]
            
            # 合并项目ID列表
            all_project_ids = list(set(project_ids + owned_project_ids))
            
            if all_project_ids:
                if self.table_alias:
                    task_table = getattr(query.column_descriptions[0]['type'], self.table_alias, RdTasks)
                else:
                    task_table = RdTasks
                query = query.where(task_table.project_id.in_(all_project_ids))
            else:
                # 没有参与任何项目，无法查看任何任务
                query = query.where(False)
        else:
            # 普通用户无法查看任何任务
            query = query.where(False)

        return query

    async def _apply_data_scope(
        self,
        query,
        user_id: int,
        user_role_keys: List[str],
        query_db: AsyncSession
    ):
        """
        应用数据级别的权限过滤

        :param query: SQLAlchemy查询对象
        :param user_id: 用户ID
        :param user_role_keys: 用户角色键值列表
        :param query_db: 数据库会话
        :return: 过滤后的查询对象
        """
        # 项目管理员和项目成员只能查看自己参与项目的数据
        if RoleConstants.is_project_manager(user_role_keys) or RoleConstants.is_project_member(user_role_keys):
            # 获取用户参与的项目ID列表
            user_projects = await ProjectMembersDao.get_user_projects(query_db, user_id)
            project_ids = [project.project_id for project in user_projects]
            
            # 获取用户拥有的项目ID列表
            owned_projects = await ProjectsDao.get_projects_by_owner(query_db, user_id)
            owned_project_ids = [project.project_id for project in owned_projects]
            
            # 合并项目ID列表
            all_project_ids = list(set(project_ids + owned_project_ids))
            
            if all_project_ids:
                # 假设数据表有project_id字段
                query = query.where(query.column_descriptions[0]['type'].project_id.in_(all_project_ids))
            else:
                # 没有参与任何项目，无法查看任何数据
                query = query.where(False)
        else:
            # 普通用户无法查看任何数据
            query = query.where(False)

        return query


class ProjectDataScopeFilter(DataScopeFilter):
    """
    项目数据权限过滤器
    """

    def __init__(self, table_alias: str = 'RdProjects'):
        super().__init__(table_alias)

    async def filter_projects(
        self,
        query,
        current_user: CurrentUserModel,
        query_db: AsyncSession
    ):
        """
        过滤项目数据

        :param query: SQLAlchemy查询对象
        :param current_user: 当前用户
        :param query_db: 数据库会话
        :return: 过滤后的查询对象
        """
        return await self.apply_data_scope(query, current_user, query_db, 'project')


class TaskDataScopeFilter(DataScopeFilter):
    """
    任务数据权限过滤器
    """

    def __init__(self, table_alias: str = 'RdTasks'):
        super().__init__(table_alias)

    async def filter_tasks(
        self,
        query,
        current_user: CurrentUserModel,
        query_db: AsyncSession
    ):
        """
        过滤任务数据

        :param query: SQLAlchemy查询对象
        :param current_user: 当前用户
        :param query_db: 数据库会话
        :return: 过滤后的查询对象
        """
        return await self.apply_data_scope(query, current_user, query_db, 'task')


class BusinessDataScopeFilter(DataScopeFilter):
    """
    业务数据权限过滤器
    """

    def __init__(self, table_alias: str = None):
        super().__init__(table_alias)

    async def filter_business_data(
        self,
        query,
        current_user: CurrentUserModel,
        query_db: AsyncSession
    ):
        """
        过滤业务数据

        :param query: SQLAlchemy查询对象
        :param current_user: 当前用户
        :param query_db: 数据库会话
        :return: 过滤后的查询对象
        """
        return await self.apply_data_scope(query, current_user, query_db, 'data')


def get_user_accessible_project_ids(
    current_user: CurrentUserModel,
    query_db: AsyncSession
) -> List[int]:
    """
    获取用户可访问的项目ID列表

    :param current_user: 当前用户
    :param query_db: 数据库会话
    :return: 项目ID列表
    """
    user_id = current_user.user.user_id
    user_role_keys = [role.role_key for role in current_user.user.role]

    # 超级管理员可以访问所有项目
    if RoleConstants.is_super_admin(user_role_keys):
        return []  # 空列表表示所有项目

    # 项目管理员和项目成员只能访问自己参与的项目
    if RoleConstants.is_project_manager(user_role_keys) or RoleConstants.is_project_member(user_role_keys):
        # 这里需要异步调用，实际使用时需要在异步函数中调用
        # user_projects = await ProjectMembersDao.get_user_projects(query_db, user_id)
        # owned_projects = await ProjectsDao.get_projects_by_owner(query_db, user_id)
        # return list(set([p.project_id for p in user_projects] + [p.project_id for p in owned_projects]))
        pass

    # 普通用户无法访问任何项目
    return [-1]  # 返回不存在的项目ID
