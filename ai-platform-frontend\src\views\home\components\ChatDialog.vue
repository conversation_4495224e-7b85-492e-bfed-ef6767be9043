<template>
  <div class="chat-dialog">
    <!-- 上方区域：知识库选择 -->
    <div class="top-section">
      <el-select 
        v-model="selectedKnowledgeBase" 
        placeholder="请选择知识库"
        class="knowledge-select"
        filterable
      >
        <el-option
          v-for="kb in knowledgeBases"
          :key="kb.kbId"
          :label="kb.kbName"
          :value="kb.kbId"
        />
      </el-select>
    </div>

    <!-- 分隔线 -->
    <div class="divider"></div>

    <!-- 下方区域：对话输入 -->
    <div class="bottom-section">
      <div class="input-container">
      <!-- 操作区域 -->
      <div class="action-area">
        <!-- 文件上传图标 -->
        <el-tooltip content="上传文件/图片" placement="top">
          <el-upload
            ref="uploadRef"
            :auto-upload="false"
            :on-change="handleFileChange"
            :on-remove="handleFileRemove"
            :file-list="fileList"
            :show-file-list="false"
            multiple
            class="upload-icon"
          >
            <el-button 
              type="text" 
              class="upload-btn"
              :icon="Paperclip"
            />
          </el-upload>
        </el-tooltip>
      </div>

        <el-input
          v-model="inputMessage"
          type="textarea"
          :rows="2"
          placeholder="请输入您的问题..."
          class="message-input"
          @input="handleInput"
          @keydown.enter.prevent="handleEnterKey"
          ref="inputRef"
        />
        
        <!-- 历史记录下拉菜单 -->
        <div v-if="showHistory && historyRecords.length > 0" class="history-dropdown">
          <div
            v-for="(record, index) in filteredHistory"
            :key="index"
            class="history-item"
            @click="selectHistory(record)"
          >
            <el-icon><chat-dot-round /></el-icon>
            <span class="history-text">{{ record }}</span>
        </div>
      </div>

      <!-- 发送按钮区域 -->
      <div class="send-area">
        <!-- 发送按钮 -->
        <el-button 
          type="primary" 
          @click="handleSubmit"
          :disabled="!canSubmit"
          :loading="submitting"
          class="send-btn"
          :icon="Promotion"
        />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, nextTick, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { UploadFilled, ChatDotRound, Paperclip, Promotion } from '@element-plus/icons-vue'
import { getKnowledgeBasesList } from '@/api/knowledge-bases'
import { submitChat, uploadFileForChat } from '@/api/chat'

const router = useRouter()

// 响应式数据
const selectedKnowledgeBase = ref('')
const inputMessage = ref('')
const showHistory = ref(false)
const submitting = ref(false)
const fileList = ref([])
const knowledgeBases = ref([])
const historyRecords = ref([])

// 组件引用
const inputRef = ref(null)
const uploadRef = ref(null)

// 计算属性
const canSubmit = computed(() => {
  return inputMessage.value.trim().length > 0
})

const filteredHistory = computed(() => {
  if (!inputMessage.value) return historyRecords.value.slice(0, 5)
  return historyRecords.value
    .filter(record => record.toLowerCase().includes(inputMessage.value.toLowerCase()))
    .slice(0, 5)
})

// 方法

const handleInput = () => {
  showHistory.value = inputMessage.value.length > 0
}

const handleEnterKey = (e) => {
  if (e.shiftKey) {
    // Shift + Enter 换行
    return
  }
  // Enter 提交
  handleSubmit()
}

const selectHistory = (record) => {
  inputMessage.value = record
  showHistory.value = false
}

const handleFileChange = (file) => {
  // 文件类型验证
  const allowedTypes = ['image/jpeg', 'image/png', 'application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'text/plain']
  if (!allowedTypes.includes(file.raw.type)) {
    ElMessage.error('不支持的文件类型')
    return false
  }
  
  // 文件大小验证（10MB）
  if (file.size > 10 * 1024 * 1024) {
    ElMessage.error('文件大小不能超过10MB')
    return false
  }
  
  return true
}

const handleFileRemove = (file) => {
  // 文件移除逻辑
}

const handleSubmit = async () => {
  if (!canSubmit.value) return
  
  submitting.value = true
  
  try {
    // 保存到历史记录
    if (inputMessage.value.trim()) {
      const newRecord = inputMessage.value.trim()
      if (!historyRecords.value.includes(newRecord)) {
        historyRecords.value.unshift(newRecord)
        // 只保留最近20条记录
        if (historyRecords.value.length > 20) {
          historyRecords.value = historyRecords.value.slice(0, 20)
        }
        // 保存到本地存储
        localStorage.setItem('chatHistory', JSON.stringify(historyRecords.value))
      }
    }
    
    // 准备提交数据
    const submitData = {
      type: 'mixed',
      message: inputMessage.value.trim(),
      knowledgeBaseId: selectedKnowledgeBase.value,
      files: fileList.value.map(file => ({
        name: file.name,
        size: file.size,
        type: file.raw.type
      }))
    }
    
    // 调用后端接口
    let response
    if (fileList.value.length > 0) {
      // 有文件上传
      const formData = new FormData()
      formData.append('message', inputMessage.value.trim())
      formData.append('knowledgeBaseId', selectedKnowledgeBase.value || '')
      formData.append('type', 'mixed')
      
      fileList.value.forEach(file => {
        formData.append('files', file.raw)
      })
      
      response = await uploadFileForChat(formData)
    } else {
      // 纯文本对话
      response = await submitChat(submitData)
    }
    
    if (response.code === 200) {
      ElMessage.success('对话已提交，正在跳转到对话界面...')
      // 跳转到对话界面
      router.push('/home/<USER>')
    } else {
      ElMessage.error(response.msg || '提交失败')
    }
    
  } catch (error) {
    ElMessage.error('提交失败，请重试')
  } finally {
    submitting.value = false
  }
}

// 获取知识库列表
const fetchKnowledgeBases = async () => {
  try {
    const response = await getKnowledgeBasesList({ pageNum: 1, pageSize: 100 })
    if (response.code === 200) {
      knowledgeBases.value = response.rows || []
    }
  } catch (error) {
    console.error('获取知识库列表失败:', error)
  }
}

// 加载历史记录
const loadHistory = () => {
  try {
    const saved = localStorage.getItem('chatHistory')
    if (saved) {
      historyRecords.value = JSON.parse(saved)
    }
  } catch (error) {
    console.error('加载历史记录失败:', error)
  }
}

// 点击外部关闭历史记录
const handleClickOutside = (event) => {
  if (inputRef.value && !inputRef.value.$el.contains(event.target)) {
    showHistory.value = false
  }
}

// 生命周期
onMounted(() => {
  fetchKnowledgeBases()
  loadHistory()
  
  // 添加点击外部关闭历史记录的事件监听
  document.addEventListener('click', handleClickOutside)
})

// 组件卸载时移除事件监听
onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>

<style scoped>
.chat-dialog {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  width: 60%;
  max-width: 800px;
  margin: 0 auto;
  aspect-ratio: 4 / 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.top-section {
  flex: 1;
  padding: 20px 24px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.knowledge-select {
  width: 25%;
  min-width: 200px;
}

.knowledge-select :deep(.el-input__wrapper) {
  border-radius: 8px;
  border: 1px solid rgba(102, 126, 234, 0.2);
  background: rgba(255, 255, 255, 0.8);
  transition: all 0.3s ease;
  
  &:hover, &.is-focus {
    border-color: #667eea;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
  }
}

.divider {
  height: 1px;
  background: #e4e7ed;
  margin: 0;
  width: 100%;
}

.bottom-section {
  flex: 2;
  padding: 20px 24px;
  display: flex;
  align-items: flex-end;
}

.input-container {
  flex: 1;
  position: relative;
  display: flex;
  align-items: flex-end;
  gap: 12px;
}

.action-area {
  display: flex;
  align-items: center;
  flex-shrink: 0;
}

.send-area {
  display: flex;
  align-items: center;
  flex-shrink: 0;
}

.message-input {
  flex: 1;
}

.message-input :deep(.el-textarea__inner) {
  border-radius: 12px;
  border: 1px solid rgba(102, 126, 234, 0.2);
  background: rgba(255, 255, 255, 0.8);
  transition: all 0.3s ease;
  resize: none;
  
  &:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
  }
}

.history-dropdown {
  position: absolute;
  bottom: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  max-height: 200px;
  overflow-y: auto;
  margin-bottom: 8px;
}

.history-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  cursor: pointer;
  transition: background-color 0.2s;
  
  &:hover {
    background-color: #f5f7fa;
  }
  
  &:not(:last-child) {
    border-bottom: 1px solid #f0f0f0;
  }
}

.history-text {
  flex: 1;
  color: #606266;
  font-size: 14px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}



.upload-icon {
  display: inline-block;
}

.upload-btn {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #606266;
  transition: all 0.3s ease;
  
  &:hover {
    background-color: rgba(102, 126, 234, 0.1);
    color: #667eea;
  }
}

.send-btn {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  transition: all 0.3s ease;
  
  &:hover:not(:disabled) {
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
  }
  
  &:disabled {
    background: #c0c4cc;
    cursor: not-allowed;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .chat-dialog {
    margin: 0 16px;
    aspect-ratio: auto;
    min-height: 200px;
  }
  
  .top-section,
  .bottom-section {
    padding: 16px;
  }
  
  .top-section {
    justify-content: center;
  }
  
  .knowledge-select {
    width: 100%;
    max-width: 300px;
  }
  
  .bottom-section {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }
  
  .input-container {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }
  
  .action-area {
    justify-content: flex-start;
  }
  
  .send-area {
    justify-content: flex-end;
  }
}
</style> 