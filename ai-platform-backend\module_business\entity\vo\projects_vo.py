from datetime import datetime
from pydantic import BaseModel, ConfigDict, Field
from pydantic.alias_generators import to_camel
from pydantic_validation_decorator import NotBlank
from typing import Optional
from module_admin.annotation.pydantic_annotation import as_query




class ProjectsModel(BaseModel):
    """
    项目信息管理表对应pydantic模型
    """
    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True)

    project_id: Optional[int] = Field(default=None, description='')
    project_name: Optional[str] = Field(default=None, description='')
    description: Optional[str] = Field(default=None, description='')
    owner_id: Optional[int] = Field(default=None, description='')
    is_deleted: Optional[int] = Field(default=0, description='')
    created_at: Optional[datetime] = Field(default=None, description='')
    updated_at: Optional[datetime] = Field(default=None, description='')

    @NotBlank(field_name='project_name', message='项目名称不能为空')
    def get_project_name(self):
        return self.project_name




    def validate_fields(self):
        self.get_project_name()
        # self.get_owner_id()




class ProjectsQueryModel(ProjectsModel):
    """
    项目信息管理不分页查询模型
    """
    pass


@as_query
class ProjectsPageQueryModel(ProjectsQueryModel):
    """
    项目信息管理分页查询模型
    """

    page_num: int = Field(default=1, description='当前页码')
    page_size: int = Field(default=10, description='每页记录数')
    order_by_column: Optional[str] = Field(default=None, description='排序字段')
    is_asc: Optional[bool] = Field(default=True, description='是否升序')


class DeleteProjectsModel(BaseModel):
    """
    删除项目信息管理模型
    """

    model_config = ConfigDict(alias_generator=to_camel)

    project_ids: str = Field(description='需要删除的')
