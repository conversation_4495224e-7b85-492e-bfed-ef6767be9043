import request from '@/utils/request'

// 获取项目列表
// export function getProjectsList(params) {
//   return request({
//     url: '/business/projects/list',
//     method: 'get',
//     params
//   })
// }

// 获取最近项目列表
// export function getRecentProjects(limit = 5) {
//   return request({
//     url: '/business/projects/recent',
//     method: 'get',
//     params: { limit }
//   })
// }

// 获取带类型标签的项目列表
export function getProjectsListWithTypes(query) {
  return request({
    url: '/business/projects/list_with_types',
    method: 'get',
    params: query
  })
}

// 新增项目
export function addProject(data) {
  return request({
    url: '/business/projects',
    method: 'post',
    data
  })
}

// 编辑项目
export function updateProject(data) {
  return request({
    url: '/business/projects',
    method: 'put',
    data
  })
}

// 按类型多选、分页、搜索获取项目列表
export function getProjectsByTypes({ typeIds = '', pageNum = 1, pageSize = 12, projectName = '' }) {
  return request({
    url: '/business/project_types_rel/projects/by_types',
    method: 'get',
    params: { type_ids: typeIds, page_num: pageNum, page_size: pageSize, project_name: projectName }
  })
}

// 获取单个项目详情
export function getProjectDetail(projectId) {
  return request({
    url: `/business/projects/${projectId}`,
    method: 'get'
  })
} 