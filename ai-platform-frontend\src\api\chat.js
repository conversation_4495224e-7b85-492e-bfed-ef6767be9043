import request from '@/utils/request'

// WebSocket连接类
export class ChatWebSocket {
  constructor(sessionId = null, onMessage, onError, onClose) {
    this.sessionId = sessionId
    this.onMessage = onMessage
    this.onError = onError
    this.onClose = onClose
    this.ws = null
    this.heartbeatInterval = null
    this.reconnectAttempts = 0
    this.maxReconnectAttempts = 5
    this.isConnected = false
  }

  connect() {
    try {
      // 构建WebSocket URL
      let wsUrl
      if (import.meta.env.MODE === 'development') {
        // 开发环境：连接到主应用的chat接口
        wsUrl = `ws://localhost:8000/chat/ws/${this.sessionId || 'default'}`
      } else {
        // 生产环境：使用当前页面的协议和主机
        const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:'
        const host = window.location.host
        wsUrl = `${protocol}//${host}/chat/ws/${this.sessionId || 'default'}`
      }
      
      console.log('尝试连接Chat WebSocket:', wsUrl)
      this.ws = new WebSocket(wsUrl)
      
      this.ws.onopen = () => {
        console.log(`Chat WebSocket连接已建立，会话ID: ${this.sessionId}`)
        this.isConnected = true
        this.reconnectAttempts = 0
        this.startHeartbeat()
      }
      
      this.ws.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data)
          if (this.onMessage) {
            this.onMessage(data)
          }
        } catch (error) {
          console.error('解析Chat WebSocket消息失败:', error)
        }
      }
      
      this.ws.onerror = (error) => {
        console.error('Chat WebSocket错误:', error)
        this.isConnected = false
        if (this.onError) {
          this.onError(error)
        }
      }
      
      this.ws.onclose = (event) => {
        console.log('Chat WebSocket连接已关闭:', event.code, event.reason)
        this.isConnected = false
        this.stopHeartbeat()
        if (this.onClose) {
          this.onClose(event)
        }
        
        // 自动重连
        if (this.reconnectAttempts < this.maxReconnectAttempts) {
          this.reconnectAttempts++
          console.log(`尝试重连Chat WebSocket (${this.reconnectAttempts}/${this.maxReconnectAttempts})...`)
          setTimeout(() => {
            this.connect()
          }, 3000)
        }
      }
    } catch (error) {
      console.error('创建Chat WebSocket连接失败:', error)
      if (this.onError) {
        this.onError(error)
      }
    }
  }

  startHeartbeat() {
    this.heartbeatInterval = setInterval(() => {
      if (this.ws && this.ws.readyState === WebSocket.OPEN) {
        this.sendMessage({ type: 'ping' })
      }
    }, 30000) // 每30秒发送一次心跳
  }

  stopHeartbeat() {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval)
      this.heartbeatInterval = null
    }
  }

  disconnect() {
    this.stopHeartbeat()
    if (this.ws) {
      this.ws.close()
      this.ws = null
    }
    this.isConnected = false
  }

  sendMessage(message) {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(message))
      return true
    } else {
      console.error('WebSocket未连接，无法发送消息')
      return false
    }
  }

  sendChatMessage(message) {
    return this.sendMessage({
      type: 'chat',
      message: message,
      timestamp: new Date().toISOString()
    })
  }

  clearHistory() {
    return this.sendMessage({
      type: 'clear',
      timestamp: new Date().toISOString()
    })
  }
}

// 传统的HTTP API调用（保留兼容性）
// 提交对话
export function submitChat(data) {
  return request({
    url: '/chat/submit',
    method: 'post',
    data: data
  })
}

// 获取对话历史
export function getChatHistory(params) {
  return request({
    url: '/chat/history',
    method: 'get',
    params: params
  })
}

// 清空对话历史
export function clearChatHistory(sessionId) {
  return request({
    url: '/chat/clear',
    method: 'delete',
    params: { session_id: sessionId }
  })
}

// 上传文件进行对话
export function uploadFileForChat(data) {
  return request({
    url: '/chat/upload',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 获取会话列表
export function getChatSessions() {
  return request({
    url: '/chat/sessions',
    method: 'get'
  })
}

// 获取会话详情
export function getChatSession(sessionId) {
  return request({
    url: `/chat/sessions/${sessionId}`,
    method: 'get'
  })
}

// 删除指定会话
export function deleteChatSession(sessionId) {
  return request({
    url: `/chat/sessions/${sessionId}`,
    method: 'delete'
  })
} 