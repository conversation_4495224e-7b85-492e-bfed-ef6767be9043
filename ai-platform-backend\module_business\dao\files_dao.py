from sqlalchemy import delete, select, update, func
from sqlalchemy.ext.asyncio import AsyncSession
from module_business.entity.do.files_do import RdKnowledgeFiles
from module_business.entity.vo.files_vo import FilesModel, FilesPageQueryModel
from utils.page_util import PageUtil


class FilesDao:
    """
    数据库文件存储模块数据库操作层
    """

    @classmethod
    async def get_files_detail_by_id(cls, db: AsyncSession, file_id: int):
        """
        根据文件id获取数据库文件存储详细信息

        :param db: orm对象
        :param file_id: 文件id
        :return: 数据库文件存储信息对象
        """
        files_info = (
            (
                await db.execute(
                    select(RdKnowledgeFiles)
                    .where(
                        RdKnowledgeFiles.file_id == file_id
                    )
                )
            )
            .scalars()
            .first()
        )

        return files_info

    @classmethod
    async def get_files_detail_by_info(cls, db: AsyncSession, files: FilesModel):
        """
        根据数据库文件存储参数获取数据库文件存储信息

        :param db: orm对象
        :param files: 数据库文件存储参数对象
        :return: 数据库文件存储信息对象
        """
        files_info = (
            (
                await db.execute(
                    select(RdKnowledgeFiles).where(
                    )
                )
            )
            .scalars()
            .first()
        )

        return files_info

    @classmethod
    async def get_files_list(cls, db: AsyncSession, query_object: FilesPageQueryModel, is_page: bool = False):
        """
        根据查询参数获取数据库文件存储列表信息

        :param db: orm对象
        :param query_object: 查询参数对象
        :param is_page: 是否开启分页
        :return: 数据库文件存储列表信息对象
        """
        query = (
            select(RdKnowledgeFiles)
            .where(
                RdKnowledgeFiles.kb_id == query_object.kb_id if query_object.kb_id else True,
                RdKnowledgeFiles.project_id == query_object.project_id if query_object.project_id else True,
                RdKnowledgeFiles.file_type == query_object.file_type if query_object.file_type else True,
                RdKnowledgeFiles.original_name.like(f'%{query_object.original_name}%') if query_object.original_name else True,
                RdKnowledgeFiles.storage_path == query_object.storage_path if query_object.storage_path else True,
                RdKnowledgeFiles.file_size == query_object.file_size if query_object.file_size else True,
                RdKnowledgeFiles.file_format == query_object.file_format if query_object.file_format else True,
                RdKnowledgeFiles.source_task_id == query_object.source_task_id if query_object.source_task_id else True,
                RdKnowledgeFiles.file_metadata  == query_object.file_metadata  if query_object.file_metadata  else True,
                RdKnowledgeFiles.created_at == query_object.created_at if query_object.created_at else True,
                RdKnowledgeFiles.is_deleted == query_object.is_deleted if query_object.is_deleted else True,
            )
            .order_by(RdKnowledgeFiles.file_id)
            .distinct()
        )
        files_list = await PageUtil.paginate(db, query, query_object.page_num, query_object.page_size, is_page)

        return files_list

    @classmethod
    async def add_files_dao(cls, db: AsyncSession, files: FilesModel):
        """
        新增数据库文件存储数据库操作

        :param db: orm对象
        :param files: 数据库文件存储对象
        :return:
        """
        db_files = RdKnowledgeFiles(**files.model_dump(exclude={}))
        db.add(db_files)
        await db.flush()

        return db_files

    @classmethod
    async def edit_files_dao(cls, db: AsyncSession, files: dict):
        """
        编辑数据库文件存储数据库操作

        :param db: orm对象
        :param files: 需要更新的数据库文件存储字典
        :return:
        """
        await db.execute(update(RdKnowledgeFiles), [files])

    @classmethod
    async def delete_files_dao(cls, db: AsyncSession, files: FilesModel):
        """
        删除数据库文件存储数据库操作

        :param db: orm对象
        :param files: 数据库文件存储对象
        :return:
        """
        await db.execute(delete(RdKnowledgeFiles).where(RdKnowledgeFiles.file_id.in_([files.file_id])))

    @classmethod
    async def get_file_stats_by_kb(cls, db: AsyncSession, kb_id: int):
        """
        根据数据库ID获取文件类型统计

        :param db: orm对象
        :param kb_id: 数据库ID
        :return: 各类型文件数量统计
        """
        query = (
            select(RdKnowledgeFiles.file_type, func.count(RdKnowledgeFiles.file_id).label('count'))
            .where(
                RdKnowledgeFiles.kb_id == kb_id,
                RdKnowledgeFiles.is_deleted == False
            )
            .group_by(RdKnowledgeFiles.file_type)
        )

        result = await db.execute(query)
        stats = {}

        for row in result.fetchall():
            file_type = row[0]
            count = row[1]
            stats[file_type] = count

        # 确保所有类型都有统计，没有的设为0
        all_types = ['model', 'report', 'dataset', 'log', 'document', 'archived']
        for file_type in all_types:
            if file_type not in stats:
                stats[file_type] = 0

        return stats

    @classmethod
    async def get_files_by_kb_and_type(cls, db: AsyncSession, kb_id: int, file_type: str = None):
        """
        根据数据库ID和文件类型获取文件列表

        :param db: orm对象
        :param kb_id: 数据库ID
        :param file_type: 文件类型（可选，不传则获取所有类型）
        :return: 文件列表
        """
        query = (
            select(RdKnowledgeFiles)
            .where(
                RdKnowledgeFiles.kb_id == kb_id,
                RdKnowledgeFiles.is_deleted == False
            )
            .order_by(RdKnowledgeFiles.created_at.desc())
        )
        
        if file_type:
            query = query.where(RdKnowledgeFiles.file_type == file_type)

        result = await db.execute(query)
        files = result.scalars().all()
        
        return files

    @classmethod
    async def get_files_grouped_by_task(cls, db: AsyncSession, kb_id: int, file_type: str = None):
        """
        根据数据库ID和文件类型获取按task_id分组的文件列表

        :param db: orm对象
        :param kb_id: 数据库ID
        :param file_type: 文件类型（可选，不传则获取所有类型）
        :return: 按task_id分组的文件列表
        """
        query = (
            select(RdKnowledgeFiles)
            .where(
                RdKnowledgeFiles.kb_id == kb_id,
                RdKnowledgeFiles.is_deleted == False
            )
            .order_by(RdKnowledgeFiles.source_task_id.desc(), RdKnowledgeFiles.created_at.desc())
        )

        if file_type:
            query = query.where(RdKnowledgeFiles.file_type == file_type)

        result = await db.execute(query)
        files = result.scalars().all()

        # 按task_id分组
        grouped_files = {}
        for file in files:
            task_id = file.source_task_id
            task_key = f"task_{task_id}" if task_id else "no_task"

            if task_key not in grouped_files:
                grouped_files[task_key] = []
            grouped_files[task_key].append(file)

        return grouped_files

    @classmethod
    async def get_files_by_task_id(cls, db: AsyncSession, task_id: int):
        """
        根据任务ID获取该任务的所有文件

        :param db: orm对象
        :param task_id: 任务ID
        :return: 文件列表
        """
        query = (
            select(RdKnowledgeFiles)
            .where(
                RdKnowledgeFiles.source_task_id == task_id,
                RdKnowledgeFiles.is_deleted == False
            )
            .order_by(RdKnowledgeFiles.created_at.desc())
        )

        result = await db.execute(query)
        files = result.scalars().all()
        
        return files

    @classmethod
    async def get_files_by_path_and_task(cls, db: AsyncSession, storage_path: str, task_id: int):
        """
        根据存储路径和任务ID检查文件是否已存在

        :param db: orm对象
        :param storage_path: 存储路径
        :param task_id: 任务ID
        :return: 文件信息对象，如果不存在则返回None
        """
        files_info = (
            (
                await db.execute(
                    select(RdKnowledgeFiles)
                    .where(
                        RdKnowledgeFiles.storage_path == storage_path,
                        RdKnowledgeFiles.source_task_id == task_id,
                        RdKnowledgeFiles.is_deleted == False
                    )
                )
            )
            .scalars()
            .first()
        )

        return files_info