# CV-AI产品设计平台架构分析

## 1. 项目整体架构

该项目是一个基于 FastAPI (后端) 和 Vue3 (前端) 的前后端分离的管理系统框架。项目采用了现代化的技术栈和最佳实践。

### 1.1 技术栈

#### 后端技术栈：
- FastAPI：高性能的 Python Web 框架
- SQLAlchemy：ORM 框架
- Redis：缓存数据库
- Celery：任务队列（用于异步任务处理）
- JWT：用户认证

#### 前端技术栈：
- Vue3：前端框架
- Vite：构建工具
- Element Plus：UI 组件库
- Pinia：状态管理
- Vue Router：路由管理

### 1.2 目录结构

#### 后端目录结构
```
ai-platform-backend/
├── app.py                 # 应用入口文件
├── server.py             # 服务器配置
├── config/               # 配置文件目录
├── module_admin/         # 管理模块
├── module_generator/     # 代码生成模块
├── module_task/         # 任务模块
├── middlewares/         # 中间件
├── exceptions/          # 异常处理
├── utils/               # 工具类
├── vf_admin/            # 后台管理相关
└── sub_applications/    # 子应用模块
```

#### 前端目录结构
```
ai-platform-frontend/
├── src/                 # 源代码目录
├── public/              # 静态资源
├── vite/               # Vite 配置
└── package.json        # 项目依赖配置
```

## 2. 系统架构图

```mermaid
graph TB
    subgraph Frontend
        Vue3[Vue3 前端应用]
        Router[Vue Router]
        Store[Pinia Store]
        UI[Element Plus]
    end
    
    subgraph Backend
        FastAPI[FastAPI 应用]
        Auth[认证中间件]
        Router_Backend[路由层]
        Service[服务层]
        Model[数据模型层]
    end
    
    subgraph Database
        MySQL[(MySQL/PG)]
        Redis[(Redis)]
    end
    
    subgraph Tasks
        Celery[Celery 任务队列]
    end

    Vue3 --> Router
    Vue3 --> Store
    Vue3 --> UI
    Vue3 -.-> |API 请求| FastAPI
    
    FastAPI --> Auth
    Auth --> Router_Backend
    Router_Backend --> Service
    Service --> Model
    Model --> MySQL
    Service --> Redis
    Service --> Celery
</mermaid>

## 3. 核心流程

### 3.1 系统启动流程
1. 后端启动流程（详细）：
   - 通过 `app.py` 启动 uvicorn 服务器
   - 在 `server.py` 中进行主要的应用初始化：
     1. 执行生命周期事件（lifespan）：
        - 初始化数据库表
        - 创建 Redis 连接池
        - 初始化系统字典和配置到 Redis
        - 初始化系统调度器
     2. 初始化 FastAPI 应用实例
     3. 挂载子应用
     4. 加载中间件
     5. 配置全局异常处理
     6. 注册所有控制器路由

2. 前端启动流程（详细）：
   - 通过 `main.js` 初始化 Vue 应用：
     1. 导入全局样式和组件库
     2. 注册全局组件：
        - DictTag（字典标签组件）
        - Pagination（分页组件）
        - FileUpload（文件上传组件）
        - ImageUpload（图片上传组件）
        - ImagePreview（图片预览组件）
        - RightToolbar（表格工具组件）
        - Editor（富文本编辑器）
     3. 挂载全局方法：
        - useDict：字典操作
        - download：文件下载
        - parseTime：时间解析
        - resetForm：表单重置
        - handleTree：树形数据处理
        - addDateRange：日期范围处理
        - selectDictLabel：字典标签选择
     4. 配置路由和状态管理
     5. 配置 ElementPlus UI 库
     6. 挂载应用到 DOM

### 3.2 请求处理流程
1. 前端发起请求
2. 后端中间件处理（认证、日志等）
3. 路由分发
4. 业务逻辑处理
5. 数据库操作
6. 响应返回

### 3.3 系统模块说明

#### 前端模块列表
1. 全局组件
   - 字典标签组件
   - 分页组件
   - 文件上传组件
   - 图片上传组件
   - 图片预览组件
   - 表格工具组件
   - 富文本编辑器
2. 工具模块
   - 字典操作
   - 文件下载
   - 时间处理
   - 表单处理
   - 树形数据处理
3. 权限管理
4. 路由管理
5. 状态管理
6. UI 组件（Element Plus）

#### 后端模块列表
1. 登录模块
2. 验证码模块
3. 系统管理
   - 用户管理
   - 角色管理
   - 菜单管理
   - 部门管理
   - 岗位管理
   - 字典管理
   - 参数管理
   - 通知公告管理
   - 日志管理
4. 系统监控
   - 在线用户
   - 定时任务
   - 菜单管理
   - 缓存监控
5. 通用模块
6. 代码生成

### 3.4 入口文件说明

#### 前端入口文件
- `src/main.js`: Vue 应用主入口
  - 全局组件注册
  - 全局方法配置
  - 路由和状态管理配置
  - UI 库配置
- `src/router/index.js`: 路由配置
- `src/store/index.js`: 状态管理配置
- `src/permission.js`: 权限控制配置

#### 后端入口文件
- `app.py`: 应用程序主入口，负责启动 uvicorn 服务器
- `server.py`: 核心服务器配置文件
  - 配置生命周期事件
  - 初始化数据库和 Redis 连接
  - 注册路由和中间件
  - 配置异常处理 