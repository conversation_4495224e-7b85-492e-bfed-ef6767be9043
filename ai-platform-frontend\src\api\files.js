import request from '@/utils/request'

// 根据数据库ID和文件类型获取文件列表
export function getFilesByKbAndType(kbId, fileType = null) {
  const params = {}
  if (fileType) {
    params.file_type = fileType
  }
  return request({
    url: `/business/files/list/${kbId}`,
    method: 'get',
    params: params
  })
}

// 根据数据库ID和文件类型获取按task_id分组的文件列表
export function getFilesGroupedByTask(kbId, fileType = null) {
  const params = {}
  if (fileType) {
    params.file_type = fileType
  }
  return request({
    url: `/business/files/grouped/${kbId}`,
    method: 'get',
    params: params
  })
}

// 下载文件
export function downloadFile(fileId) {
  return request({
    url: `/business/files/download/${fileId}`,
    method: 'get',
    responseType: 'blob'
  })
}

// 删除文件（逻辑删除）
export function deleteFile(fileId) {
  return request({
    url: `/business/files`,
    method: 'put',
    data: {
      fileId: fileId,
      isDeleted: 1
    }
  })
}

// 上传文件
export function uploadFile(kbId, fileType, file, projectId = null, sourceTaskId = null, subFolder = null) {
  const formData = new FormData()
  formData.append('file', file)

  // 构建查询参数
  const params = {}
  if (projectId) {
    params.project_id = projectId
  }
  if (sourceTaskId) {
    params.source_task_id = sourceTaskId
  }
  if (subFolder) {
    params.sub_folder = subFolder
  }

  return request({
    url: `/business/files/upload/${kbId}/${fileType}`,
    method: 'post',
    data: formData,
    params: params,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 仅上传文件到MinIO（不保存到数据库）
export function uploadFileToMinioOnly(kbId, fileType, file, taskId = null, subFolder = null) {
  const formData = new FormData()
  formData.append('file', file)

  // 构建查询参数
  const params = {}
  if (taskId) {
    params.task_id = taskId
  }
  if (subFolder) {
    params.sub_folder = subFolder
  }

  return request({
    url: `/business/files/upload-minio/${kbId}/${fileType}`,
    method: 'post',
    data: formData,
    params: params,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 根据文件路径下载文件
export function downloadFileByPath(filePath) {
  return request({
    url: `/business/files/download-by-path`,
    method: 'get',
    params: { path: filePath },
    responseType: 'blob'
  })
}

// 根据任务ID获取文件列表
export function getFilesByTaskId(taskId) {
  return request({
    url: `/business/files/list-by-task/${taskId}`,
    method: 'get'
  })
}

// 批量上传文件
export function batchUploadFiles(kbId, files, projectId = null, sourceTaskId = null, subFolder = null) {
  const formData = new FormData()

  // 添加多个文件
  files.forEach(file => {
    formData.append('files', file)
  })

  // 构建查询参数
  const params = {}
  if (projectId) {
    params.project_id = projectId
  }
  if (sourceTaskId) {
    params.source_task_id = sourceTaskId
  }
  if (subFolder) {
    params.sub_folder = subFolder
  }

  return request({
    url: `/business/files/batch-upload/${kbId}`,
    method: 'post',
    data: formData,
    params: params,
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    timeout: 300000 // 5分钟超时，因为批量上传可能需要更长时间
  })
}

// 批量上传文件到MinIO（不保存到数据库）
export function batchUploadFilesToMinioOnly(kbId, files, taskId = null, subFolder = null) {
  const formData = new FormData()

  // 添加多个文件
  files.forEach(file => {
    formData.append('files', file)
  })

  // 构建查询参数
  const params = {}
  if (taskId) {
    params.task_id = taskId
  }
  if (subFolder) {
    params.sub_folder = subFolder
  }

  return request({
    url: `/business/files/batch-upload-minio/${kbId}`,
    method: 'post',
    data: formData,
    params: params,
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    timeout: 300000 // 5分钟超时
  })
}