from datetime import datetime
from pydantic import BaseModel, ConfigDict, Field
from pydantic.alias_generators import to_camel
from pydantic_validation_decorator import NotBlank
from typing import Optional
from module_admin.annotation.pydantic_annotation import as_query




class FilesModel(BaseModel):
    """
    数据库文件存储表对应pydantic模型
    """
    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True)

    file_id: Optional[int] = Field(default=None, description='文件id')
    kb_id: Optional[int] = Field(default=None, description='关联数据库id')
    project_id: Optional[int] = Field(default=None, description='冗余存储便于查询')
    file_type: Optional[str] = Field(default=None, description='文件类型')
    original_name: Optional[str] = Field(default=None, description='源文件名称')
    storage_path: Optional[str] = Field(default=None, description='OSS存储路径')
    file_size: Optional[int] = Field(default=None, description='文件大小')
    file_format: Optional[str] = Field(default=None, description='文件扩展名')
    source_task_id: Optional[int] = Field(default=None, description='关联产生此文件的任务')
    file_metadata: Optional[dict] = Field(default=None, description='包含模型参数/仿真条件等')
    created_at: Optional[datetime] = Field(default=None, description='创建时间')
    is_deleted: Optional[int] = Field(default=0, description='是否删除')

    @NotBlank(field_name='file_type', message='文件类型不能为空')
    def get_file_type(self):
        return self.file_type

    @NotBlank(field_name='original_name', message='源文件名称不能为空')
    def get_original_name(self):
        return self.original_name

    @NotBlank(field_name='storage_path', message='OSS存储路径不能为空')
    def get_storage_path(self):
        return self.storage_path

    def validate_fields(self):
        self.get_file_type()
        self.get_original_name()
        self.get_storage_path()




class FilesQueryModel(FilesModel):
    """
    数据库文件存储不分页查询模型
    """
    pass


@as_query
class FilesPageQueryModel(FilesQueryModel):
    """
    数据库文件存储分页查询模型
    """

    page_num: int = Field(default=1, description='当前页码')
    page_size: int = Field(default=10, description='每页记录数')


class FilesDeleteModel(BaseModel):
    """
    文件逻辑删除模型
    """
    model_config = ConfigDict(alias_generator=to_camel)

    file_id: int = Field(description='文件id')
    is_deleted: int = Field(default=1, description='是否删除')

    def validate_fields(self):
        if not self.file_id:
            raise ValueError('文件id不能为空')
        if self.is_deleted not in [0, 1]:
            raise ValueError('is_deleted字段只能是0或1')
