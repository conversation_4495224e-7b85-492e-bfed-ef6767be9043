import useUserStore from '@/store/modules/user'

/**
 * 字符权限校验
 * @param {Array} value 校验值
 * @returns {Boolean}
 */
export function checkPermi(value) {
  if (value && value instanceof Array && value.length > 0) {
    const permissions = useUserStore().permissions
    const permissionDatas = value
    const all_permission = "*:*:*";

    const hasPermission = permissions.some(permission => {
      return all_permission === permission || permissionDatas.includes(permission)
    })

    if (!hasPermission) {
      return false
    }
    return true
  } else {
    console.error(`need roles! Like checkPermi="['system:user:add','system:user:edit']"`)
    return false
  }
}

/**
 * 角色权限校验
 * @param {Array} value 校验值
 * @returns {Boolean}
 */
export function checkRole(value) {
  if (value && value instanceof Array && value.length > 0) {
    const roles = useUserStore().roles
    const permissionRoles = value
    const super_admin = "admin";

    const hasRole = roles.some(role => {
      return super_admin === role || permissionRoles.includes(role)
    })

    if (!hasRole) {
      return false
    }
    return true
  } else {
    console.error(`need roles! Like checkRole="['admin','editor']"`)
    return false
  }
}

/**
 * 检查用户是否为超级管理员
 * @returns {boolean}
 */
export function isSuperAdmin() {
  const roles = useUserStore().roles
  return roles.includes('super_admin') || roles.includes('admin')
}

/**
 * 检查用户是否为项目管理员
 * @returns {boolean}
 */
export function isProjectManager() {
  const roles = useUserStore().roles
  return roles.includes('project_manager')
}

/**
 * 检查用户是否为项目成员
 * @returns {boolean}
 */
export function isProjectMember() {
  const roles = useUserStore().roles
  return roles.includes('project_member')
}

/**
 * 检查用户是否为普通用户
 * @returns {boolean}
 */
export function isCommonUser() {
  const roles = useUserStore().roles
  return roles.includes('common_user')
}

/**
 * 检查用户是否可以管理项目
 * @returns {boolean}
 */
export function canManageProjects() {
  return isSuperAdmin() || isProjectManager()
}

/**
 * 检查用户是否可以访问数据库管理
 * @returns {boolean}
 */
export function canAccessDatabase() {
  return isSuperAdmin() || isProjectManager()
}

/**
 * 检查用户是否可以编辑任务
 * @param {Object} task - 任务对象
 * @returns {boolean}
 */
export function canEditTask(task) {
  // 超级管理员可以编辑所有任务
  if (isSuperAdmin()) {
    return true
  }

  // 项目管理员可以编辑所属项目的所有任务
  if (isProjectManager()) {
    // 这里需要检查用户是否为该项目的管理员
    // 暂时返回true，实际需要调用API检查
    return true
  }

  // 项目成员只能编辑自己创建的任务
  if (isProjectMember()) {
    const currentUserId = useUserStore().userId
    return task.assignedTo === currentUserId || task.createdBy === currentUserId
  }

  return false
}

/**
 * 检查用户是否可以删除任务
 * @param {Object} task - 任务对象
 * @returns {boolean}
 */
export function canDeleteTask(task) {
  // 超级管理员可以删除所有任务
  if (isSuperAdmin()) {
    return true
  }

  // 项目管理员可以删除所属项目的所有任务
  if (isProjectManager()) {
    // 这里需要检查用户是否为该项目的管理员
    // 暂时返回true，实际需要调用API检查
    return true
  }

  // 项目成员只能删除自己创建的任务
  if (isProjectMember()) {
    const currentUserId = useUserStore().userId
    return task.assignedTo === currentUserId || task.createdBy === currentUserId
  }

  return false
}