<div style="display: flex; flex-direction: column; align-items: center;">
  <img alt="logo" src="./sanhua.jpg" width="100" height="80">
  <h1 style="margin: 30px 0 10px; font-weight: bold; text-align: center;">RD-AI-Platform v0.0.1</h1>
  <h4 style="margin: 0 0 30px; text-align: center;">CV-AI产品设计平台</h4>
  <div>
    <img src="https://img.shields.io/badge/python-≥3.9-blue">
    <img src="https://img.shields.io/badge/MySQL-≥5.7-blue">
  </div>
</div>

## 平台简介

CV-AI产品设计平台，诣在实现从输入到设计仿真迭代输出的闭环，沉淀可复用的参数化数据库，缩短研发周期，提升研发效率，并输出一套可复用的AI平台搭建方法论。

## 建设规划

1.  基础数据收集：原始资料收集与分析、多维数据结构化、产品数据库搭建。
2.  产品开发流程梳理：人工设计的交付流程梳理、自动化流程梳理。
3.  数据清洗对齐：制定统一的数据标准、规范数据清洗流程。
4.  建模仿真自动化：参数化建模、流体域模型转化、仿真自动化。
5.  智能化寻优：智能化寻优方案定义、敏感参数定义、寻优策略确定。
6.  CV-AI平台搭建：自动化建模仿真任务调度、数据管理与可视化、接口标准化与扩展性设计。

## 项目开发及发布相关

### 开发

```bash
# 克隆项目
git clone http://10.10.201.46:8088/shrd/ai_platform.git

# 进入项目根目录
cd RD-AI-Platform
```

#### 前端
```bash
# 进入前端目录
cd ai-platform-frontend

# 安装依赖
npm install 或 yarn --registry=https://registry.npmmirror.com

# 建议不要直接使用 cnpm 安装依赖，会有各种诡异的 bug。可以通过如下操作解决 npm 下载速度慢的问题
npm install --registry=https://registry.npmmirror.com

# 启动服务
npm run dev 或 yarn dev
```

#### 后端
```bash
# 进入后端目录
cd ai-platform-backend

# 如果使用的是MySQL数据库，请执行以下命令安装项目依赖环境
pip3 install -r requirements.txt
# 如果使用的是PostgreSQL数据库，请执行以下命令安装项目依赖环境
pip3 install -r requirements-pg.txt

# 配置环境
在.env.dev文件中配置开发环境的数据库和redis

# 运行sql文件
1.新建数据库ai_platform(默认，可修改)
2.如果使用的是MySQL数据库，使用命令或数据库连接工具运行sql文件夹下的ai-platform.sql；如果使用的是PostgreSQL数据库，使用命令或数据库连接工具运行sql文件夹下的ai-platform-pg.sql

# 运行后端
python3 app.py --env=dev
```

#### 访问
```bash
# 默认账号密码
账号：admin
密码：admin123

# 浏览器访问
地址：http://localhost:80
```

### 发布

#### 前端
```bash
# 构建测试环境
npm run build:stage 或 yarn build:stage

# 构建生产环境
npm run build:prod 或 yarn build:prod
```

#### 后端
```bash
# 配置环境
在.env.prod文件中配置生产环境的数据库和redis

# 运行后端
python3 app.py --env=prod
```

### Git代码管理规范
#### 分支管理
##### 主要分支

- **main**  
  - 稳定版本代码分支  
  - 只能从 develop 分支合并而来  
  - 必须通过 PR (Pull Request) 进行代码审查后才能合并  
  - 禁止直接推送代码到该分支  

- **develop**  
  - 日常开发集成中心  
  - 所有功能开发和 bug 修复最终都应合并到此分支  
  - 禁止直接在该分支上进行开发  

##### 开发分支

- **feature/**  
  - 功能开发分支  
  - 必须从 develop 分支拉取  
  - 命名规范：`feature/<功能简述>`  
    - 示例：`feature/search_project_list`  
  - 开发完成后通过 PR 合并回 develop 分支  

- **bugfix/**  
  - 日常 bug 修复分支  
  - 必须从 develop 分支拉取  
  - 命名规范：`bugfix/<修复功能简述>`  
    - 示例：`bugfix/cannot_show_the_project_list`  
  - 修复完成后通过 PR 合并回 develop 分支  

#### 代码提交规范

1. **提交信息要求**  
   - 每次提交必须包含有意义的提交信息  
   - 简要描述本次提交完成的内容或修复的问题  

2. **提交信息格式**  
   - 使用英文描述（推荐）  
   - 简明扼要，不超过 50 个字符  
   - 使用祈使语气（如 "Add" 而不是 "Added"）  

3. **示例**  
   ```bash
   git commit -m "Implement project list search functionality"
   git commit -m "Fix project list display issue"
   git commit -m "Refactor user authentication module"



