from sqlalchemy import delete, select, update
from sqlalchemy.ext.asyncio import AsyncSession
from module_business.entity.do.project_types_rel_do import RdProjectTypesRel
from module_business.entity.vo.project_types_rel_vo import ProjectTypesRelModel, ProjectTypesRelPageQueryModel
from utils.page_util import PageUtil
from typing import List
from module_business.entity.do.projects_do import RdProjects


class ProjectTypesRelDao:
    """
    项目-类型关联关系模块数据库操作层
    """

    @classmethod
    async def get_project_types_rel_detail_by_id(cls, db: AsyncSession, project_id: int):
        """
        根据获取项目-类型关联关系详细信息

        :param db: orm对象
        :param project_id: 
        :return: 项目-类型关联关系信息对象
        """
        project_types_rel_info = (
            (
                await db.execute(
                    select(RdProjectTypesRel)
                    .where(
                        RdProjectTypesRel.project_id == project_id
                    )
                )
            )
            .scalars()
            .first()
        )

        return project_types_rel_info

    @classmethod
    async def get_project_types_rel_detail_by_info(cls, db: AsyncSession, project_types_rel: ProjectTypesRelModel):
        """
        根据项目-类型关联关系参数获取项目-类型关联关系信息

        :param db: orm对象
        :param project_types_rel: 项目-类型关联关系参数对象
        :return: 项目-类型关联关系信息对象
        """
        project_types_rel_info = (
            (
                await db.execute(
                    select(RdProjectTypesRel).where(
                    )
                )
            )
            .scalars()
            .first()
        )

        return project_types_rel_info

    @classmethod
    async def get_project_types_rel_list(cls, db: AsyncSession, query_object: ProjectTypesRelPageQueryModel, is_page: bool = False):
        """
        根据查询参数获取项目-类型关联关系列表信息

        :param db: orm对象
        :param query_object: 查询参数对象
        :param is_page: 是否开启分页
        :return: 项目-类型关联关系列表信息对象
        """
        query = (
            select(RdProjectTypesRel)
            .where(
                RdProjectTypesRel.assigned_at == query_object.assigned_at if query_object.assigned_at else True,
                RdProjectTypesRel.assigned_by == query_object.assigned_by if query_object.assigned_by else True,
            )
            .order_by(RdProjectTypesRel.project_id)
            .distinct()
        )
        project_types_rel_list = await PageUtil.paginate(db, query, query_object.page_num, query_object.page_size, is_page)

        return project_types_rel_list

    @classmethod
    async def get_project_types_rel_by_project_id_and_type_id(
        cls, db: AsyncSession, project_id: int, type_id: int
    ):
        """
        根据项目ID和类型ID查询关联关系
        :param db: orm对象
        :param project_id: 项目ID
        :param type_id: 类型ID
        :return: 关联关系对象，如果不存在则返回None
        """
        query = select(RdProjectTypesRel).where(
            RdProjectTypesRel.project_id == project_id, RdProjectTypesRel.type_id == type_id
        )
        result = await db.execute(query)
        return result.scalars().first()

    @classmethod
    async def add_project_types_rel_dao(cls, db: AsyncSession, project_types_rel: ProjectTypesRelModel):
        """
        新增项目-类型关联关系数据库操作

        :param db: orm对象
        :param project_types_rel: 项目-类型关联关系对象
        :return:
        """
        db_project_types_rel = RdProjectTypesRel(**project_types_rel.model_dump(exclude={}))
        db.add(db_project_types_rel)
        await db.flush()

        return db_project_types_rel

    @classmethod
    async def edit_project_types_rel_dao(cls, db: AsyncSession, project_types_rel: dict):
        """
        编辑项目-类型关联关系数据库操作

        :param db: orm对象
        :param project_types_rel: 需要更新的项目-类型关联关系字典
        :return:
        """
        await db.execute(update(RdProjectTypesRel), [project_types_rel])

    @classmethod
    async def delete_project_types_rel_dao(cls, db: AsyncSession, project_types_rel: ProjectTypesRelModel):
        """
        删除项目-类型关联关系数据库操作

        :param db: orm对象
        :param project_types_rel: 项目-类型关联关系对象
        :return:
        """
        await db.execute(delete(RdProjectTypesRel).where(RdProjectTypesRel.project_id.in_([project_types_rel.project_id])))

    @classmethod
    async def delete_project_type_rel_by_project_and_type(cls, db: AsyncSession, project_id: int, type_id: int):
        """
        根据项目ID和类型ID删除特定的项目-类型关联关系

        :param db: orm对象
        :param project_id: 项目ID
        :param type_id: 类型ID
        :return:
        """
        await db.execute(
            delete(RdProjectTypesRel).where(
                RdProjectTypesRel.project_id == project_id,
                RdProjectTypesRel.type_id == type_id
            )
        )

    @classmethod
    async def get_project_types_rel_list_by_project_id(cls, db: AsyncSession, project_id: int):
        """
        根据项目ID获取所有关联的类型信息

        :param db: orm对象
        :param project_id: 项目ID
        :return: 项目关联的所有类型信息列表
        """
        query = (
            select(RdProjectTypesRel)
            .where(RdProjectTypesRel.project_id == project_id)
            .order_by(RdProjectTypesRel.assigned_at.desc())
        )
        result = (await db.execute(query)).scalars().all()
        return result

    @classmethod
    async def get_projects_by_types(cls, db: AsyncSession, type_ids: List[int], page_num: int = 1, page_size: int = 12, project_name: str = '', is_page: bool = True):
        """
        根据类型ID列表获取项目列表，支持分页和项目名称模糊搜索
        :param db: orm对象
        :param type_ids: 类型ID列表
        :param page_num: 当前页码
        :param page_size: 每页数量
        :param project_name: 项目名称模糊搜索
        :param is_page: 是否分页
        :return: 项目列表
        """
        # 主查询：查找项目
        query = select(RdProjects).where(RdProjects.is_deleted == False)
        
        # 如果指定了类型ID，则添加类型筛选条件
        if type_ids:
            # 为每个类型ID创建一个子查询
            type_subqueries = []
            for type_id in type_ids:
                type_subquery = (
                    select(RdProjectTypesRel.project_id)
                    .where(
                        RdProjectTypesRel.type_id == type_id
                    )
                    .scalar_subquery()
                )
                type_subqueries.append(type_subquery)
            
            # 添加类型筛选条件
            for type_subquery in type_subqueries:
                query = query.where(RdProjects.project_id.in_(type_subquery))
        
        # 添加项目名称搜索条件
        if project_name:
            query = query.where(RdProjects.project_name.like(f"%{project_name}%"))
        
        query = query.order_by(RdProjects.created_at.desc())

        result = await PageUtil.paginate(db, query, page_num, page_size, is_page)
        
        # 为每个项目获取类型信息和负责人信息
        if is_page and hasattr(result, 'rows'):
            # 收集所有项目的owner_id
            owner_ids = set()
            for project in result.rows:
                owner_id = project.get('ownerId') or project.get('owner_id')
                if owner_id:
                    owner_ids.add(owner_id)
            
            # 批量获取用户信息
            users_info = {}
            if owner_ids:
                from module_admin.entity.do.user_do import SysUser
                
                user_query = select(SysUser).where(SysUser.user_id.in_(owner_ids))
                user_result = await db.execute(user_query)
                users = user_result.scalars().all()
                
                for user in users:
                    users_info[user.user_id] = user.nick_name or user.user_name
            
            for project in result.rows:
                # 获取项目的类型信息
                project_id = project.get('projectId') or project.get('project_id')
                if project_id:
                    type_query = (
                        select(RdProjectTypesRel.type_id)
                        .where(RdProjectTypesRel.project_id == project_id)
                    )
                    type_result = await db.execute(type_query)
                    type_ids = [row[0] for row in type_result.fetchall()]
                    project['typeIds'] = type_ids
                else:
                    project['typeIds'] = []
                
                # 添加负责人信息
                owner_id = project.get('ownerId') or project.get('owner_id')
                if owner_id and owner_id in users_info:
                    project['ownerName'] = users_info[owner_id]
                else:
                    project['ownerName'] = '未知用户'
        
        return result

