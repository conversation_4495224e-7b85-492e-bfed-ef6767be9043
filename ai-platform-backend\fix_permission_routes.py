#!/usr/bin/env python3
"""
修复权限路由问题的脚本
"""
import asyncio
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from config.get_db import get_db
from sqlalchemy.ext.asyncio import AsyncSession


async def fix_menu_permissions():
    """修复菜单权限配置"""
    print("🔧 开始修复菜单权限配置...")
    
    async for db in get_db():
        try:
            # 删除旧的菜单权限关联
            await db.execute("""
                DELETE FROM sys_role_menu WHERE menu_id IN (
                    SELECT menu_id FROM sys_menu WHERE path IN ('dashboard', 'tools', 'projects', 'tasks', 'knowledge-bases', 'system')
                );
            """)
            
            # 删除旧的菜单
            await db.execute("""
                DELETE FROM sys_menu WHERE path IN ('dashboard', 'tools', 'projects', 'tasks', 'knowledge-bases', 'system');
            """)
            
            # 插入新的菜单
            menu_sql = """
                INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
                VALUES 
                ('首页', 0, 1, 'dashboard', 'dashboard/index', 1, 0, 'C', '0', '0', 'dashboard:view', 'dashboard', 'admin', CURRENT_TIMESTAMP, '', NULL, '系统首页'),
                ('工具广场', 0, 2, 'tools', 'tools/index', 1, 0, 'C', '0', '0', 'tools:view', 'tool', 'admin', CURRENT_TIMESTAMP, '', NULL, '工具广场页面'),
                ('项目管理', 0, 3, 'projects', 'projects/board', 1, 0, 'C', '0', '0', 'projects:view', 'project', 'admin', CURRENT_TIMESTAMP, '', NULL, '项目管理页面'),
                ('数据库管理', 0, 4, 'knowledge-bases', 'knowledge-bases/index', 1, 0, 'C', '0', '0', 'database:view', 'database', 'admin', CURRENT_TIMESTAMP, '', NULL, '数据库管理页面'),
                ('系统管理', 0, 5, 'system', 'system/index', 1, 0, 'M', '0', '0', 'system:view', 'system', 'admin', CURRENT_TIMESTAMP, '', NULL, '系统管理目录');
            """
            await db.execute(menu_sql)
            
            # 插入角色菜单权限关联
            role_menu_sql = """
                -- 超级管理员：所有菜单权限
                INSERT INTO sys_role_menu (role_id, menu_id) 
                SELECT r.role_id, m.menu_id 
                FROM sys_role r, sys_menu m 
                WHERE r.role_key = 'super_admin' 
                AND m.path IN ('dashboard', 'tools', 'projects', 'knowledge-bases', 'system');

                -- 项目管理员：首页、工具广场、项目管理、数据库管理
                INSERT INTO sys_role_menu (role_id, menu_id) 
                SELECT r.role_id, m.menu_id 
                FROM sys_role r, sys_menu m 
                WHERE r.role_key = 'project_manager' 
                AND m.path IN ('dashboard', 'tools', 'projects', 'knowledge-bases');

                -- 项目成员：首页、工具广场、项目管理
                INSERT INTO sys_role_menu (role_id, menu_id) 
                SELECT r.role_id, m.menu_id 
                FROM sys_role r, sys_menu m 
                WHERE r.role_key = 'project_member' 
                AND m.path IN ('dashboard', 'tools', 'projects');

                -- 普通用户：首页、工具广场
                INSERT INTO sys_role_menu (role_id, menu_id) 
                SELECT r.role_id, m.menu_id 
                FROM sys_role r, sys_menu m 
                WHERE r.role_key = 'common_user' 
                AND m.path IN ('dashboard', 'tools');
            """
            await db.execute(role_menu_sql)
            
            await db.commit()
            print("✅ 菜单权限配置修复完成")
            
        except Exception as e:
            await db.rollback()
            print(f"❌ 修复菜单权限配置失败: {e}")
            raise
        finally:
            await db.close()
            break


def check_frontend_files():
    """检查前端文件是否正确"""
    print("\n🔍 检查前端文件...")
    
    frontend_root = project_root.parent / "ai-platform-frontend"
    
    # 检查任务管理主页面是否已删除
    task_index_file = frontend_root / "src/views/tasks/index.vue"
    if task_index_file.exists():
        print("❌ 任务管理主页面仍然存在，需要删除")
        return False
    else:
        print("✅ 任务管理主页面已正确删除")
    
    # 检查路由配置
    router_file = frontend_root / "src/router/index.js"
    if router_file.exists():
        with open(router_file, 'r', encoding='utf-8') as f:
            content = f.read()
            if "path: '/tasks'" in content and "hidden: true" in content:
                print("✅ 任务路由配置正确")
            else:
                print("❌ 任务路由配置需要修复")
                return False
    
    # 检查权限存储模块
    permission_file = frontend_root / "src/store/modules/permission.js"
    if permission_file.exists():
        with open(permission_file, 'r', encoding='utf-8') as f:
            content = f.read()
            if "staticRoutePaths" in content and "filteredSdata" in content:
                print("✅ 权限存储模块已修复路由冲突")
            else:
                print("❌ 权限存储模块需要修复路由冲突")
                return False
    
    return True


def print_deployment_instructions():
    """打印部署说明"""
    print("\n" + "=" * 60)
    print("📋 部署说明")
    print("=" * 60)
    
    print("\n1. 后端部署：")
    print("   - 重启后端服务以加载新的权限配置")
    print("   - 确保数据库连接正常")
    
    print("\n2. 前端部署：")
    print("   - 清除浏览器缓存")
    print("   - 重启前端开发服务器：npm run dev")
    
    print("\n3. 验证步骤：")
    print("   - 使用不同角色用户登录测试")
    print("   - 检查菜单显示是否正确")
    print("   - 验证项目和任务功能是否正常")
    
    print("\n4. 权限说明：")
    print("   - 超级管理员：所有功能权限")
    print("   - 项目管理员：项目管理、数据库管理")
    print("   - 项目成员：查看参与的项目，管理自己的任务")
    print("   - 普通用户：首页、工具广场")
    
    print("\n5. 任务管理说明：")
    print("   - 任务不再有独立菜单")
    print("   - 任务通过项目详情页面管理")
    print("   - 任务详情页面路径：/tasks/detail/:id")


async def main():
    """主函数"""
    print("🔧 权限路由修复脚本")
    print("=" * 60)
    
    try:
        # 修复数据库菜单权限
        await fix_menu_permissions()
        
        # 检查前端文件
        frontend_ok = check_frontend_files()
        
        if frontend_ok:
            print("\n🎉 所有修复完成！")
            print_deployment_instructions()
        else:
            print("\n⚠️  前端文件需要手动修复")
            
    except Exception as e:
        print(f"\n❌ 修复过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True


if __name__ == '__main__':
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
