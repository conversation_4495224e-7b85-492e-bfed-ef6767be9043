from sqlalchemy import TIM<PERSON><PERSON>MP, String, Column, SmallInteger, Integer
from config.database import Base


class RdSysTypes(Base):
    """
    通用类型管理表
    """

    __tablename__ = 'rd_sys_types'

    type_id = Column(Integer, primary_key=True, autoincrement=True, nullable=False, comment='')
    type_name = Column(String(50), nullable=False, comment='类型名称(英文标识)')
    display_name = Column(String(50), nullable=False, comment='显示名称')
    color_code = Column(String(20), nullable=True, comment='UI显示颜色')
    is_active = Column(SmallInteger, nullable=True, comment='是否激活')
    is_deleted = Column(SmallInteger, nullable=True, comment='')
    created_at = Column(TIMESTAMP, nullable=True, comment='')



