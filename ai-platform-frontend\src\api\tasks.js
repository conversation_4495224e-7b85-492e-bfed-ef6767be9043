import request from '@/utils/request'

// 获取项目任务列表
export function getTasksListByProject(projectId) {
  return request({
    url: `/business/tasks/list/${projectId}`,
    method: 'get',
    timeout: 15000  // 15秒超时
  })
}



// 获取所有任务列表
export function getTasksList(query) {
  return request({
    url: '/business/tasks/list',
    method: 'get',
    params: query
  })
}

// 获取任务详情
export function getTask(taskId) {
  return request({
    url: `/business/tasks/${taskId}`,
    method: 'get'
  })
}

// 获取任务最新进度
export function getTaskProgress(taskId) {
  return request({
    url: `/business/tasks/progress/${taskId}`,
    method: 'get'
  })
}

// 修复任务状态
export function fixTaskStatus(taskId) {
  return request({
    url: `/business/task-queue/fix-task-status/${taskId}`,
    method: 'post'
  })
}

// 获取任务详情看板数据
export function getTaskDetail(taskId) {
  return request({
    url: `/business/tasks/${taskId}`,
    method: 'get',
    params: { dashboard: true },
    timeout: 60000  // 增加超时时间到60秒，避免任务处理时间较长时的超时问题
  })
}

// 添加任务
export function addTask(data) {
  return request({
    url: '/business/tasks',
    method: 'post',
    data: data
  })
}

// 更新任务（用于编辑、删除、状态、进度等所有更新操作）
export function updateTask(data) {
  return request({
    url: '/business/tasks',
    method: 'put',
    data: data
  })
}

// 删除任务
export function deleteTask(taskId) {
  return request({
    url: `/business/tasks/${taskId}`,
    method: 'delete'
  })
}

// 批量删除任务
export function deleteTasks(taskIds) {
  return request({
    url: '/business/tasks/batch',
    method: 'delete',
    data: { taskIds }
  })
}

// 更新任务状态
export function updateTaskStatus(taskId, status) {
  return request({
    url: `/business/tasks/${taskId}/status`,
    method: 'put',
    data: { status }
  })
}

// 获取任务队列状态
export function getTaskQueueStatus() {
  return request({
    url: '/business/task-queue/status',
    method: 'get'
  })
}

// WebSocket连接类
export class TaskLogWebSocket {
  constructor(taskId, onMessage, onError, onClose) {
    this.taskId = taskId
    this.onMessage = onMessage
    this.onError = onError
    this.onClose = onClose
    this.ws = null
    this.heartbeatInterval = null
    this.reconnectAttempts = 0
    this.maxReconnectAttempts = 5
  }

  connect() {
    try {
      // 构建WebSocket URL
      let wsUrl
      if (import.meta.env.MODE === 'development') {
        // 开发环境：直接连接到后端服务器
        wsUrl = `ws://localhost:9099/business/task-logs/ws/${this.taskId}`
      } else {
        // 生产环境：使用当前页面的协议和主机
        const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:'
        const host = window.location.host
        wsUrl = `${protocol}//${host}/business/task-logs/ws/${this.taskId}`
      }
      
      console.log('尝试连接WebSocket:', wsUrl)
      this.ws = new WebSocket(wsUrl)
      
      this.ws.onopen = () => {
        console.log(`WebSocket连接已建立，任务ID: ${this.taskId}`)
        this.reconnectAttempts = 0
        this.startHeartbeat()
      }
      
      this.ws.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data)
          if (this.onMessage) {
            this.onMessage(data)
          }
        } catch (error) {
          console.error('解析WebSocket消息失败:', error)
        }
      }
      
      this.ws.onerror = (error) => {
        console.error('WebSocket错误:', error)
        if (this.onError) {
          this.onError(error)
        }
      }
      
      this.ws.onclose = (event) => {
        console.log('WebSocket连接已关闭:', event.code, event.reason)
        this.stopHeartbeat()
        if (this.onClose) {
          this.onClose(event)
        }
        
        // 自动重连
        if (this.reconnectAttempts < this.maxReconnectAttempts) {
          this.reconnectAttempts++
          console.log(`尝试重连 (${this.reconnectAttempts}/${this.maxReconnectAttempts})...`)
          setTimeout(() => {
            this.connect()
          }, 3000)
        }
      }
    } catch (error) {
      console.error('创建WebSocket连接失败:', error)
      if (this.onError) {
        this.onError(error)
      }
    }
  }

  startHeartbeat() {
    this.heartbeatInterval = setInterval(() => {
      if (this.ws && this.ws.readyState === WebSocket.OPEN) {
        this.ws.send(JSON.stringify({ type: 'ping' }))
      }
    }, 30000) // 每30秒发送一次心跳
  }

  stopHeartbeat() {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval)
      this.heartbeatInterval = null
    }
  }

  disconnect() {
    this.stopHeartbeat()
    if (this.ws) {
      this.ws.close()
      this.ws = null
    }
  }

  sendMessage(message) {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(message))
    }
  }
}

// 检查历史任务匹配（除了选型任务都需要调用）
export function checkHistoricalMatch(data) {
  const formData = new FormData()
  formData.append('project_id', data.project_id)
  formData.append('type_id', data.type_id)
  formData.append('tool_id', data.tool_id)
  formData.append('parameters', JSON.stringify(data.parameters))

  return request({
    url: '/business/tasks/check-historical-match',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 保持向后兼容的别名
export const checkHistoricalModelingMatch = checkHistoricalMatch