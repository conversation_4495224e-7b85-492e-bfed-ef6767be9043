<template>
  <div class="app-container">
    <!-- 项目信息头部 -->
    <div class="project-header">
      <div class="project-info">
        <h2 class="project-title">{{ getProjectTitle() + '项目看板' }}</h2>
        <div class="project-types">
          <el-tag
            v-for="typeId in getProjectTypeIds()"
            :key="typeId"
            :type="getTypeTagType(typeId)"
            size="small"
            class="type-tag"
          >
            {{ getTypeName(typeId) }}
          </el-tag>
        </div>
      </div>
      <div class="project-actions">
        <el-button type="warning" @click="fetchTasks" :loading="loading">
          <el-icon><Refresh /></el-icon>
          刷新列表
        </el-button>
        <el-button type="primary" @click="goToCreateTask">
          <el-icon><Plus /></el-icon>
          创建任务
        </el-button>
        <el-button
          type="success"
          @click="goToKnowledgeBase"
          :disabled="!knowledgeBase"
        >
          <el-icon><Collection /></el-icon>
          项目数据库
        </el-button>
      </div>
    </div>

    <!-- 筛选和搜索区域 -->
    <div class="filter-section">
      <!-- 任务类型筛选 -->
      <div class="filter-group">
        <div class="filter-label">任务类型：</div>
        <div class="filter-buttons">
          <el-button
            :type="selectedType === 'all' ? 'primary' : 'default'"
            size="small"
            @click="selectedType = 'all'"
          >全部</el-button>
          <el-button
            v-for="type in taskTypes"
            :key="type.typeId"
            :type="selectedType === type.typeId ? 'primary' : 'default'"
            size="small"
            @click="selectedType = type.typeId"
          >
            {{ type.displayName }}
          </el-button>
        </div>
      </div>

      <!-- 任务状态筛选 -->
      <div class="filter-group">
        <div class="filter-label">任务状态：</div>
        <div class="filter-buttons">
          <el-button
            :type="selectedStatus === 'all' ? 'primary' : 'default'"
            size="small"
            @click="selectedStatus = 'all'"
          >全部</el-button>
          <el-button
            v-for="status in taskStatusOptions"
            :key="status.value"
            :type="selectedStatus === status.value ? 'primary' : 'default'"
            size="small"
            @click="selectedStatus = status.value"
          >
            {{ status.label }}
          </el-button>
        </div>
      </div>

      <!-- 任务搜索 -->
      <div class="search-section">
        <el-input
          v-model="taskSearchQuery"
          placeholder="搜索任务名称"
          class="task-search-input"
          clearable
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
      </div>
    </div>

    <!-- 任务列表 -->
    <div class="tasks-table-container">
      <el-table :data="filteredTasks" style="width: 100%" v-loading="loading">
        <el-table-column prop="taskId" label="任务ID" width="100">
          <template #default="{ row }">
            <span class="task-id-display">#{{ getTaskField(row, 'taskId') }}</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="taskName" label="任务名称" min-width="200">
          <template #default="{ row }">
            <span class="task-name">{{ getTaskField(row, 'taskName') }}</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="typeId" label="任务类型" width="120">
          <template #default="{ row }">
            <el-tag
              :type="getTypeTagType(getTaskField(row, 'typeId'))"
              size="small"
            >
              {{ getTypeName(getTaskField(row, 'typeId')) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="toolName" label="工具选择" width="150">
          <template #default="{ row }">
            <span>{{ getTaskField(row, 'toolName') || '未选择工具' }}</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="creatorName" label="发起者" width="120">
          <template #default="{ row }">
            <span>{{ getTaskField(row, 'creatorName') || '未知用户' }}</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="toolQueueRequired" label="队列要求" width="100">
          <template #default="{ row }">
            <el-tag
              :type="getTaskField(row, 'toolQueueRequired') ? 'warning' : 'success'"
              size="small"
            >
              {{ getTaskField(row, 'toolQueueRequired') ? '需要排队' : '直接执行' }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="status" label="任务状态" width="200">
          <template #default="{ row }">
            <div style="display: flex; align-items: center; gap: 8px">
              <el-tag :type="getTaskStatusTagType(row.status)" size="small">
                {{ getTaskStatusText(row.status) }}
              </el-tag>

              <!-- 排队中状态显示队列位置 -->
              <template v-if="row.status === 'queued'">
                <el-tag v-if="row.queuePosition" type="info" size="small">
                  {{ typeof row.queuePosition === 'number' ? `第${row.queuePosition}位` : row.queuePosition }}
                </el-tag>
              </template>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="progress" label="任务进度" width="200">
          <template #default="{ row }">
            <el-progress
              :percentage="row.progress || 0"
              :status="getProgressStatus(row.status)"
              :stroke-width="8"
            />
          </template>
        </el-table-column>
        
        <el-table-column prop="startedAt" label="开始时间" width="160">
          <template #default="{ row }">
            <span>{{ formatDateTime(getTaskField(row, 'startedAt')) }}</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="estimatedCompletedAt" label="预计完成时间" width="160">
          <template #default="{ row }">
            <span>{{ formatDateTime(getTaskField(row, 'estimatedCompletedAt')) }}</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="completedAt" label="实际完成时间" width="160">
          <template #default="{ row }">
            <span>{{ formatDateTime(getTaskField(row, 'completedAt')) }}</span>
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button size="small" type="primary" @click="viewTask(row)">查看</el-button>
            <el-button
              size="small"
              type="success"
              @click="submitTask(row)"
              :disabled="row.status !== 'pending'"
              >提交</el-button
            >
            <el-button size="small" type="danger" @click="deleteTask(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 创建任务弹窗 -->
    <el-dialog v-model="showCreateTaskDialog" title="创建新任务" width="600px">
      <el-form :model="taskForm" label-width="100px">
        <el-form-item label="任务名称" required>
          <el-input v-model="taskForm.taskName" placeholder="请输入任务名称" />
        </el-form-item>
        <el-form-item label="任务类型" required>
          <el-select v-model="taskForm.typeId" placeholder="请选择任务类型" style="width: 100%">
            <el-option
              v-for="type in taskTypes"
              :key="type.typeId"
              :label="type.displayName"
              :value="type.typeId"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="任务描述">
          <el-input v-model="taskForm.description" type="textarea" rows="3" placeholder="请输入任务描述" />
        </el-form-item>
        <el-form-item label="预计完成时间">
          <el-date-picker
            v-model="taskForm.completedAt"
            type="datetime"
            placeholder="选择预计完成时间"
            style="width: 100%"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showCreateTaskDialog = false">取消</el-button>
        <el-button type="primary" @click="createTask">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { Search, Plus, Collection, Refresh } from '@element-plus/icons-vue'
import { getTypesList } from '@/api/types'
import { getProjectDetail } from '@/api/projects'
import { getTasksListByProject, addTask, updateTask } from '@/api/tasks'
import { submitTaskToQueue, getTaskQueuePosition } from '@/api/task-queue'
import { deleteProjectTypeRelByProjectAndType } from '@/api/project-types-rel'
import { getKnowledgeBasesList } from '@/api/knowledge-bases'
import { ElMessage, ElMessageBox } from 'element-plus'

const route = useRoute()
const router = useRouter()

// 响应式数据
const projectInfo = ref({})
const taskTypes = ref([])
const allTasks = ref([])
const loading = ref(false)
const showCreateTaskDialog = ref(false)
const selectedStatus = ref('all')
const selectedType = ref('all')
const knowledgeBase = ref(null)
const taskSearchQuery = ref('')

// 任务表单
const taskForm = ref({
  taskName: '',
  typeId: '',
  description: '',
  completedAt: null
})

// 任务状态选项
const taskStatusOptions = [
  { value: 'pending', label: '待提交' },
  { value: 'queued', label: '排队中' },
  { value: 'running', label: '进行中' },
  { value: 'completed', label: '已完成' },
  { value: 'failed', label: '失败' }
]

// 计算属性：筛选后的任务
const filteredTasks = computed(() => {
  let tasks = allTasks.value

  // 按任务类型筛选
  if (selectedType.value !== 'all') {
    tasks = tasks.filter(task => getTaskField(task, 'typeId') === selectedType.value)
  }

  // 按任务状态筛选
  if (selectedStatus.value !== 'all') {
    tasks = tasks.filter(task => task.status === selectedStatus.value)
  }

  // 按任务名称搜索
  if (taskSearchQuery.value) {
    const query = taskSearchQuery.value.toLowerCase()
    tasks = tasks.filter(task => 
      (getTaskField(task, 'taskName') || '').toLowerCase().includes(query)
    )
  }

  return tasks
})

// 工具方法
const getTaskField = (task, field) => {
  return task[field] || task[field.toLowerCase()] || task[field.replace(/([A-Z])/g, '_$1').toLowerCase()]
}

const getProjectTitle = () => {
  return projectInfo.value.projectName || projectInfo.value.project_name || '项目'
}

const getProjectTypeIds = () => {
  return projectInfo.value.typeIds || projectInfo.value.type_ids || []
}

const getTypeName = (typeId) => {
  const type = taskTypes.value.find(t => t.typeId === typeId)
  return type ? type.displayName : '未知类型'
}

const getTypeTagType = (typeId) => {
  const type = taskTypes.value.find(t => t.typeId === typeId)
  const colorMap = { success: 'success', warning: 'warning', danger: 'danger', info: 'info' }
  return colorMap[type?.colorCode] || 'info'
}

const getTaskStatusText = (status) => {
  const statusMap = { 
    pending: '待提交',
    queued: '排队中',
    running: '进行中', 
    completed: '已完成', 
    failed: '失败'
  }
  return statusMap[status] || status
}

const getTaskStatusTagType = (status) => {
  const map = { 
    pending: 'info',
    queued: 'warning',
    running: 'warning', 
    completed: 'success', 
    failed: 'danger'
  }
  return map[status] || 'info'
}

const getProgressStatus = (status) => {
  if (status === 'completed') return 'success'
  if (status === 'failed') return 'exception'
  return ''
}

const formatDateTime = (dateTime) => {
  if (!dateTime) return '-'
  return new Date(dateTime).toLocaleString('zh-CN')
}

// API 方法
const fetchProjectInfo = async () => {
  try {
    loading.value = true
    const res = await getProjectDetail(route.params.id)
    if (res.code === 200) {
      projectInfo.value = res.data
      
      // 根据项目ID查找关联的知识库
      try {
        const kbRes = await getKnowledgeBasesList({ projectId: route.params.id, pageNum: 1, pageSize: 1 })
        if (kbRes.code === 200 && kbRes.rows && kbRes.rows.length > 0) {
          knowledgeBase.value = kbRes.rows[0]
        } else {
          knowledgeBase.value = null
        }
      } catch (error) {
        knowledgeBase.value = null
      }
    }
  } catch (error) {
    ElMessage.error('获取项目信息失败')
  } finally {
    loading.value = false
  }
}

const fetchTypes = async () => {
  try {
    const res = await getTypesList({ pageNum: 1, pageSize: 100, isActive: 1 })
    if (res.code === 200) {
      taskTypes.value = res.rows
    }
  } catch (error) {
    ElMessage.error('获取类型列表失败')
  }
}

const fetchTasks = async (retryCount = 0, projectId = null) => {
  try {
    loading.value = true
    const currentProjectId = projectId || route.params.id
    console.log('开始获取任务列表，项目ID:', currentProjectId, '重试次数:', retryCount)

    // 安全检查：确保项目ID存在
    if (!currentProjectId) {
      console.error('项目ID为空，无法获取任务列表')
      ElMessage.error('项目ID不存在，无法获取任务列表')
      return
    }

    const res = await getTasksListByProject(currentProjectId)
    console.log('任务列表API响应:', res)

    if (res.code === 200) {
      allTasks.value = Array.isArray(res.data) ? res.data : []
      console.log('获取到的任务数量:', allTasks.value.length)

      // 只检查真正需要查询队列位置的任务
      const tasksToCheck = allTasks.value.filter(task => {
        // 如果任务状态是running，直接设置为执行中，不需要查询队列
        if (task.status === 'running') {
          task.queuePosition = '执行中'
          return false
        }
        // 如果任务状态是completed或failed，直接设置状态
        if (task.status === 'completed') {
          task.queuePosition = '已完成'
          return false
        }
        if (task.status === 'failed') {
          task.queuePosition = '已失败'
          return false
        }
        // 只有queued状态的任务才需要查询队列位置
        return task.status === 'queued'
      })
      console.log('需要查询队列位置的任务数量:', tasksToCheck.length)
      
      if (tasksToCheck.length > 0) {
        // 并发获取队列位置，设置超时
        const queuePromises = tasksToCheck.map(async (task) => {
          try {
            const queueRes = await Promise.race([
              getTaskQueuePosition(task.taskId),
              new Promise((_, reject) => 
                setTimeout(() => reject(new Error('timeout')), 3000) // 3秒超时
              )
            ])
            
            if (queueRes.code === 200) {
              if (queueRes.data.queue_position) {
                task.queuePosition = queueRes.data.queue_position
              } else {
                // 任务不在队列中，根据状态显示相应信息
                const status = queueRes.data.status
                if (status === 'running') {
                  task.queuePosition = '执行中'
                  // 更新任务状态
                  task.status = 'running'
                } else if (status === 'processing') {
                  task.queuePosition = '正在处理...'
                } else if (status === 'pending_restart') {
                  task.queuePosition = '状态异常，正在修复...'
                  // 状态异常时，3秒后重新查询
                  setTimeout(() => {
                    console.log(`🔄 任务 ${task.taskId} 状态异常，3秒后重新查询`)
                    updateTaskQueuePosition(task)
                  }, 3000)
                } else if (status === 'completed') {
                  task.queuePosition = '已完成'
                  task.status = 'completed'
                } else if (status === 'failed') {
                  task.queuePosition = '已失败'
                  task.status = 'failed'
                } else {
                  task.queuePosition = '状态更新中'
                }
              }
            }
          } catch (error) {
            // 获取队列位置失败不应阻碍主流程，设置默认值
            console.warn(`获取任务 ${task.taskId} 队列位置失败:`, error)
            task.queuePosition = '获取中...'
          }
        })
        
        // 等待所有队列位置查询完成，但不超过5秒
        try {
          await Promise.race([
            Promise.allSettled(queuePromises),
            new Promise((_, reject) =>
              setTimeout(() => reject(new Error('batch timeout')), 5000)
            )
          ])

          // 检查是否有任务状态发生了变化或异常，如果有则再次刷新
          const hasStatusChange = tasksToCheck.some(task =>
            task.status === 'running' ||
            task.queuePosition === '执行中' ||
            task.queuePosition === '状态异常，正在修复...'
          )

          if (hasStatusChange) {
            console.log('🔄 检测到任务状态变化，2秒后再次刷新')
            const currentProjectId = route.params.id
            setTimeout(() => {
              fetchTasks(0, currentProjectId)
            }, 2000)
          }
        } catch (error) {
          console.warn('获取队列位置批量操作超时')
        }
      }
      
      // 处理工具队列要求信息
      allTasks.value.forEach(task => {
        if (task.toolQueueRequired !== undefined) {
          task.toolQueueRequired = Boolean(task.toolQueueRequired)
        } else {
          task.toolQueueRequired = true  // 默认需要队列
        }
      })

      // 注意：进度更新已移至任务详情页的WebSocket实时更新
    } else {
      console.error('任务列表API返回错误:', res)
      ElMessage.error(res.msg || '获取任务列表失败')
    }
  } catch (error) {
    console.error('获取任务列表异常:', error)
    
    // 根据错误类型显示不同的提示
    if (error.message && error.message.includes('timeout')) {
      if (retryCount < 2) {
        console.log(`获取任务列表超时，${retryCount + 1}秒后重试`)
        const currentProjectId = projectId || route.params.id
        setTimeout(() => fetchTasks(retryCount + 1, currentProjectId), (retryCount + 1) * 1000)
        return
      } else {
        ElMessage.error('获取任务列表超时，请稍后重试')
      }
    } else if (error.code === 'ECONNABORTED') {
      ElMessage.error('网络连接超时，请检查网络后重试')
    } else if (error.response) {
      // 服务器返回了错误状态码
      console.error('服务器错误:', error.response.status, error.response.data)
      ElMessage.error(`服务器错误: ${error.response.status}`)
    } else if (error.request) {
      // 请求已发出但没有收到响应
      console.error('网络请求失败:', error.request)
      ElMessage.error('网络请求失败，请检查网络连接')
    } else {
      // 其他错误
      console.error('未知错误:', error.message)
      ElMessage.error('获取任务列表失败，请稍后重试')
    }
  } finally {
    loading.value = false
  }
}

// 注意：进度更新已移至任务详情页的WebSocket实时更新
// 项目详情页只显示任务状态，不进行实时进度更新

// 任务操作方法
const viewTask = (task) => {
  const taskId = task.taskId || task.task_id
  if (taskId) {
    router.push(`/tasks/detail/${taskId}`)
  } else {
    ElMessage.warning('任务ID不存在')
  }
}

const submitTask = async (task) => {
  try {
    console.log('开始提交任务:', task.taskId)
    
    // 检查工具队列要求
    const requiresQueue = task.toolQueueRequired !== false
    
    if (requiresQueue) {
      ElMessage.info('该工具需要排队执行，任务将进入队列')
    } else {
      ElMessage.info('该工具支持直接执行，任务将立即开始')
    }
    
    // 提交任务（根据工具配置决定是否排队）
    const res = await submitTaskToQueue(task.taskId)
    console.log('任务提交响应:', res)
    
    if (res.code === 200) {
      const data = res.data
      
      if (data.status === 'running') {
        // 任务直接开始执行
        ElMessage.success('任务已开始执行')
      } else if (data.status === 'queued') {
        // 任务进入队列
        const position = data.queue_position
        ElMessage.success(`任务已提交到队列，当前位置：第${position}位`)
      } else {
        ElMessage.success('任务提交成功')
      }
      
      // 任务提交成功后，立即更新本地状态
      console.log('任务提交成功，更新本地状态')
      const taskIndex = allTasks.value.findIndex(t => t.taskId === task.taskId)
      if (taskIndex !== -1) {
        allTasks.value[taskIndex].status = data.status || 'running'
        if (data.queue_position) {
          allTasks.value[taskIndex].queuePosition = data.queue_position
        }
      }
      
      // 延迟刷新任务列表获取最新状态
      const currentProjectId = route.params.id // 捕获当前项目ID
      setTimeout(async () => {
        try {
          console.log('3秒后刷新任务列表')
          await fetchTasks(0, currentProjectId)
        } catch (error) {
          console.warn('刷新任务列表失败:', error.message)
          // 刷新失败不影响用户操作，继续使用当前状态
        }
      }, 3000)
    } else {
      console.error('任务提交失败:', res)
      ElMessage.error(res.msg || '提交任务失败')
    }
  } catch (error) {
    console.error('提交任务异常:', error)
    
    // 统一错误处理
    const errorMsg = error.response?.data?.msg || error.message || '提交任务失败，请稍后重试'
    ElMessage.error(errorMsg)
  }
}

const deleteTask = (task) => {
  ElMessageBox.confirm('确定要删除该任务吗？', '提示', { type: 'warning' })
    .then(async () => {
      try {
        await updateTask({ ...task, isDeleted: 1 })
        
        // 检查是否需要删除项目类型关联
        const taskTypeId = getTaskField(task, 'typeId')
        if (taskTypeId) {
          const remainingTasks = allTasks.value.filter(t => 
            getTaskField(t, 'typeId') === taskTypeId && 
            t.taskId !== task.taskId && 
            t.isDeleted !== 1
          )
          
          if (remainingTasks.length === 0) {
            try {
              await deleteProjectTypeRelByProjectAndType(parseInt(route.params.id), taskTypeId)
            } catch (error) {
              // 删除关联失败不应阻碍主流程
            }
          }
        }
        
        ElMessage.success('删除成功')
        fetchTasks()
      } catch (error) {
        ElMessage.error('删除失败')
      }
    })
    .catch(() => {
      // 用户取消删除
    })
}

const createTask = async () => {
  if (!taskForm.value.taskName || !taskForm.value.typeId) {
    ElMessage.warning('请填写必填项')
    return
  }
  
  try {
    await addTask({
      ...taskForm.value,
      projectId: route.params.id,
      status: 'pending'
    })
    ElMessage.success('创建成功')
    showCreateTaskDialog.value = false
    taskForm.value = { taskName: '', typeId: '', description: '', completedAt: null }
    fetchTasks()
  } catch (error) {
    ElMessage.error('创建失败')
  }
}

const goToCreateTask = () => {
  router.push({ name: 'createTask', params: { projectId: route.params.id } })
}

const goToKnowledgeBase = () => {
  if (knowledgeBase.value && (knowledgeBase.value.kbId || knowledgeBase.value.kb_id)) {
    const kbId = knowledgeBase.value.kbId || knowledgeBase.value.kb_id
    router.push(`/knowledge-bases/detail/${kbId}`)
  } else {
    ElMessage.warning('该项目暂未关联数据库')
  }
}

onMounted(() => {
  fetchProjectInfo()
  fetchTypes()
  fetchTasks()
})

// 进度更新已移至任务详情页的WebSocket实时更新
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.project-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e4e7ed;
}

.project-info {
  flex: 1;
}

.project-title {
  margin: 0 0 12px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.project-types {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.type-tag {
  margin-right: 4px;
}

.project-actions {
  display: flex;
  gap: 12px;
}

.filter-section {
  margin-bottom: 24px;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 8px;
}

.filter-group {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.filter-group:last-child {
  margin-bottom: 0;
}

.filter-label {
  width: 80px;
  font-weight: 500;
  color: #606266;
}

.filter-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.search-section {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #e4e7ed;
}

.task-search-input {
  width: 300px;
}

.tasks-table-container {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.task-name {
  font-weight: 500;
  color: #303133;
}

.task-id-display {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 600;
  display: inline-block;
  text-align: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  min-width: 50px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .project-header {
    flex-direction: column;
    gap: 16px;
  }
  
  .project-actions {
    width: 100%;
    justify-content: flex-start;
  }
  
  .filter-group {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .filter-label {
    width: auto;
  }
  
  .task-search-input {
    width: 100%;
  }
}
</style> 