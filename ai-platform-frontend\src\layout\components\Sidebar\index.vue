<template>
  <div :class="{ 'has-logo': showLogo }" class="sidebar-container">
    <logo v-if="showLogo" :collapse="isCollapse" />
    <el-scrollbar wrap-class="scrollbar-wrapper">
      <el-menu
        :default-active="activeMenu"
        :collapse="isCollapse"
        :background-color="getMenuBackground"
        :text-color="getMenuTextColor"
        :unique-opened="true"
        :active-text-color="theme"
        :collapse-transition="false"
        mode="vertical"
        :class="sideTheme"
      >
        <sidebar-item
          v-for="(route, index) in sidebarRouters"
          :key="route.path + index"
          :item="route"
          :base-path="route.path"
        />
      </el-menu>
      <!-- 底部操作按钮 -->
      <div class="sidebar-bottom-actions">
        <el-link class="sidebar-action-link" @click="$router.push('/user/profile')">
          <el-icon style="margin-right:8px;"><User /></el-icon>个人中心
        </el-link>
        <el-link class="sidebar-action-link" type="danger" @click="handleLogout">
          <el-icon style="margin-right:8px;"><SwitchButton /></el-icon>退出登录
        </el-link>
      </div>
    </el-scrollbar>
  </div>
</template>

<script setup>
import Logo from './Logo'
import SidebarItem from './SidebarItem'
import variables from '@/assets/styles/variables.module.scss'
import useAppStore from '@/store/modules/app'
import useSettingsStore from '@/store/modules/settings'
import usePermissionStore from '@/store/modules/permission'
import useUserStore from '@/store/modules/user'
import { User, SwitchButton } from '@element-plus/icons-vue'

const route = useRoute();
const appStore = useAppStore()
const settingsStore = useSettingsStore()
const permissionStore = usePermissionStore()
const userStore = useUserStore()

const sidebarRouters = computed(() => permissionStore.sidebarRouters);
const showLogo = computed(() => settingsStore.sidebarLogo);
const sideTheme = computed(() => settingsStore.sideTheme);
const theme = computed(() => settingsStore.theme);
const isCollapse = computed(() => !appStore.sidebar.opened);

// 获取菜单背景色
const getMenuBackground = computed(() => {
  if (settingsStore.isDark) {
    return 'var(--sidebar-bg)';
  }
  return sideTheme.value === 'theme-dark' ? variables.menuBg : variables.menuLightBg;
});

// 获取菜单文字颜色
const getMenuTextColor = computed(() => {
  if (settingsStore.isDark) {
    return 'var(--sidebar-text)';
  }
  return sideTheme.value === 'theme-dark' ? variables.menuText : variables.menuLightText;
});

const activeMenu = computed(() => {
  const { meta, path } = route;
  if (meta.activeMenu) {
    return meta.activeMenu;
  }
  return path;
});

function handleLogout() {
  userStore.logOut().then(() => {
    window.location.href = '/login';
  });
}
</script>

<style lang="scss" scoped>
.sidebar-container {
  background: linear-gradient(180deg, #15395a 0%, #1e4a6d 100%) !important;
  color: #fff;
  backdrop-filter: blur(0);
  border-right: none;
  box-shadow: 2px 0 20px rgba(0, 0, 0, 0.08);
  display: flex;
  flex-direction: column;
  height: 100vh;
  position: relative;

  .scrollbar-wrapper {
    background: transparent;
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: 100%;
  }

  .el-menu {
    border: none;
    height: auto;
    width: 100% !important;
    background: transparent;
    color: #fff;
    .el-menu-item, .el-sub-menu__title {
      margin: 4px 8px;
      border-radius: 8px;
      transition: all 0.3s ease;
      color: #fff !important;
      font-weight: 500;
      &:hover {
        background: rgba(37, 99, 235, 0.15) !important;
        color: #fff !important;
        transform: translateX(4px);
      }
    }
    .el-menu-item {
      &.is-active {
        color: #fff !important;
        background: #2563eb !important;
        box-shadow: 0 2px 8px rgba(37, 99, 235, 0.15);
      }
    }
    .el-sub-menu__title {
      color: #fff !important;
    }
    .el-sub-menu {
      .el-menu {
        background: rgba(21, 57, 90, 0.8);
        border-radius: 8px;
        margin: 4px 8px;
      }
    }
  }
  .sidebar-bottom-actions {
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
    padding: 24px 12px 16px 12px;
    background: transparent;
    .sidebar-action-link {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: flex-start;
      text-align: left;
      font-weight: 500;
      font-size: 15px;
      line-height: 48px;
      height: 48px;
      letter-spacing: 1px;
      color: #fff;
      background: none;
      border: none;
      margin: 0 auto;
      padding: 0 20px;
      box-sizing: border-box;
      transition: color 0.2s;
      overflow: hidden;
      white-space: nowrap;
      &:hover {
        color: #2563eb;
        background: none;
      }
      .el-icon {
        margin-right: 16px;
        font-size: 18px;
      }
    }
  }
}
</style>
