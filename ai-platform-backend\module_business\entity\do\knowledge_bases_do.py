from sqlalchemy import J<PERSON><PERSON>, Column, Text, TIMESTAMP, BigInteger, String, SmallInteger
from config.database import Base


class RdKnowledgeBases(Base):
    """
    数据库管理表
    """

    __tablename__ = 'rd_knowledge_bases'

    kb_id = Column(BigInteger, primary_key=True, autoincrement=True, nullable=False, comment='数据库id')
    project_id = Column(BigInteger, nullable=True, comment='NULL表示通用数据库')
    kb_name = Column(String(255), nullable=False, comment='数据库名称')
    description = Column(Text, nullable=True, comment='数据库描述')
    owner_id = Column(BigInteger, nullable=False, comment='数据库所有者')
    vector_store_id = Column(String(100), nullable=True, comment='向量库标识')
    is_public = Column(SmallInteger, nullable=True, comment='是否公开')
    created_at = Column(TIMESTAMP, nullable=True, comment='创建时间')
    updated_at = Column(TIMESTAMP, nullable=True, comment='更新时间')
    is_deleted = Column(SmallInteger, nullable=True, comment='是否删除')
    file_type_stats = Column(JSON, nullable=True, comment='JSON示例: model=5, report=3, log=12')



