from datetime import datetime
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List
from exceptions.exception import ServiceException
from module_admin.entity.vo.common_vo import CrudResponseModel
from module_business.entity.vo.project_members_vo import (
    ProjectMembersModel,
    ProjectMembersPageQueryModel,
    AddProjectMemberModel,
    BatchAddProjectMembersModel,
    UpdateProjectMemberRoleModel,
)
from module_business.dao.project_members_dao import ProjectMembersDao
from module_business.dao.projects_dao import ProjectsDao
from module_business.entity.vo.projects_vo import ProjectsModel
from module_admin.dao.user_dao import UserDao
from module_admin.entity.vo.user_vo import UserPageQueryModel
from utils.page_util import PageResponseModel


class ProjectMembersService:
    """
    项目成员管理模块服务层
    """

    @classmethod
    async def get_project_members_list_services(
        cls, query_db: AsyncSession, query_object: ProjectMembersPageQueryModel, is_page: bool = False
    ):
        """
        获取项目成员列表信息service

        :param query_db: orm对象
        :param query_object: 查询参数对象
        :param is_page: 是否开启分页
        :return: 项目成员列表信息对象
        """
        project_members_list_result = await ProjectMembersDao.get_project_members_list(query_db, query_object, is_page)

        return project_members_list_result

    @classmethod
    async def add_project_member_services(cls, query_db: AsyncSession, add_member: AddProjectMemberModel, current_user_id: int):
        """
        新增项目成员service

        :param query_db: orm对象
        :param add_member: 新增项目成员对象
        :param current_user_id: 当前用户ID
        :return: 新增项目成员校验结果
        """
        # 检查项目是否存在
        project = await ProjectsDao.get_projects_detail_by_info(
            query_db, ProjectsModel(project_id=add_member.project_id)
        )
        if not project:
            raise ServiceException(message='项目不存在')

        # 检查用户是否存在
        user = await UserDao.get_user_detail_by_info(
            query_db, UserPageQueryModel(userId=add_member.user_id)
        )
        if not user:
            raise ServiceException(message='用户不存在')

        # 检查是否已经是项目成员
        existing_member = await ProjectMembersDao.get_project_members_detail_by_info(
            query_db, ProjectMembersModel(project_id=add_member.project_id, user_id=add_member.user_id)
        )
        if existing_member:
            raise ServiceException(message='用户已经是该项目的成员')

        try:
            # 添加项目成员
            project_member = ProjectMembersModel(
                project_id=add_member.project_id,
                user_id=add_member.user_id,
                role_type=add_member.role_type,
                assigned_at=datetime.now(),
                assigned_by=current_user_id,
                is_deleted=0,
            )
            await ProjectMembersDao.add_project_members_dao(query_db, project_member)
            await query_db.commit()
            return CrudResponseModel(is_success=True, message='添加成功')
        except Exception as e:
            await query_db.rollback()
            raise e

    @classmethod
    async def batch_add_project_members_services(
        cls, query_db: AsyncSession, batch_add: BatchAddProjectMembersModel, current_user_id: int
    ):
        """
        批量添加项目成员service

        :param query_db: orm对象
        :param batch_add: 批量添加项目成员对象
        :param current_user_id: 当前用户ID
        :return: 批量添加项目成员校验结果
        """
        # 检查项目是否存在
        project = await ProjectsDao.get_projects_detail_by_info(
            query_db, ProjectsModel(project_id=batch_add.project_id)
        )
        if not project:
            raise ServiceException(message='项目不存在')

        success_count = 0
        failed_users = []

        try:
            for user_id in batch_add.user_ids:
                # 检查用户是否存在
                user = await UserDao.get_user_detail_by_info(
                    query_db, UserPageQueryModel(userId=user_id)
                )
                if not user:
                    failed_users.append(f'用户ID {user_id} 不存在')
                    continue

                # 检查是否已经是项目成员
                existing_member = await ProjectMembersDao.get_project_members_detail_by_info(
                    query_db, ProjectMembersModel(project_id=batch_add.project_id, user_id=user_id)
                )
                if existing_member:
                    failed_users.append(f'用户ID {user_id} 已经是该项目的成员')
                    continue

                # 添加项目成员
                project_member = ProjectMembersModel(
                    project_id=batch_add.project_id,
                    user_id=user_id,
                    role_type=batch_add.role_type,
                    assigned_at=datetime.now(),
                    assigned_by=current_user_id,
                    is_deleted=0,
                )
                await ProjectMembersDao.add_project_members_dao(query_db, project_member)
                success_count += 1

            await query_db.commit()
            
            message = f'成功添加 {success_count} 个成员'
            if failed_users:
                message += f'，失败 {len(failed_users)} 个：{"; ".join(failed_users)}'
                
            return CrudResponseModel(is_success=True, message=message)
        except Exception as e:
            await query_db.rollback()
            raise e

    @classmethod
    async def update_project_member_role_services(
        cls, query_db: AsyncSession, update_role: UpdateProjectMemberRoleModel
    ):
        """
        更新项目成员角色service

        :param query_db: orm对象
        :param update_role: 更新项目成员角色对象
        :return: 更新项目成员角色校验结果
        """
        # 检查项目成员是否存在
        existing_member = await ProjectMembersDao.get_project_members_detail_by_info(
            query_db, ProjectMembersModel(project_id=update_role.project_id, user_id=update_role.user_id)
        )
        if not existing_member:
            raise ServiceException(message='项目成员不存在')

        try:
            # 更新角色
            project_member = ProjectMembersModel(
                project_id=update_role.project_id,
                user_id=update_role.user_id,
                role_type=update_role.role_type,
            )
            await ProjectMembersDao.edit_project_members_dao(query_db, project_member)
            await query_db.commit()
            return CrudResponseModel(is_success=True, message='更新成功')
        except Exception as e:
            await query_db.rollback()
            raise e

    @classmethod
    async def remove_project_member_services(
        cls, query_db: AsyncSession, project_id: int, user_id: int
    ):
        """
        移除项目成员service

        :param query_db: orm对象
        :param project_id: 项目ID
        :param user_id: 用户ID
        :return: 移除项目成员校验结果
        """
        # 检查项目成员是否存在
        existing_member = await ProjectMembersDao.get_project_members_detail_by_info(
            query_db, ProjectMembersModel(project_id=project_id, user_id=user_id)
        )
        if not existing_member:
            raise ServiceException(message='项目成员不存在')

        try:
            # 删除项目成员
            project_member = ProjectMembersModel(project_id=project_id, user_id=user_id)
            await ProjectMembersDao.delete_project_members_dao(query_db, project_member)
            await query_db.commit()
            return CrudResponseModel(is_success=True, message='移除成功')
        except Exception as e:
            await query_db.rollback()
            raise e

    @classmethod
    async def get_user_projects_services(cls, query_db: AsyncSession, user_id: int):
        """
        获取用户所属的项目列表service

        :param query_db: orm对象
        :param user_id: 用户ID
        :return: 用户所属的项目列表
        """
        projects = await ProjectMembersDao.get_user_projects(query_db, user_id)
        return projects

    @classmethod
    async def get_project_members_services(cls, query_db: AsyncSession, project_id: int):
        """
        获取项目成员列表service

        :param query_db: orm对象
        :param project_id: 项目ID
        :return: 项目成员列表
        """
        members = await ProjectMembersDao.get_project_members_by_project_id(query_db, project_id)
        return members

    @classmethod
    async def check_user_project_permission_services(
        cls, query_db: AsyncSession, user_id: int, project_id: int, required_role: str = None
    ):
        """
        检查用户对项目的权限service

        :param query_db: orm对象
        :param user_id: 用户ID
        :param project_id: 项目ID
        :param required_role: 需要的角色类型
        :return: 权限检查结果
        """
        has_permission = await ProjectMembersDao.check_user_project_permission(
            query_db, user_id, project_id, required_role
        )
        return has_permission
