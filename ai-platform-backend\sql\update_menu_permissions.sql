-- ----------------------------
-- 更新菜单权限脚本
-- ----------------------------

-- 删除旧的菜单数据（如果存在）
DELETE FROM sys_role_menu WHERE menu_id IN (
    SELECT menu_id FROM sys_menu WHERE path IN ('dashboard', 'tools', 'projects', 'tasks', 'knowledge-bases', 'system')
);

DELETE FROM sys_menu WHERE path IN ('dashboard', 'tools', 'projects', 'tasks', 'knowledge-bases', 'system');

-- 重新插入菜单数据
-- 首页菜单（所有角色都可以访问）
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('首页', 0, 1, 'dashboard', 'dashboard/index', 1, 0, 'C', '0', '0', 'dashboard:view', 'dashboard', 'admin', CURRENT_TIMESTAMP, '', NULL, '系统首页');

-- 工具广场菜单（所有角色都可以访问）
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('工具广场', 0, 2, 'tools', 'tools/index', 1, 0, 'C', '0', '0', 'tools:view', 'tool', 'admin', CURRENT_TIMESTAMP, '', NULL, '工具广场页面');

-- 项目管理菜单（项目管理员和超级管理员可以访问）
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('项目管理', 0, 3, 'projects', 'projects/board', 1, 0, 'C', '0', '0', 'projects:view', 'project', 'admin', CURRENT_TIMESTAMP, '', NULL, '项目管理页面');

-- 任务管理菜单（项目成员、项目管理员和超级管理员可以访问）
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('任务管理', 0, 4, 'tasks', 'tasks/index', 1, 0, 'C', '0', '0', 'tasks:view', 'task', 'admin', CURRENT_TIMESTAMP, '', NULL, '任务管理页面');

-- 数据库管理菜单（只有超级管理员和项目管理员可以访问）
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('数据库管理', 0, 5, 'knowledge-bases', 'knowledge-bases/index', 1, 0, 'C', '0', '0', 'database:view', 'database', 'admin', CURRENT_TIMESTAMP, '', NULL, '数据库管理页面');

-- 系统管理菜单（只有超级管理员可以访问）
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('系统管理', 0, 6, 'system', 'system/index', 1, 0, 'M', '0', '0', 'system:view', 'system', 'admin', CURRENT_TIMESTAMP, '', NULL, '系统管理目录');

-- ----------------------------
-- 角色菜单关联关系
-- ----------------------------

-- 获取角色ID和菜单ID
-- 超级管理员角色ID
-- 项目管理员角色ID
-- 项目成员角色ID
-- 普通用户角色ID

-- 菜单ID
-- 超级管理员：所有菜单权限
INSERT INTO sys_role_menu (role_id, menu_id) 
SELECT r.role_id, m.menu_id 
FROM sys_role r, sys_menu m 
WHERE r.role_key = 'super_admin' 
AND m.path IN ('dashboard', 'tools', 'projects', 'tasks', 'knowledge-bases', 'system');

-- 项目管理员：首页、工具广场、项目管理、任务管理、数据库管理
INSERT INTO sys_role_menu (role_id, menu_id) 
SELECT r.role_id, m.menu_id 
FROM sys_role r, sys_menu m 
WHERE r.role_key = 'project_manager' 
AND m.path IN ('dashboard', 'tools', 'projects', 'tasks', 'knowledge-bases');

-- 项目成员：首页、工具广场、任务管理
INSERT INTO sys_role_menu (role_id, menu_id) 
SELECT r.role_id, m.menu_id 
FROM sys_role r, sys_menu m 
WHERE r.role_key = 'project_member' 
AND m.path IN ('dashboard', 'tools', 'tasks');

-- 普通用户：首页、工具广场
INSERT INTO sys_role_menu (role_id, menu_id) 
SELECT r.role_id, m.menu_id 
FROM sys_role r, sys_menu m 
WHERE r.role_key = 'common_user' 
AND m.path IN ('dashboard', 'tools');
