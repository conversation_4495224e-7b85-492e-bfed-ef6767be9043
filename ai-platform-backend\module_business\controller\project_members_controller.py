from fastapi import APIRouter, Depends
from pydantic_validation_decorator import <PERSON><PERSON>te<PERSON>ields
from sqlalchemy.ext.asyncio import AsyncSession
from config.enums import BusinessType
from config.get_db import get_db
from module_admin.annotation.log_annotation import Log
from module_admin.entity.vo.user_vo import CurrentUserModel
from module_admin.service.login_service import LoginService
from module_business.service.project_members_service import ProjectMembersService
from module_business.entity.vo.project_members_vo import (
    ProjectMembersPageQueryModel,
    AddProjectMemberModel,
    BatchAddProjectMembersModel,
    UpdateProjectMemberRoleModel,
)
from utils.log_util import logger
from utils.page_util import PageResponseModel
from utils.response_util import ResponseUtil


project_membersController = APIRouter(prefix='/business/projectMembers', dependencies=[Depends(LoginService.get_current_user)])


@project_membersController.get(
    '/list', response_model=PageResponseModel  # dependencies=[Depends(CheckUserInterfaceAuth('business:projectMembers:list'))]
)
async def get_business_project_members_list(
    project_members_page_query: ProjectMembersPageQueryModel = Depends(),
    query_db: AsyncSession = Depends(get_db),
):
    # 获取分页数据
    project_members_page_query_result = await ProjectMembersService.get_project_members_list_services(
        query_db, project_members_page_query, is_page=True
    )
    logger.info('获取成功')

    return ResponseUtil.success(model_content=project_members_page_query_result)


@project_membersController.post('/add')  # dependencies=[Depends(CheckUserInterfaceAuth('business:projectMembers:add'))]
@ValidateFields(validate_model='add_project_member')
@Log(title='项目成员管理', business_type=BusinessType.INSERT)
async def add_project_member(
    add_member: AddProjectMemberModel,
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    add_result = await ProjectMembersService.add_project_member_services(
        query_db, add_member, current_user.user.user_id
    )
    logger.info(add_result.message)

    return ResponseUtil.success(msg=add_result.message)


@project_membersController.post('/batchAdd')  # dependencies=[Depends(CheckUserInterfaceAuth('business:projectMembers:add'))]
@ValidateFields(validate_model='batch_add_project_members')
@Log(title='项目成员管理', business_type=BusinessType.INSERT)
async def batch_add_project_members(
    batch_add: BatchAddProjectMembersModel,
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    add_result = await ProjectMembersService.batch_add_project_members_services(
        query_db, batch_add, current_user.user.user_id
    )
    logger.info(add_result.message)

    return ResponseUtil.success(msg=add_result.message)


@project_membersController.put('/updateRole')  # dependencies=[Depends(CheckUserInterfaceAuth('business:projectMembers:edit'))]
@ValidateFields(validate_model='update_project_member_role')
@Log(title='项目成员管理', business_type=BusinessType.UPDATE)
async def update_project_member_role(
    update_role: UpdateProjectMemberRoleModel,
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    update_result = await ProjectMembersService.update_project_member_role_services(query_db, update_role)
    logger.info(update_result.message)

    return ResponseUtil.success(msg=update_result.message)


@project_membersController.delete('/{project_id}/{user_id}')  # dependencies=[Depends(CheckUserInterfaceAuth('business:projectMembers:remove'))]
@Log(title='项目成员管理', business_type=BusinessType.DELETE)
async def remove_project_member(
    project_id: int,
    user_id: int,
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    remove_result = await ProjectMembersService.remove_project_member_services(query_db, project_id, user_id)
    logger.info(remove_result.message)

    return ResponseUtil.success(msg=remove_result.message)


@project_membersController.get('/userProjects/{user_id}')
async def get_user_projects(
    user_id: int,
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """获取用户所属的项目列表"""
    projects = await ProjectMembersService.get_user_projects_services(query_db, user_id)
    logger.info(f'获取用户{user_id}的项目列表成功')

    return ResponseUtil.success(data=projects)


@project_membersController.get('/projectMembers/{project_id}')
async def get_project_members(
    project_id: int,
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """获取项目成员列表"""
    members = await ProjectMembersService.get_project_members_services(query_db, project_id)
    logger.info(f'获取项目{project_id}的成员列表成功')

    return ResponseUtil.success(data=members)
