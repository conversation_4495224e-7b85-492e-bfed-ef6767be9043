# 任务队列系统

## 概述

本系统基于Redis实现了一个高性能的任务队列，支持任务的提交、排队、调度和执行。系统包含以下核心组件：

- **任务队列服务** (`TaskQueueService`): 使用Redis List结构维护任务队列
- **任务调度服务** (`TaskSchedulerService`): 从队列取任务并调用外部服务
- **调度器管理器** (`SchedulerManager`): 管理调度器的生命周期
- **API接口**: 提供任务提交、队列查询、回调处理等功能

## 系统架构

```
用户提交任务 → 任务队列 → 任务调度器 → 外部服务 → 回调通知 → 更新任务状态
```

### 核心组件

1. **任务队列服务** (`module_scheduler/service/task_queue_service.py`)
   - 使用Redis List存储任务ID队列
   - 使用Redis Hash存储任务详细信息
   - 支持任务的提交、查询、移除等操作

2. **任务调度服务** (`module_scheduler/service/task_scheduler_service.py`)
   - 异步循环从队列头部取任务
   - 根据任务类型调用相应的外部服务（使用requests库）
   - 处理外部服务的回调通知
   - 更新任务状态

3. **调度器管理器** (`module_scheduler/service/scheduler_manager.py`)
   - 管理调度器的启动和停止
   - 提供调度器状态查询功能

## API接口

### 任务队列管理

#### 1. 提交任务到队列
```http
POST /business/task-queue/submit/{task_id}
```

**响应示例:**
```json
{
  "code": 200,
  "msg": "任务已提交到队列",
  "data": {
    "task_id": 123,
    "queue_position": 5,
    "status": "queued",
    "submitted_at": "2024-01-01T12:00:00"
  }
}
```

#### 2. 查询任务队列位置
```http
GET /business/task-queue/position/{task_id}
```

#### 3. 获取队列长度
```http
GET /business/task-queue/length
```

#### 4. 获取队列状态
```http
GET /business/task-queue/status
```

#### 5. 清空队列（管理员功能）
```http
DELETE /business/task-queue/clear
```

### 任务回调处理

#### 1. 接收单个任务回调
```http
POST /business/tasks/callback/{task_id}
Content-Type: application/json

{
  "status": "completed",
  "result": {
    "output_file": "/path/to/result.txt",
    "execution_time": 120
  },
  "error_message": ""
}
```

#### 2. 接收批量任务回调
```http
POST /business/tasks/callback/batch
Content-Type: application/json

{
  "tasks": [
    {
      "task_id": 123,
      "status": "completed",
      "result": {...},
      "error_message": ""
    }
  ]
}
```

### 调度器管理

**注意**: 调度器会随着应用启动自动启动，无需手动管理。

调度器状态可以通过日志查看，系统会自动记录：
- 调度器启动和停止
- 任务处理过程
- 错误和异常信息

## 任务状态流转

```
pending → queued → running → completed/failed
```

- **pending**: 任务已创建，等待提交到队列
- **queued**: 任务已提交到队列，等待执行
- **running**: 任务正在执行中
- **completed**: 任务执行成功
- **failed**: 任务执行失败

## 外部服务集成

### 工具配置

在 `rd_tools` 表中配置工具的 `executable_api` 字段，例如：

```sql
INSERT INTO rd_tools (tool_name, type_id, executable_api) VALUES 
('建模工具', 1, 'http://localhost:8001/run'),
('仿真工具', 2, 'http://localhost:8002/run'),
('选型工具', 3, 'http://localhost:8003/run');
```

### 外部服务要求

外部服务需要实现以下接口：

1. **任务执行接口** (POST)
   - 接收任务参数
   - 异步执行任务
   - 返回接收确认

2. **回调通知**
   - 任务完成后调用平台的回调接口
   - 传递执行结果和状态

### 回调数据格式

```json
{
  "status": "completed|failed",
  "result": {
    "output_files": ["/path/to/file1.txt", "/path/to/file2.txt"],
    "execution_time": 120,
    "metadata": {...}
  },
  "error_message": "错误信息（如果失败）"
}
```

## 配置要求

### Redis配置

确保Redis服务已启动，并在 `config/env.py` 中配置正确的Redis连接信息：

```python
class RedisConfig(BaseSettings):
    redis_host: str = "localhost"
    redis_port: int = 6379
    redis_username: str = ""
    redis_password: str = ""
    redis_database: int = 0
```

### 依赖要求

系统使用以下主要依赖：

- `redis>=5.2.1` - Redis客户端
- `requests==2.32.3` - HTTP客户端（用于调用外部服务）
- `SQLAlchemy[asyncio]==2.0.38` - 数据库ORM
- `fastapi[all]==0.115.8` - Web框架

**注意**: 系统使用 `requests` 库进行同步HTTP请求，而不是 `aiohttp`，这样可以避免复杂的异步HTTP客户端依赖问题。

## 使用示例

### 1. 创建任务

```python
# 创建任务记录
task_data = {
    "project_id": 1,
    "task_name": "建模任务",
    "type_id": 1,
    "tool_id": 1,
    "parameters": {"model_type": "neural_network"},
    "status": "pending"
}
```

### 2. 提交任务到队列

```python
# 调用API提交任务
response = await client.post(f"/business/task-queue/submit/{task_id}")
queue_info = response.json()["data"]
print(f"任务已提交，队列位置: {queue_info['queue_position']}")
```

### 3. 监控任务状态

```python
# 查询任务状态
response = await client.get(f"/business/tasks/{task_id}")
task_status = response.json()["data"]["status"]
print(f"任务状态: {task_status}")
```

## 监控和日志

系统提供详细的日志记录，包括：

- 任务提交和队列操作
- 调度器启动和停止
- 外部服务调用
- 回调处理
- 错误和异常

可以通过以下接口监控系统状态：

- `/business/task-queue/status` - 队列状态
- `/business/task-queue/length` - 队列长度

调度器状态可通过应用日志查看。

## 故障排除

### 常见问题

1. **Redis连接失败**
   - 检查Redis服务是否启动
   - 验证连接配置是否正确

2. **外部服务调用失败**
   - 检查外部服务是否可访问
   - 验证API地址配置是否正确
   - 检查网络连接

3. **任务卡在队列中**
   - 检查调度器是否正常运行
   - 查看调度器日志
   - 检查外部服务状态

4. **ModuleNotFoundError: No module named 'aiohttp'**
   - 系统已改用 `requests` 库，不再需要 `aiohttp`
   - 确保 `requests` 库已安装：`pip install requests`

### 调试方法

1. 查看应用日志
2. 使用Redis客户端查看队列状态
3. 调用状态查询接口
4. 检查数据库中的任务状态

## 扩展功能

系统支持以下扩展：

1. **优先级队列**: 可以为任务设置优先级
2. **任务重试**: 失败任务自动重试机制
3. **负载均衡**: 多个调度器实例
4. **任务超时**: 任务执行超时控制
5. **资源限制**: 并发任务数量限制 