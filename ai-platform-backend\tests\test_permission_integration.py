"""
权限系统集成测试
"""
import pytest
from unittest.mock import AsyncMock, patch, MagicMock
from fastapi.testclient import TestClient
from sqlalchemy.ext.asyncio import AsyncSession

from module_admin.entity.vo.user_vo import CurrentUserModel, UserModel
from module_admin.entity.do.role_do import SysRole
from module_business.entity.vo.projects_vo import ProjectsModel
from module_business.entity.vo.tasks_vo import TasksModel
from config.role_constants import RoleConstants
from exceptions.exception import PermissionException


class TestPermissionIntegration:
    """权限系统集成测试类"""

    @pytest.fixture
    def mock_db(self):
        """模拟数据库会话"""
        return AsyncMock(spec=AsyncSession)

    @pytest.fixture
    def super_admin_user(self):
        """超级管理员用户"""
        role = SysRole(role_id=1, role_key='super_admin', role_name='超级管理员')
        user = UserModel(user_id=1, user_name='admin', role=[role])
        return CurrentUserModel(user=user)

    @pytest.fixture
    def project_manager_user(self):
        """项目管理员用户"""
        role = SysRole(role_id=2, role_key='project_manager', role_name='项目管理员')
        user = UserModel(user_id=2, user_name='pm', role=[role])
        return CurrentUserModel(user=user)

    @pytest.fixture
    def project_member_user(self):
        """项目成员用户"""
        role = SysRole(role_id=3, role_key='project_member', role_name='项目成员')
        user = UserModel(user_id=3, user_name='member', role=[role])
        return CurrentUserModel(user=user)

    @pytest.fixture
    def common_user(self):
        """普通用户"""
        role = SysRole(role_id=4, role_key='common_user', role_name='普通用户')
        user = UserModel(user_id=4, user_name='user', role=[role])
        return CurrentUserModel(user=user)

    @pytest.fixture
    def sample_project(self):
        """示例项目"""
        return AsyncMock(project_id=1, project_name='测试项目', owner_id=2)

    @pytest.fixture
    def sample_task(self):
        """示例任务"""
        return AsyncMock(task_id=1, task_name='测试任务', project_id=1, assigned_to=3)

    @pytest.mark.asyncio
    async def test_project_access_permissions(self, mock_db, sample_project):
        """测试项目访问权限的完整流程"""
        
        # 测试超级管理员
        super_admin = self._create_user(1, 'super_admin')
        with patch('module_business.dao.projects_dao.ProjectsDao.get_projects_detail_by_info') as mock_project_dao:
            mock_project_dao.return_value = sample_project
            
            from utils.permission_util import PermissionUtil
            result = await PermissionUtil.check_project_permission(mock_db, super_admin, 1)
            assert result is True

        # 测试项目所有者
        project_owner = self._create_user(2, 'project_manager')
        with patch('module_business.dao.projects_dao.ProjectsDao.get_projects_detail_by_info') as mock_project_dao:
            mock_project_dao.return_value = sample_project
            
            result = await PermissionUtil.check_project_permission(mock_db, project_owner, 1)
            assert result is True

        # 测试项目成员
        project_member = self._create_user(3, 'project_member')
        with patch('module_business.dao.projects_dao.ProjectsDao.get_projects_detail_by_info') as mock_project_dao, \
             patch('module_business.dao.project_members_dao.ProjectMembersDao.check_user_project_permission') as mock_member_check:
            
            mock_project_dao.return_value = sample_project
            mock_member_check.return_value = True
            
            result = await PermissionUtil.check_project_permission(mock_db, project_member, 1)
            assert result is True

        # 测试无权限用户
        unauthorized_user = self._create_user(5, 'common_user')
        with patch('module_business.dao.projects_dao.ProjectsDao.get_projects_detail_by_info') as mock_project_dao, \
             patch('module_business.dao.project_members_dao.ProjectMembersDao.check_user_project_permission') as mock_member_check:
            
            mock_project_dao.return_value = sample_project
            mock_member_check.return_value = False
            
            result = await PermissionUtil.check_project_permission(mock_db, unauthorized_user, 1)
            assert result is False

    @pytest.mark.asyncio
    async def test_task_edit_permissions(self, mock_db, sample_task):
        """测试任务编辑权限的完整流程"""
        
        # 测试超级管理员
        super_admin = self._create_user(1, 'super_admin')
        with patch('module_business.dao.tasks_dao.TasksDao.get_tasks_detail_by_info') as mock_task_dao:
            mock_task_dao.return_value = sample_task
            
            from utils.permission_util import PermissionUtil
            result = await PermissionUtil.check_task_edit_permission(mock_db, super_admin, 1)
            assert result is True

        # 测试任务创建者
        task_creator = self._create_user(3, 'project_member')
        with patch('module_business.dao.tasks_dao.TasksDao.get_tasks_detail_by_info') as mock_task_dao:
            mock_task_dao.return_value = sample_task
            
            result = await PermissionUtil.check_task_edit_permission(mock_db, task_creator, 1)
            assert result is True

        # 测试项目管理员
        project_manager = self._create_user(2, 'project_manager')
        with patch('module_business.dao.tasks_dao.TasksDao.get_tasks_detail_by_info') as mock_task_dao, \
             patch('module_business.dao.project_members_dao.ProjectMembersDao.check_user_project_permission') as mock_permission, \
             patch('module_business.dao.projects_dao.ProjectsDao.get_projects_detail_by_info') as mock_project_dao:
            
            mock_task_dao.return_value = sample_task
            mock_permission.return_value = True
            mock_project_dao.return_value = AsyncMock(owner_id=2)
            
            result = await PermissionUtil.check_task_edit_permission(mock_db, project_manager, 1)
            assert result is True

        # 测试无权限用户
        unauthorized_user = self._create_user(5, 'common_user')
        with patch('module_business.dao.tasks_dao.TasksDao.get_tasks_detail_by_info') as mock_task_dao, \
             patch('module_business.dao.project_members_dao.ProjectMembersDao.check_user_project_permission') as mock_permission, \
             patch('module_business.dao.projects_dao.ProjectsDao.get_projects_detail_by_info') as mock_project_dao:
            
            mock_task_dao.return_value = sample_task
            mock_permission.return_value = False
            mock_project_dao.return_value = AsyncMock(owner_id=2)
            
            result = await PermissionUtil.check_task_edit_permission(mock_db, unauthorized_user, 1)
            assert result is False

    def test_menu_access_permissions(self):
        """测试菜单访问权限"""
        from utils.permission_util import PermissionUtil
        
        # 测试各角色的菜单访问权限
        test_cases = [
            # (role_keys, menu_path, expected_result)
            (['super_admin'], '/dashboard', True),
            (['super_admin'], '/system', True),
            (['project_manager'], '/projects', True),
            (['project_manager'], '/database', True),
            (['project_manager'], '/system', False),
            (['project_member'], '/tasks', True),
            (['project_member'], '/projects', False),
            (['project_member'], '/database', False),
            (['common_user'], '/dashboard', True),
            (['common_user'], '/tools', True),
            (['common_user'], '/tasks', False),
            (['common_user'], '/projects', False),
        ]
        
        for role_keys, menu_path, expected in test_cases:
            result = PermissionUtil.check_menu_permission(role_keys, menu_path)
            assert result == expected, f"Role {role_keys} accessing {menu_path} should be {expected}"

    def test_interface_access_permissions(self):
        """测试接口访问权限"""
        from utils.permission_util import PermissionUtil
        
        # 测试各角色的接口访问权限
        test_cases = [
            # (role_keys, interface_code, expected_result)
            (['super_admin'], 'any:permission', True),
            (['project_manager'], 'business:projects:list', True),
            (['project_manager'], 'business:tasks:edit', True),
            (['project_manager'], 'system:user:add', False),
            (['project_member'], 'business:tasks:list', True),
            (['project_member'], 'business:projects:edit', False),
            (['common_user'], 'dashboard:view', True),
            (['common_user'], 'business:tasks:list', False),
        ]
        
        for role_keys, interface_code, expected in test_cases:
            result = PermissionUtil.check_interface_permission(role_keys, interface_code)
            assert result == expected, f"Role {role_keys} accessing {interface_code} should be {expected}"

    @pytest.mark.asyncio
    async def test_data_scope_filtering(self, mock_db):
        """测试数据权限过滤"""
        from module_business.aspect.data_scope import ProjectDataScopeFilter
        
        # 模拟查询对象
        mock_query = MagicMock()
        
        # 测试超级管理员 - 应该能看到所有数据
        super_admin = self._create_user(1, 'super_admin')
        filter_instance = ProjectDataScopeFilter()
        
        with patch.object(filter_instance, 'apply_data_scope') as mock_apply:
            mock_apply.return_value = mock_query
            
            result = await filter_instance.filter_projects(mock_query, super_admin, mock_db)
            mock_apply.assert_called_once_with(mock_query, super_admin, mock_db, 'project')

        # 测试项目成员 - 应该只能看到参与的项目
        project_member = self._create_user(3, 'project_member')
        
        with patch('module_business.dao.project_members_dao.ProjectMembersDao.get_user_projects') as mock_user_projects, \
             patch('module_business.dao.projects_dao.ProjectsDao.get_projects_by_owner') as mock_owned_projects:
            
            mock_user_projects.return_value = [AsyncMock(project_id=1)]
            mock_owned_projects.return_value = [AsyncMock(project_id=2)]
            
            # 这里应该测试实际的过滤逻辑
            # 由于涉及复杂的SQLAlchemy查询，这里只测试方法调用
            pass

    @pytest.mark.asyncio
    async def test_permission_escalation_prevention(self, mock_db):
        """测试权限提升防护"""
        from utils.permission_util import PermissionUtil
        
        # 测试普通用户不能访问管理功能
        common_user = self._create_user(4, 'common_user')
        
        # 测试项目管理权限
        assert PermissionUtil.check_interface_permission(['common_user'], 'business:projects:add') is False
        
        # 测试用户管理权限
        assert PermissionUtil.check_interface_permission(['common_user'], 'system:user:add') is False
        
        # 测试系统管理权限
        assert PermissionUtil.check_menu_permission(['common_user'], '/system') is False

    @pytest.mark.asyncio
    async def test_cross_project_access_prevention(self, mock_db):
        """测试跨项目访问防护"""
        from utils.permission_util import PermissionUtil
        
        # 项目成员不应该能访问其他项目
        project_member = self._create_user(3, 'project_member')
        
        with patch('module_business.dao.projects_dao.ProjectsDao.get_projects_detail_by_info') as mock_project_dao, \
             patch('module_business.dao.project_members_dao.ProjectMembersDao.check_user_project_permission') as mock_permission:
            
            # 模拟用户不是项目成员
            mock_project_dao.return_value = AsyncMock(project_id=999, owner_id=1)
            mock_permission.return_value = False
            
            result = await PermissionUtil.check_project_permission(mock_db, project_member, 999)
            assert result is False

    def test_role_hierarchy_enforcement(self):
        """测试角色层次结构强制执行"""
        # 测试角色级别
        assert RoleConstants.get_highest_role_level(['super_admin']) < RoleConstants.get_highest_role_level(['project_manager'])
        assert RoleConstants.get_highest_role_level(['project_manager']) < RoleConstants.get_highest_role_level(['project_member'])
        assert RoleConstants.get_highest_role_level(['project_member']) < RoleConstants.get_highest_role_level(['common_user'])
        
        # 测试权限继承
        assert RoleConstants.can_manage_projects(['super_admin']) is True
        assert RoleConstants.can_manage_projects(['project_manager']) is True
        assert RoleConstants.can_manage_projects(['project_member']) is False

    def _create_user(self, user_id: int, role_key: str) -> CurrentUserModel:
        """创建测试用户"""
        role = SysRole(role_id=user_id, role_key=role_key, role_name=RoleConstants.ROLE_NAMES.get(role_key, role_key))
        user = UserModel(user_id=user_id, user_name=f'user_{user_id}', role=[role])
        return CurrentUserModel(user=user)

    @pytest.mark.asyncio
    async def test_permission_edge_cases(self, mock_db):
        """测试权限边界情况"""
        from utils.permission_util import PermissionUtil
        
        # 测试不存在的项目
        user = self._create_user(1, 'project_manager')
        
        with patch('module_business.dao.projects_dao.ProjectsDao.get_projects_detail_by_info') as mock_project_dao:
            mock_project_dao.return_value = None
            
            with pytest.raises(PermissionException) as exc_info:
                await PermissionUtil.check_project_permission(mock_db, user, 999)
            
            assert '项目不存在' in str(exc_info.value)

        # 测试不存在的任务
        with patch('module_business.dao.tasks_dao.TasksDao.get_tasks_detail_by_info') as mock_task_dao:
            mock_task_dao.return_value = None
            
            with pytest.raises(PermissionException) as exc_info:
                await PermissionUtil.check_task_edit_permission(mock_db, user, 999)
            
            assert '任务不存在' in str(exc_info.value)

    def test_multiple_roles_permission(self):
        """测试多角色权限"""
        # 用户同时拥有多个角色时，应该取最高权限
        multiple_roles = ['common_user', 'project_member', 'project_manager']
        
        assert RoleConstants.is_project_manager(multiple_roles) is True
        assert RoleConstants.can_manage_projects(multiple_roles) is True
        assert RoleConstants.get_highest_role_level(multiple_roles) == 2
