"""
可靠回调处理器
基于 callback_example.py 的设计理念，为新的队列系统提供回调处理功能
"""

from datetime import datetime
from typing import Dict, Any
from redis import asyncio as aioredis
from sqlalchemy.ext.asyncio import AsyncSession

from module_scheduler.service.task_queue_service import TaskQueueService
from module_business.dao.tasks_dao import TasksDao
from utils.log_util import logger


class ReliableCallbackHandler:
    """
    可靠回调处理器
    处理外部服务的回调，确保任务状态正确更新并从处理队列移除
    """
    
    def __init__(self, redis_client: aioredis.Redis, db_session: AsyncSession):
        self.redis = redis_client
        self.db_session = db_session
        self.queue_service = TaskQueueService(redis_client)
    
    async def handle_task_completion_callback(self, task_id: int, callback_data: Dict[str, Any]) -> bool:
        """
        处理任务完成回调
        
        Args:
            task_id: 任务ID
            callback_data: 回调数据，格式如：
            {
                "status": "completed|failed",
                "result": {...},  # 任务结果（成功时）
                "error_message": "...",  # 错误信息（失败时）
                "progress": 100,
                "message": "任务完成"
            }
            
        Returns:
            是否处理成功
        """
        try:
            status = callback_data.get('status', 'unknown')
            success = status == 'completed'
            
            logger.info(f"📞 收到任务 {task_id} 的完成回调，状态: {status}")
            
            # 使用队列服务的回调处理机制
            queue_success = await self.queue_service.handle_task_callback(
                task_id, 
                success, 
                callback_data.get('result') if success else callback_data.get('error_message')
            )
            
            if not queue_success:
                logger.error(f"❌ 队列回调处理失败，任务 {task_id}")
                return False
            
            # 更新数据库中的任务状态
            db_success = await self._update_database_task_status(task_id, callback_data)

            if not db_success:
                logger.error(f"❌ 数据库状态更新失败，任务 {task_id}")
                return False

            # 如果任务成功完成，处理结果文件
            if success:
                await self._handle_result_files(task_id, callback_data)

            logger.info(f"✅ 任务 {task_id} 回调处理完成")
            return True
            
        except Exception as e:
            logger.error(f"❌ 处理任务完成回调失败，任务 {task_id}: {e}")
            return False
    
    async def handle_progress_callback(self, task_id: int, progress_data: Dict[str, Any]) -> bool:
        """
        处理任务进度回调
        
        Args:
            task_id: 任务ID
            progress_data: 进度数据，格式如：
            {
                "progress": 50,
                "stage": "数据处理中",
                "message": "正在处理第2步",
                "timestamp": "2024-01-01T12:00:00Z"
            }
            
        Returns:
            是否处理成功
        """
        try:
            progress = progress_data.get('progress', 0)
            stage = progress_data.get('stage', '')
            message = progress_data.get('message', '')
            
            logger.info(f"📊 收到任务 {task_id} 的进度回调，进度: {progress}%")
            
            # 使用增强的_update_task_status方法更新Redis中的任务状态
            await self.queue_service._update_task_status(
                task_id,
                "running",  # 进度回调时状态保持为running
                message,
                progress=progress,
                stage=stage
            )
            
            # 更新数据库中的任务进度
            try:
                update_data = {
                    "task_id": task_id,
                    "progress": progress,
                    "message": message,
                    "updated_at": datetime.now()
                }
                await TasksDao.edit_tasks_dao(self.db_session, update_data)
                await self.db_session.commit()
                
            except Exception as db_error:
                logger.error(f"❌ 更新数据库进度失败，任务 {task_id}: {db_error}")
                await self.db_session.rollback()
                return False
            
            logger.info(f"📊 任务 {task_id} 进度更新完成: {progress}%")
            return True
            
        except Exception as e:
            logger.error(f"❌ 处理任务进度回调失败，任务 {task_id}: {e}")
            return False
    
    async def handle_status_callback(self, task_id: int, status_data: Dict[str, Any]) -> bool:
        """
        处理任务状态回调
        
        Args:
            task_id: 任务ID
            status_data: 状态数据
            
        Returns:
            是否处理成功
        """
        try:
            status = status_data.get('status', 'unknown')
            message = status_data.get('message', '')
            
            logger.info(f"📋 收到任务 {task_id} 的状态回调，状态: {status}")
            
            # 根据状态类型处理
            if status in ['completed', 'failed']:
                return await self.handle_task_completion_callback(task_id, status_data)
            elif status in ['running', 'processing']:
                return await self.handle_progress_callback(task_id, status_data)
            else:
                # 其他状态更新
                await self.queue_service._update_task_status(task_id, status, message)
                return True
                
        except Exception as e:
            logger.error(f"❌ 处理任务状态回调失败，任务 {task_id}: {e}")
            return False
    
    async def _update_database_task_status(self, task_id: int, callback_data: Dict[str, Any]) -> bool:
        """
        更新数据库中的任务状态
        
        Args:
            task_id: 任务ID
            callback_data: 回调数据
            
        Returns:
            是否更新成功
        """
        try:
            status = callback_data.get('status')
            progress = callback_data.get('progress', 0)
            message = callback_data.get('message', '')
            error_message = callback_data.get('error_message')
            
            update_data = {
                "task_id": task_id,
                "status": status,
                "progress": progress,
                "message": message,
                "updated_at": datetime.now()
            }
            
            # 如果任务完成，设置完成时间
            if status == 'completed':
                update_data["completed_at"] = datetime.now()
            
            # 如果任务失败，设置错误信息
            if status == 'failed' and error_message:
                update_data["error_message"] = error_message
            
            await TasksDao.edit_tasks_dao(self.db_session, update_data)
            await self.db_session.commit()
            
            logger.info(f"✅ 数据库任务状态更新完成，任务 {task_id}: {status}")
            return True
            
        except Exception as e:
            logger.error(f"❌ 更新数据库任务状态失败，任务 {task_id}: {e}")
            await self.db_session.rollback()
            return False

    async def _handle_result_files(self, task_id: int, callback_data: Dict[str, Any]):
        """
        处理任务结果文件

        Args:
            task_id: 任务ID
            callback_data: 回调数据
        """
        try:
            result = callback_data.get('result', {})
            output_files = result.get('output_files', {})

            logger.info(f"任务 {task_id} 开始处理结果文件，output_files: {output_files}")

            if not output_files:
                logger.info(f"任务 {task_id} 没有输出文件信息")
                # 尝试扫描MinIO查找文件
                await self._scan_minio_for_task_files(task_id)
                return

            # 获取任务信息以获取知识库ID
            from module_business.dao.tasks_dao import TasksDao
            task_info = await TasksDao.get_tasks_detail_by_id(self.db_session, task_id)

            if not task_info:
                logger.error(f"任务 {task_id} 信息不存在")
                return

            # 通过项目ID获取知识库ID
            kb_id = None
            if task_info.project_id:
                try:
                    from sqlalchemy import select
                    from module_business.entity.do.knowledge_bases_do import RdKnowledgeBases

                    query = (
                        select(RdKnowledgeBases)
                        .where(
                            RdKnowledgeBases.project_id == task_info.project_id,
                            RdKnowledgeBases.is_deleted == 0
                        )
                        .order_by(RdKnowledgeBases.kb_id)
                        .limit(1)
                    )

                    result_query = await self.db_session.execute(query)
                    kb_obj = result_query.scalars().first()
                    if kb_obj:
                        kb_id = kb_obj.kb_id
                        logger.info(f"任务 {task_id} 通过项目ID {task_info.project_id} 获取到知识库ID: {kb_id}")
                    else:
                        logger.warning(f"任务 {task_id} 项目ID {task_info.project_id} 未找到关联的知识库")
                except Exception as e:
                    logger.error(f"获取知识库ID失败: {e}")

            if not kb_id:
                logger.error(f"任务 {task_id} 没有知识库ID，跳过文件处理")
                return

            # 处理每个输出文件
            from module_business.entity.vo.files_vo import FilesModel
            from utils.minio_util import MinioUtil
            from module_business.dao.files_dao import FilesDao
            from datetime import datetime
            import os

            minio_util = MinioUtil.get_instance()

            for file_type, file_path in output_files.items():
                try:
                    # 检查文件是否已存在
                    existing_file = await FilesDao.get_files_by_path_and_task(self.db_session, file_path, task_id)
                    if existing_file:
                        logger.info(f"任务 {task_id} 文件已存在，跳过保存: {file_path}")
                        continue

                    # 获取文件信息
                    file_name = os.path.basename(file_path)

                    # 尝试获取文件大小
                    file_size = 0
                    try:
                        file_content = await minio_util.download_file_by_path(file_path)
                        if file_content:
                            file_size = len(file_content)
                    except Exception as e:
                        logger.warning(f"获取文件大小失败: {e}")

                    # 确定文件格式
                    file_format = os.path.splitext(file_name)[1][1:].lower() if '.' in file_name else ''

                    # 映射文件类型到数据库类型
                    db_file_type_mapping = {
                        'model': 'model',
                        'report': 'report',
                        'log': 'log',
                        'dataset': 'dataset',
                        'document': 'document'
                    }
                    db_file_type = db_file_type_mapping.get(file_type, 'document')

                    # 创建文件记录
                    file_model = FilesModel(
                        kbId=kb_id,
                        originalName=file_name,
                        storagePath=file_path,
                        fileType=db_file_type,
                        fileSize=file_size,
                        fileFormat=file_format,
                        projectId=task_info.project_id if task_info else None,
                        sourceTaskId=task_id,
                        createdAt=datetime.now()
                    )

                    file_model.validate_fields()
                    await FilesDao.add_files_dao(self.db_session, file_model)
                    logger.info(f"任务 {task_id} 文件保存成功: {file_type} -> {file_path}")

                except Exception as e:
                    logger.error(f"任务 {task_id} 处理文件 {file_type} 失败: {e}")
                    continue

            await self.db_session.commit()
            logger.info(f"任务 {task_id} 所有文件处理完成")

        except Exception as e:
            logger.error(f"处理任务结果文件失败，任务 {task_id}: {e}")
            await self.db_session.rollback()
            # 不重新抛出异常，避免影响主回调流程

    async def _scan_minio_for_task_files(self, task_id: int):
        """
        扫描MinIO中该任务的文件并添加到数据库

        Args:
            task_id: 任务ID
        """
        try:
            # 获取任务信息
            from module_business.dao.tasks_dao import TasksDao
            task_info = await TasksDao.get_tasks_detail_by_id(self.db_session, task_id)

            if not task_info:
                logger.error(f"任务 {task_id} 信息不存在")
                return

            # 通过项目ID获取知识库ID
            kb_id = None
            if task_info.project_id:
                try:
                    from sqlalchemy import select
                    from module_business.entity.do.knowledge_bases_do import RdKnowledgeBases

                    query = (
                        select(RdKnowledgeBases)
                        .where(
                            RdKnowledgeBases.project_id == task_info.project_id,
                            RdKnowledgeBases.is_deleted == 0
                        )
                        .order_by(RdKnowledgeBases.kb_id)
                        .limit(1)
                    )

                    result_query = await self.db_session.execute(query)
                    kb_obj = result_query.scalars().first()
                    if kb_obj:
                        kb_id = kb_obj.kb_id
                        logger.info(f"任务 {task_id} 通过项目ID {task_info.project_id} 获取到知识库ID: {kb_id}")
                    else:
                        logger.warning(f"任务 {task_id} 项目ID {task_info.project_id} 未找到关联的知识库")
                except Exception as e:
                    logger.error(f"获取知识库ID失败: {e}")

            if not kb_id:
                logger.warning(f"任务 {task_id} 没有知识库ID，跳过MinIO扫描")
                return

            from utils.minio_util import MinioUtil
            from module_business.entity.vo.files_vo import FilesModel
            from module_business.dao.files_dao import FilesDao
            from datetime import datetime
            import os

            minio_util = MinioUtil.get_instance()
            found_files = []

            # 尝试检查常见的文件名模式
            common_files = [
                f"kb_{kb_id}/report/o_ring_report_task_{task_id}.txt",
                f"kb_{kb_id}/log/o_ring_design_task_{task_id}.log"
            ]

            for file_path in common_files:
                try:
                    # 尝试下载文件以验证存在性
                    file_content = await minio_util.download_file_by_path(file_path)
                    if file_content:
                        file_name = os.path.basename(file_path)
                        file_size = len(file_content)
                        file_format = os.path.splitext(file_name)[1][1:].lower()

                        # 根据路径确定文件类型
                        if '/report/' in file_path:
                            file_type = 'report'
                        elif '/log/' in file_path:
                            file_type = 'log'
                        else:
                            file_type = 'document'

                        # 检查是否已存在
                        existing_file = await FilesDao.get_files_by_path_and_task(self.db_session, file_path, task_id)
                        if existing_file:
                            logger.info(f"任务 {task_id} 文件已存在，跳过: {file_name}")
                            continue

                        # 创建文件记录
                        file_model = FilesModel(
                            kbId=kb_id,
                            originalName=file_name,
                            storagePath=file_path,
                            fileType=file_type,
                            fileSize=file_size,
                            fileFormat=file_format,
                            projectId=task_info.project_id if task_info else None,
                            sourceTaskId=task_id,
                            createdAt=datetime.now()
                        )

                        file_model.validate_fields()
                        await FilesDao.add_files_dao(self.db_session, file_model)
                        found_files.append(file_name)
                        logger.info(f"任务 {task_id} 从MinIO发现并添加文件: {file_name} -> {file_path}")

                except Exception as e:
                    # 文件不存在或其他错误，继续检查下一个
                    logger.debug(f"任务 {task_id} 检查文件 {file_path} 失败: {e}")
                    continue

            if found_files:
                await self.db_session.commit()
                logger.info(f"任务 {task_id} 从MinIO成功添加 {len(found_files)} 个文件: {found_files}")
            else:
                logger.info(f"任务 {task_id} 在MinIO中没有找到文件")

        except Exception as e:
            logger.error(f"扫描MinIO文件失败，任务 {task_id}: {e}")
            await self.db_session.rollback()
    
    async def validate_callback(self, task_id: int) -> bool:
        """
        验证回调的有效性

        Args:
            task_id: 任务ID

        Returns:
            回调是否有效
        """
        try:
            # 首先检查任务是否存在于数据库中
            from module_business.dao.tasks_dao import TasksDao
            task_info = await TasksDao.get_tasks_detail_by_id(self.db_session, task_id)

            if not task_info:
                logger.warning(f"⚠️ 回调验证失败：任务 {task_id} 不存在于数据库中")
                return False

            # 检查工具是否需要队列
            tool_requires_queue = True
            if task_info.tool_id:
                try:
                    from module_business.dao.tools_dao import ToolsDao
                    tool_info = await ToolsDao.get_tools_detail_by_id(self.db_session, task_info.tool_id)
                    if tool_info:
                        tool_requires_queue = bool(tool_info.queue_required)
                        logger.info(f"任务 {task_id} 的工具 {task_info.tool_id} ({tool_info.tool_name}) queue_required: {tool_requires_queue}")
                except Exception as e:
                    logger.error(f"获取工具信息失败: {e}")

            # 如果工具不需要队列（如选型任务），则跳过Redis验证
            if not tool_requires_queue:
                logger.info(f"✅ 任务 {task_id} 的工具不需要队列，跳过Redis验证")
                return True

            # 对于需要队列的任务，检查Redis中的状态
            task_info_key = f"task_info:{task_id}"
            exists = await self.redis.hexists(task_info_key, "id")

            if not exists:
                logger.warning(f"⚠️ 回调验证失败：任务 {task_id} 不存在于Redis中")
                return False

            # 检查任务是否在处理队列中
            processing_queue_key = "task_processing_queue_new"
            in_processing = await self.redis.lpos(processing_queue_key, task_id)

            if in_processing is None:
                logger.warning(f"⚠️ 回调验证失败：任务 {task_id} 不在处理队列中")
                return False

            return True

        except Exception as e:
            logger.error(f"❌ 回调验证失败，任务 {task_id}: {e}")
            return False
