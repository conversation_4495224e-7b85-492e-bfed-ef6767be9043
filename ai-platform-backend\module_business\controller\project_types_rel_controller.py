from fastapi import APIRouter, Depends, Request
from pydantic_validation_decorator import Valida<PERSON><PERSON>ields
from sqlalchemy.ext.asyncio import AsyncSession
from config.enums import BusinessType
from config.get_db import get_db
from module_admin.annotation.log_annotation import Log
from module_admin.aspect.interface_auth import CheckUserInterfaceAuth
from module_admin.entity.vo.user_vo import CurrentUserModel
from module_admin.service.login_service import LoginService
from module_business.service.project_types_rel_service import ProjectTypesRelService
from module_business.entity.vo.project_types_rel_vo import ProjectTypesRelModel, ProjectTypesRelPageQueryModel
from module_business.entity.vo.projects_vo import ProjectsModel
from utils.log_util import logger
from utils.page_util import PageResponseModel
from utils.response_util import ResponseUtil
from datetime import datetime
from typing import List, Optional
from module_business.entity.vo.kb_types_rel_vo import KbTypesRelModel
from module_business.service.knowledge_bases_service import KnowledgeBasesService
from module_business.service.kb_types_rel_service import KbTypesRelService
from module_business.entity.vo.knowledge_bases_vo import KnowledgeBasesPageQueryModel


project_types_relController = APIRouter(prefix='/business/project_types_rel', dependencies=[Depends(LoginService.get_current_user)])


async def _get_kb_id_by_project_id(query_db: AsyncSession, project_id: int) -> Optional[int]:
    """
    根据项目ID获取关联的知识库ID
    """
    kb_query = KnowledgeBasesPageQueryModel(projectId=project_id, pageNum=1, pageSize=1)
    kb_list_result = await KnowledgeBasesService.get_knowledge_bases_list_services(query_db, kb_query, is_page=False)
    
    kb_info = None
    if kb_list_result and hasattr(kb_list_result, 'rows') and kb_list_result.rows:
        kb_info = kb_list_result.rows[0]
    elif kb_list_result and isinstance(kb_list_result, list) and kb_list_result:
        kb_info = kb_list_result[0]
    
    # 处理ORM对象或字典
    if kb_info:
        if hasattr(kb_info, 'kb_id'):
            # ORM对象
            return kb_info.kb_id
        else:
            # 字典对象
            return kb_info.get('kbId') or kb_info.get('kb_id')
    
    return None


async def _handle_kb_type_rel(query_db: AsyncSession, kb_id: int, type_id: int, 
                             current_user_id: int, operation: str = 'add'):
    """
    处理知识库类型关联的添加或删除
    """
    try:
        if operation == 'add':
            # 先检查关联是否已存在
            from module_business.dao.kb_types_rel_dao import KbTypesRelDao
            existing_rel = await KbTypesRelDao.get_kb_types_rel_by_kb_id_and_type_id(query_db, kb_id, type_id)
            
            if existing_rel:
                # 关联已存在，不需要重复添加
                logger.info(f"知识库类型关联已存在: kb_id={kb_id}, type_id={type_id}")
                return
            
            kb_type_rel = KbTypesRelModel(
                kbId=kb_id,
                typeId=type_id,
                assignedBy=current_user_id,
                assignedAt=datetime.now()
            )
            await KbTypesRelService.add_kb_types_rel_services(query_db, kb_type_rel)
        elif operation == 'delete':
            await KbTypesRelService.delete_kb_type_rel_by_kb_and_type_services(query_db, kb_id, type_id)
    except Exception as e:
        logger.warning(f"{'创建' if operation == 'add' else '删除'}知识库类型关联失败: {e}")


@project_types_relController.get(
    '/list', response_model=PageResponseModel  # dependencies=[Depends(CheckUserInterfaceAuth('business:project_types_rel:list'))]
)
async def get_business_project_types_rel_list(
    request: Request,
    project_types_rel_page_query: ProjectTypesRelPageQueryModel = Depends(),
    query_db: AsyncSession = Depends(get_db),
):
    project_types_rel_page_query_result = await ProjectTypesRelService.get_project_types_rel_list_services(query_db, project_types_rel_page_query, is_page=True)
    return ResponseUtil.success(model_content=project_types_rel_page_query_result)


@project_types_relController.post('')  # dependencies=[Depends(CheckUserInterfaceAuth('business:projectTypesRel:add'))]
@ValidateFields(validate_model='add_project_types_rel')
@Log(title='项目类型关联', business_type=BusinessType.INSERT)
async def add_business_project_types_rel(
    request: Request,
    add_project_types_rel: ProjectTypesRelModel,
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    add_project_types_rel.assigned_at = datetime.now()
    add_project_types_rel.assigned_by = current_user.user.user_id
    
    # 添加项目和类型关联
    add_project_types_rel_result = await ProjectTypesRelService.add_project_types_rel_services(query_db, add_project_types_rel)
    
    # 获取项目关联的知识库ID并创建知识库类型关联
    kb_id = await _get_kb_id_by_project_id(query_db, add_project_types_rel.project_id)
    if kb_id:
        await _handle_kb_type_rel(query_db, kb_id, add_project_types_rel.type_id, current_user.user.user_id, 'add')
    else:
        logger.warning(f"Knowledge base not found for project {add_project_types_rel.project_id}")
    
    return ResponseUtil.success(msg=add_project_types_rel_result.message)


@project_types_relController.get(
    '/{project_id}', response_model=ProjectTypesRelModel  # dependencies=[Depends(CheckUserInterfaceAuth('business:project_types_rel:query'))]
)
async def query_detail_business_project_types_rel(request: Request, project_id: int, query_db: AsyncSession = Depends(get_db)):
    project_types_rel_detail_result = await ProjectTypesRelService.project_types_rel_detail_services(query_db, project_id)
    return ResponseUtil.success(data=project_types_rel_detail_result)


@project_types_relController.get(
    '/list/{project_id}', response_model=List[ProjectTypesRelModel]  # dependencies=[Depends(CheckUserInterfaceAuth('business:project_types_rel:query'))]
)
async def query_project_types_rel_list(
    request: Request,
    project_id: int,
    query_db: AsyncSession = Depends(get_db),
):
    project_types_rel_list_result = await ProjectTypesRelService.get_project_types_rel_list_by_project_id_services(query_db, project_id)
    return ResponseUtil.success(data=project_types_rel_list_result)


@project_types_relController.get(
    '/projects/by_types', response_model=List[ProjectsModel]  # dependencies=[Depends(CheckUserInterfaceAuth('business:project_types_rel:query'))]
)
async def get_projects_by_types(
    request: Request,
    type_ids: str = '',  # 格式：1,2,3
    page_num: int = 1,
    page_size: int = 12,
    project_name: str = '',
    query_db: AsyncSession = Depends(get_db),
):
    """
    根据类型ID列表获取项目列表，支持分页和项目名称模糊搜索
    """
    type_id_list = [int(tid) for tid in type_ids.split(',') if tid]
    projects_page = await ProjectTypesRelService.get_projects_by_types_services(
        query_db, type_id_list, page_num, page_size, project_name
    )
    return ResponseUtil.success(model_content=projects_page)


@project_types_relController.delete(
    '/{project_id}/{type_id}')  # dependencies=[Depends(CheckUserInterfaceAuth('business:projectTypesRel:delete'))]
@Log(title='项目类型关联', business_type=BusinessType.DELETE)
async def delete_project_type_rel(
    request: Request,
    project_id: int,
    type_id: int,
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    删除特定的项目-类型关联关系
    """
    delete_result = await ProjectTypesRelService.delete_project_type_rel_by_project_and_type_services(query_db, project_id, type_id)
    
    # 获取项目关联的知识库ID并删除知识库类型关联
    kb_id = await _get_kb_id_by_project_id(query_db, project_id)
    if kb_id:
        await _handle_kb_type_rel(query_db, kb_id, type_id, current_user.user.user_id, 'delete')
    
    return ResponseUtil.success(msg=delete_result.message)
