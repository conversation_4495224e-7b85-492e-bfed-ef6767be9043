# 权限管理系统部署指南

## 概述

本指南将帮助您部署基于角色的权限管理系统，包括后端权限控制和前端菜单权限。

## 前置条件

- Python 3.8+
- PostgreSQL 数据库
- Node.js 16+
- Vue 3 前端环境

## 部署步骤

### 1. 后端部署

#### 1.1 数据库更新

执行以下SQL脚本更新数据库结构：

```bash
# 1. 创建项目成员表
psql -U your_username -d your_database -f sql/create_project_members_table.sql

# 2. 插入新角色和菜单权限
psql -U your_username -d your_database -f sql/insert_new_roles.sql

# 3. 更新菜单权限（如果需要重置）
psql -U your_username -d your_database -f sql/update_menu_permissions.sql
```

#### 1.2 代码部署

确保以下新增文件已部署到服务器：

**核心权限文件：**
- `config/role_constants.py` - 角色常量定义
- `utils/permission_util.py` - 权限验证工具类
- `module_business/aspect/project_auth.py` - 项目权限验证装饰器
- `module_business/aspect/data_scope.py` - 数据权限过滤器

**项目成员管理：**
- `module_business/entity/do/project_members_do.py`
- `module_business/entity/vo/project_members_vo.py`
- `module_business/dao/project_members_dao.py`
- `module_business/service/project_members_service.py`
- `module_business/controller/project_members_controller.py`

#### 1.3 路由注册

确保在 `server.py` 中注册了项目成员管理控制器：

```python
from module_business.controller.project_members_controller import project_membersController

# 在controller_list中添加
{'router': project_membersController, 'tags': ['项目成员管理']},
```

#### 1.4 权限验证更新

确保现有的控制器已更新权限验证逻辑：

- 项目管理控制器：添加了项目权限检查
- 任务管理控制器：添加了任务编辑权限检查
- 菜单控制器：添加了基于角色的菜单过滤

### 2. 前端部署

#### 2.1 文件更新

确保以下文件已更新：

**权限相关：**
- `src/utils/permission.js` - 扩展了权限检查函数
- `src/layout/components/Sidebar/index.vue` - 修复了计算属性错误

**新增页面：**
- `src/views/tasks/index.vue` - 任务管理主页面

**路由更新：**
- `src/router/index.js` - 添加了任务管理主页面路由

#### 2.2 依赖安装

```bash
cd ai-platform-frontend
npm install
```

#### 2.3 构建部署

```bash
# 开发环境
npm run dev

# 生产环境
npm run build
```

### 3. 测试验证

#### 3.1 后端权限测试

运行权限系统测试脚本：

```bash
cd ai-platform-backend
python test_permission_system.py
```

#### 3.2 前端功能测试

1. **登录测试**：使用不同角色的用户登录
2. **菜单权限测试**：验证不同角色看到的菜单是否正确
3. **功能权限测试**：验证各功能的访问权限

#### 3.3 权限验证测试

运行完整的权限测试套件：

```bash
cd ai-platform-backend
python run_permission_tests.py
```

## 角色权限说明

### 超级管理员 (super_admin)
- ✅ 所有功能权限
- ✅ 用户角色分配
- ✅ 系统管理
- ✅ 所有项目和任务管理

### 项目管理员 (project_manager)
- ✅ 管理负责的项目
- ✅ 管理项目成员
- ✅ 管理项目下所有任务
- ✅ 数据库管理
- ❌ 系统管理
- ❌ 其他项目访问

### 项目成员 (project_member)
- ✅ 查看所属项目任务
- ✅ 创建和执行任务
- ✅ 编辑自己创建的任务
- ❌ 项目管理
- ❌ 数据库管理
- ❌ 其他人的任务编辑

### 普通用户 (common_user)
- ✅ 首页访问
- ✅ 工具广场
- ❌ 项目和任务功能
- ❌ 数据库管理

## 故障排除

### 常见问题

1. **菜单不显示**
   - 检查用户角色是否正确分配
   - 验证菜单权限关联是否正确
   - 查看浏览器控制台错误

2. **权限验证失败**
   - 检查权限装饰器是否正确应用
   - 验证角色常量定义是否正确
   - 查看后端日志错误信息

3. **前端路由错误**
   - 检查路由配置是否正确
   - 验证组件路径是否存在
   - 查看Vue Router警告信息

### 调试方法

1. **后端调试**
   ```python
   # 在权限检查处添加日志
   from utils.log_util import logger
   logger.info(f"用户角色: {user_roles}, 检查权限: {permission}")
   ```

2. **前端调试**
   ```javascript
   // 在权限检查处添加控制台输出
   console.log('用户角色:', useUserStore().roles)
   console.log('权限检查结果:', result)
   ```

## 维护指南

### 添加新角色

1. 在 `RoleConstants` 中定义新角色
2. 更新权限检查方法
3. 在数据库中插入新角色记录
4. 配置角色菜单权限

### 添加新权限

1. 在 `PermissionUtil` 中添加权限检查方法
2. 在相关控制器中应用权限验证
3. 更新前端权限检查逻辑
4. 添加相应的测试用例

### 修改菜单权限

1. 更新 `MenuDao` 中的菜单过滤逻辑
2. 修改数据库中的角色菜单关联
3. 更新前端路由权限配置
4. 测试验证权限变更

## 安全注意事项

1. **权限提升防护**：严格检查角色层次，防止权限提升
2. **数据泄露防护**：确保数据权限过滤正确应用
3. **接口安全**：所有敏感接口都必须有权限验证
4. **前端安全**：前端权限检查仅用于UI显示，真正的安全控制在后端
5. **日志审计**：记录重要的权限操作日志

## 性能优化

1. **权限缓存**：考虑缓存用户权限信息
2. **数据库优化**：为权限相关表添加适当索引
3. **前端优化**：避免频繁的权限检查调用

## 联系支持

如果在部署过程中遇到问题，请：

1. 查看相关日志文件
2. 运行测试脚本验证
3. 检查配置文件是否正确
4. 联系开发团队获取支持
