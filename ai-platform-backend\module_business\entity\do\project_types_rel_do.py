from sqlalchemy import BigInteger, TIMESTAMP, Column, Integer
from config.database import Base


class RdProjectTypesRel(Base):
    """
    项目-类型关联关系表
    """

    __tablename__ = 'rd_project_types'

    project_id = Column(BigInteger, primary_key=True, nullable=False, comment='')
    type_id = Column(Integer, primary_key=True, nullable=False, comment='')
    assigned_at = Column(TIMESTAMP, nullable=True, comment='')
    assigned_by = Column(BigInteger, nullable=False, comment='')



