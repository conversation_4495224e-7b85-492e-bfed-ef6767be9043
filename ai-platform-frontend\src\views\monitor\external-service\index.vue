<template>
  <div class="app-container">
    <!-- 统计卡片 -->
    <el-row :gutter="20" class="mb-4">
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon total">
              <el-icon><Monitor /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ monitorData.totalServices || 0 }}</div>
              <div class="stat-label">总服务数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon healthy">
              <el-icon><CircleCheck /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ monitorData.healthyServices || 0 }}</div>
              <div class="stat-label">健康服务</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon unhealthy">
              <el-icon><CircleClose /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ monitorData.unhealthyServices || 0 }}</div>
              <div class="stat-label">异常服务</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon update">
              <el-icon><Clock /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ lastUpdateTime }}</div>
              <div class="stat-label">最后更新</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 服务列表 -->
    <el-card>
      <template #header>
        <div class="card-header">
          <span>工具健康状态</span>
          <el-button type="primary" @click="refreshData" :loading="loading">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </div>
      </template>

      <el-table
        :data="monitorData.services || []"
        v-loading="loading"
        style="width: 100%"
      >
        <el-table-column prop="toolName" label="工具名称" min-width="150">
          <template #default="{ row }">
            <div class="tool-name">
              {{ row.toolName }}
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="executableApi" label="API地址" min-width="200">
          <template #default="{ row }">
            <el-tooltip :content="row.executableApi" placement="top">
              <span class="api-url">{{ row.executableApi }}</span>
            </el-tooltip>
          </template>
        </el-table-column>

        <el-table-column prop="healthUrl" label="健康检查地址" min-width="200">
          <template #default="{ row }">
            <el-tooltip :content="row.healthUrl" placement="top">
              <span class="health-url">{{ row.healthUrl }}</span>
            </el-tooltip>
          </template>
        </el-table-column>

        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag
              :type="getStatusType(row.status)"
              size="small"
            >
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="responseTime" label="响应时间" width="120">
          <template #default="{ row }">
            <span v-if="row.responseTime" class="response-time">
              {{ row.responseTime }}ms
            </span>
            <span v-else class="no-response">-</span>
          </template>
        </el-table-column>

        <el-table-column prop="lastCheckTime" label="最后检查时间" width="180">
          <template #default="{ row }">
            {{ formatDateTime(row.lastCheckTime) }}
          </template>
        </el-table-column>

        <el-table-column prop="errorMessage" label="错误信息" min-width="200">
          <template #default="{ row }">
            <el-tooltip v-if="row.errorMessage" :content="row.errorMessage" placement="top">
              <span class="error-message">{{ row.errorMessage }}</span>
            </el-tooltip>
            <span v-else class="no-error">-</span>
          </template>
        </el-table-column>

        <el-table-column label="操作" width="120" fixed="right">
          <template #default="{ row }">
            <el-button
              type="primary"
              size="small"
              @click="testService(row)"
              :loading="row.testing"
            >
              测试
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { Monitor, CircleCheck, CircleClose, Clock, Refresh } from '@element-plus/icons-vue'
import { getExternalServiceMonitor, testServiceHealth } from '@/api/monitor/external-service'

const loading = ref(false)
const monitorData = ref({
  totalServices: 0,
  healthyServices: 0,
  unhealthyServices: 0,
  services: [],
  lastUpdateTime: null
})

// 格式化最后更新时间
const lastUpdateTime = computed(() => {
  if (!monitorData.value.lastUpdateTime) return '-'
  return formatDateTime(monitorData.value.lastUpdateTime)
})

// 格式化日期时间
const formatDateTime = (dateTime) => {
  if (!dateTime) return '-'
  const date = new Date(dateTime)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

// 获取状态类型
const getStatusType = (status) => {
  const statusMap = {
    'healthy': 'success',
    'unhealthy': 'danger',
    'unavailable': 'info',
    'recovered': 'warning',
    'degraded': 'danger'
  }
  return statusMap[status] || 'danger'
}

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    'healthy': '健康',
    'unhealthy': '异常',
    'unavailable': '不可用',
    'recovered': '已恢复',
    'degraded': '已降级'
  }
  return statusMap[status] || '未知'
}

// 刷新数据
const refreshData = async () => {
  loading.value = true
  try {
    const response = await getExternalServiceMonitor()
    if (response.code === 200) {
      monitorData.value = response.data
      ElMessage.success('数据刷新成功')
    } else {
      ElMessage.error(response.msg || '获取数据失败')
    }
  } catch (error) {
    console.error('获取外部服务监控数据失败:', error)
    ElMessage.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

// 测试服务
const testService = async (service) => {
  service.testing = true
  try {
    const response = await testServiceHealth(service.toolId)
    if (response.code === 200) {
      const result = response.data
      ElMessage.success(result.message || '服务测试完成')
    } else {
      ElMessage.error(response.msg || '服务测试失败')
    }
  } catch (error) {
    console.error('服务测试失败:', error)
    ElMessage.error('服务测试失败')
  } finally {
    service.testing = false
    await refreshData() // 无论成功失败都刷新
  }
}

// 初始化数据
onMounted(() => {
  refreshData()
})
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
}

.mb-4 {
  margin-bottom: 20px;
}

.stat-card {
  .stat-content {
    display: flex;
    align-items: center;
    padding: 10px;

    .stat-icon {
      width: 60px;
      height: 60px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 15px;
      font-size: 24px;
      color: white;

      &.total {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      }

      &.healthy {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
      }

      &.unhealthy {
        background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
      }

      &.update {
        background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
        color: #333;
      }
    }

    .stat-info {
      flex: 1;

      .stat-number {
        font-size: 24px;
        font-weight: bold;
        color: #303133;
        margin-bottom: 5px;
      }

      .stat-label {
        font-size: 14px;
        color: #909399;
      }
    }
  }
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.tool-name {
  display: flex;
  align-items: center;
  gap: 8px;
}

.api-url, .health-url {
  color: #409eff;
  cursor: pointer;
  word-break: break-all;
}

.response-time {
  color: #67c23a;
  font-weight: 500;
}

.no-response {
  color: #909399;
}

.error-message {
  color: #f56c6c;
  cursor: pointer;
}

.no-error {
  color: #909399;
}
</style> 