-- ----------------------------
-- 项目成员关联表
-- ----------------------------
DROP TABLE IF EXISTS rd_project_members;
CREATE TABLE rd_project_members (
    project_id BIGINT NOT NULL COMMENT '项目ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    role_type VARCHAR(20) NOT NULL COMMENT '成员角色类型：project_manager(项目管理员), project_member(项目成员)',
    assigned_at TIMESTAMP NULL COMMENT '分配时间',
    assigned_by BIGINT NOT NULL COMMENT '分配人ID',
    is_deleted SMALLINT DEFAULT 0 COMMENT '删除标志（0代表存在 1代表删除）',
    PRIMARY KEY (project_id, user_id)
);

-- 添加索引
CREATE INDEX idx_project_members_user_id ON rd_project_members(user_id);
CREATE INDEX idx_project_members_role_type ON rd_project_members(role_type);
CREATE INDEX idx_project_members_assigned_at ON rd_project_members(assigned_at);

-- 添加外键约束（如果需要的话）
-- ALTER TABLE rd_project_members ADD CONSTRAINT fk_project_members_project_id 
--     FOREIGN KEY (project_id) REFERENCES rd_projects(project_id);
-- ALTER TABLE rd_project_members ADD CONSTRAINT fk_project_members_user_id 
--     FOREIGN KEY (user_id) REFERENCES sys_user(user_id);
-- ALTER TABLE rd_project_members ADD CONSTRAINT fk_project_members_assigned_by 
--     FOREIGN KEY (assigned_by) REFERENCES sys_user(user_id);

-- 添加表注释
COMMENT ON TABLE rd_project_members IS '项目成员关联表';
COMMENT ON COLUMN rd_project_members.project_id IS '项目ID';
COMMENT ON COLUMN rd_project_members.user_id IS '用户ID';
COMMENT ON COLUMN rd_project_members.role_type IS '成员角色类型：project_manager(项目管理员), project_member(项目成员)';
COMMENT ON COLUMN rd_project_members.assigned_at IS '分配时间';
COMMENT ON COLUMN rd_project_members.assigned_by IS '分配人ID';
COMMENT ON COLUMN rd_project_members.is_deleted IS '删除标志（0代表存在 1代表删除）';
